#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebSocket连接管理 - 支持流式内容传输
"""
import json
import logging
import asyncio
from typing import Dict, List, Optional, Set
from fastapi import WebSocket, WebSocketDisconnect, APIRouter
from uuid import UUID
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

websocket_router = APIRouter()


class MessageType(Enum):
    """WebSocket消息类型"""
    # 原有消息类型
    PING = "ping"
    PONG = "pong"
    TASK_UPDATE = "task_update"
    TASK_CANCEL_RECEIVED = "task_cancel_received"

    # 新增流式传输消息类型
    CONTENT_STREAMING = "content_streaming"  # 实时内容流
    CONTENT_CHUNK = "content_chunk"          # 内容片段
    CONTENT_COMPLETE = "content_complete"    # 内容完成
    STAGE_START = "stage_start"              # 阶段开始
    STAGE_COMPLETE = "stage_complete"        # 阶段完成
    GENERATION_COMPLETE = "generation_complete"  # 整个生成任务完成
    ERROR_RECOVERY = "error_recovery"        # 错误恢复
    CONNECTION_STATUS = "connection_status"   # 连接状态


class StreamingState(Enum):
    """流式传输状态"""
    IDLE = "idle"
    STREAMING = "streaming"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class ConnectionManager:
    """WebSocket连接管理器 - 支持流式内容传输"""

    def __init__(self):
        # 存储活跃连接：{task_id: [websocket1, websocket2, ...]}
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # 存储连接对应的任务ID：{websocket: task_id}
        self.connection_tasks: Dict[WebSocket, str] = {}
        # 存储每个任务的流式传输状态
        self.streaming_states: Dict[str, StreamingState] = {}
        # 存储每个任务的内容缓冲区（用于错误恢复）
        self.content_buffers: Dict[str, Dict[str, str]] = {}
        # 存储连接的健康状态
        self.connection_health: Dict[WebSocket, bool] = {}
    
    async def connect(self, websocket: WebSocket, task_id: str):
        """接受WebSocket连接"""
        await websocket.accept()

        if task_id not in self.active_connections:
            self.active_connections[task_id] = []
            self.streaming_states[task_id] = StreamingState.IDLE
            self.content_buffers[task_id] = {}

        self.active_connections[task_id].append(websocket)
        self.connection_tasks[websocket] = task_id
        self.connection_health[websocket] = True

        # 发送连接状态确认
        await self.send_personal_message({
            "type": MessageType.CONNECTION_STATUS.value,
            "status": "connected",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }, websocket)

        logger.info(f"WebSocket connected for task {task_id}")
    
    def disconnect(self, websocket: WebSocket):
        """断开WebSocket连接"""
        if websocket in self.connection_tasks:
            task_id = self.connection_tasks[websocket]

            # 从任务连接列表中移除
            if task_id in self.active_connections:
                if websocket in self.active_connections[task_id]:
                    self.active_connections[task_id].remove(websocket)

                # 如果该任务没有其他连接，清理相关状态
                if not self.active_connections[task_id]:
                    del self.active_connections[task_id]
                    # 清理流式传输状态（但保留内容缓冲区用于恢复）
                    if task_id in self.streaming_states:
                        self.streaming_states[task_id] = StreamingState.IDLE

            # 从连接任务映射中移除
            del self.connection_tasks[websocket]

            # 清理连接健康状态
            if websocket in self.connection_health:
                del self.connection_health[websocket]

            logger.info(f"WebSocket disconnected for task {task_id}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """发送个人消息"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"Failed to send personal message: {e}")
    
    async def broadcast_to_task(self, task_id: str, message: dict):
        """向特定任务的所有连接广播消息"""
        if task_id in self.active_connections:
            # 复制连接列表，避免在迭代过程中修改
            connections = self.active_connections[task_id][:]
            
            for connection in connections:
                try:
                    await connection.send_text(json.dumps(message, ensure_ascii=False))
                except Exception as e:
                    logger.error(f"Failed to send broadcast message to task {task_id}: {e}")
                    # 如果发送失败，断开连接
                    self.disconnect(connection)
    
    def get_task_connections_count(self, task_id: str) -> int:
        """获取特定任务的连接数量"""
        return len(self.active_connections.get(task_id, []))

    async def start_content_streaming(self, task_id: str, stage: str):
        """开始内容流式传输"""
        if task_id in self.streaming_states:
            self.streaming_states[task_id] = StreamingState.STREAMING

        # 初始化数据库流式会话
        try:
            from app.core.database import AsyncSessionLocal
            from app.services.streaming_content_service import StreamingContentService

            async with AsyncSessionLocal() as session:
                streaming_service = StreamingContentService(session)
                await streaming_service.start_streaming_session(task_id, stage)
        except Exception as e:
            logger.warning(f"Failed to start database streaming session: {e}")

        message = {
            "type": MessageType.STAGE_START.value,
            "task_id": task_id,
            "stage": stage,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_task(task_id, message)
        logger.info(f"Started content streaming for task {task_id}, stage {stage}")

    async def send_content_chunk(self, task_id: str, stage: str, chunk: str,
                               total_length: int = 0, chunk_index: int = 0):
        """发送内容片段并保存到数据库"""
        if task_id not in self.streaming_states:
            return

        # 缓存内容片段用于错误恢复
        if stage not in self.content_buffers[task_id]:
            self.content_buffers[task_id][stage] = ""
        self.content_buffers[task_id][stage] += chunk

        # 保存到数据库（异步，不阻塞WebSocket发送）
        try:
            from app.core.database import AsyncSessionLocal
            from app.services.streaming_content_service import StreamingContentService

            async with AsyncSessionLocal() as session:
                streaming_service = StreamingContentService(session)
                await streaming_service.save_content_chunk(
                    task_id, stage, chunk, chunk_index, total_length
                )
        except Exception as e:
            logger.warning(f"Failed to save content chunk to database: {e}")

        message = {
            "type": MessageType.CONTENT_CHUNK.value,
            "task_id": task_id,
            "stage": stage,
            "chunk": chunk,
            "chunk_index": chunk_index,
            "total_length": total_length,
            "timestamp": datetime.now().isoformat()
        }

        await self.broadcast_to_task_with_retry(task_id, message)

    async def complete_content_streaming(self, task_id: str, stage: str, final_content: str = None):
        """完成内容流式传输"""
        if task_id in self.streaming_states:
            self.streaming_states[task_id] = StreamingState.COMPLETED

        # 完成数据库流式会话
        try:
            from app.core.database import AsyncSessionLocal
            from app.services.streaming_content_service import StreamingContentService

            async with AsyncSessionLocal() as session:
                streaming_service = StreamingContentService(session)
                await streaming_service.complete_streaming_session(
                    task_id, stage,
                    final_content or self.content_buffers.get(task_id, {}).get(stage, "")
                )
        except Exception as e:
            logger.warning(f"Failed to complete database streaming session: {e}")

        message = {
            "type": MessageType.CONTENT_COMPLETE.value,
            "task_id": task_id,
            "stage": stage,
            "final_content": final_content or self.content_buffers.get(task_id, {}).get(stage, ""),
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_task(task_id, message)
        logger.info(f"Completed content streaming for task {task_id}, stage {stage}")

    async def broadcast_to_task_with_retry(self, task_id: str, message: dict, max_retries: int = 3):
        """带重试机制的广播消息"""
        if task_id not in self.active_connections:
            return

        connections = self.active_connections[task_id][:]
        failed_connections = []

        for connection in connections:
            success = False
            for attempt in range(max_retries):
                try:
                    await connection.send_text(json.dumps(message, ensure_ascii=False))
                    success = True
                    self.connection_health[connection] = True
                    break
                except Exception as e:
                    logger.warning(f"Failed to send message to connection (attempt {attempt + 1}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.1 * (2 ** attempt))  # 指数退避

            if not success:
                failed_connections.append(connection)
                self.connection_health[connection] = False

        # 清理失败的连接
        for connection in failed_connections:
            logger.error(f"Removing failed connection for task {task_id}")
            self.disconnect(connection)


# 全局连接管理器实例
manager = ConnectionManager()


@websocket_router.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket端点 - 用于接收任务进度更新"""
    await manager.connect(websocket, task_id)
    
    try:
        while True:
            # 保持连接活跃，接收客户端的心跳包
            data = await websocket.receive_text()
            
            # 可以处理客户端发送的消息，如心跳包、取消任务等
            try:
                message = json.loads(data)
                message_type = message.get("type")
                
                if message_type == "ping":
                    # 响应心跳包
                    await manager.send_personal_message({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }, websocket)
                elif message_type == "cancel_task":
                    # 处理取消任务请求
                    await handle_cancel_task(task_id, websocket)
                
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket: {data}")
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info(f"WebSocket disconnected for task {task_id}")
    except Exception as e:
        logger.error(f"WebSocket error for task {task_id}: {e}")
        manager.disconnect(websocket)


async def handle_cancel_task(task_id: str, websocket: WebSocket):
    """处理取消任务请求"""
    # 这里可以实现取消任务的逻辑
    # 例如：向Celery发送撤销任务的命令
    
    await manager.send_personal_message({
        "type": "task_cancel_received",
        "task_id": task_id,
        "message": "任务取消请求已接收"
    }, websocket)
    
    logger.info(f"Cancel request received for task {task_id}")


async def send_task_update(task_id: str, status: str, progress: int = 0, message: str = "",
                         data: dict = None, current_stage: str = None, detailed_progress: dict = None):
    """发送任务更新消息到WebSocket客户端"""
    from datetime import datetime, timezone

    update_message = {
        "type": "task_update",
        "task_id": task_id,
        "status": status,
        "progress": progress,
        "message": message,
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }
    
    # 添加详细的阶段信息
    if current_stage:
        update_message["current_stage"] = current_stage
    
    # 添加详细进度信息
    if detailed_progress:
        update_message["detailed_progress"] = detailed_progress
    
    if data:
        update_message["data"] = data
    
    await manager.broadcast_to_task(task_id, update_message)
    stage_info = f" - {current_stage}" if current_stage else ""
    logger.info(f"Task update sent for {task_id}: {status} ({progress}%){stage_info}")


async def send_content_stream(task_id: str, stage: str, chunk: str,
                            total_length: int = 0, chunk_index: int = 0):
    """发送流式内容片段"""
    await manager.send_content_chunk(task_id, stage, chunk, total_length, chunk_index)


async def start_streaming_stage(task_id: str, stage: str):
    """开始流式传输阶段"""
    await manager.start_content_streaming(task_id, stage)


async def complete_streaming_stage(task_id: str, stage: str, final_content: str = None):
    """完成流式传输阶段"""
    await manager.complete_content_streaming(task_id, stage, final_content)


async def send_generation_complete(task_id: str, result_data: dict = None, summary: str = ""):
    """发送生成完成消息"""
    from datetime import datetime, timezone

    message = {
        "type": MessageType.GENERATION_COMPLETE.value,
        "task_id": task_id,
        "summary": summary,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    if result_data:
        message["result"] = result_data

    await manager.broadcast_to_task(task_id, message)
    logger.info(f"Generation complete message sent for task {task_id}")


async def send_error_recovery(task_id: str, error_message: str, recovery_action: str = None):
    """发送错误恢复消息"""
    from datetime import datetime, timezone

    message = {
        "type": MessageType.ERROR_RECOVERY.value,
        "task_id": task_id,
        "error_message": error_message,
        "recovery_action": recovery_action,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    await manager.broadcast_to_task(task_id, message)


# 导出给其他模块使用的函数
__all__ = [
    "websocket_router",
    "manager",
    "send_task_update",
    "send_content_stream",
    "start_streaming_stage",
    "complete_streaming_stage",
    "send_generation_complete",
    "send_error_recovery",
    "MessageType",
    "StreamingState"
]