#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
章节模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Enum, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid

from app.core.database import Base


class ChapterStatus(str, enum.Enum):
    """章节状态枚举"""
    DRAFT = "draft"  # 草稿
    PUBLISHED = "published"  # 已发布
    ARCHIVED = "archived"  # 已归档


class Chapter(Base):
    """章节表"""
    __tablename__ = "chapters"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    novel_id = Column(UUID(as_uuid=True), ForeignKey("novels.id"), nullable=False, index=True, comment="小说ID")
    
    # 章节基本信息
    chapter_number = Column(Integer, nullable=False, comment="章节编号")
    title = Column(String(255), nullable=True, comment="章节标题")
    content = Column(Text, nullable=True, comment="章节内容")
    summary = Column(Text, nullable=True, comment="章节摘要")
    status = Column(Enum(ChapterStatus, values_callable=lambda obj: [e.value for e in obj]), default=ChapterStatus.DRAFT.value, comment="章节状态")
    
    # 统计信息
    word_count = Column(Integer, default=0, comment="字数统计")
    
    # 版本控制
    version = Column(Integer, default=1, comment="版本号")
    parent_id = Column(UUID(as_uuid=True), ForeignKey("chapters.id"), nullable=True, comment="父版本ID")
    is_active = Column(Boolean, default=True, comment="是否为当前活跃版本")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    novel = relationship("Novel", back_populates="chapters")
    parent = relationship("Chapter", remote_side=[id], backref="children")
    
    # 唯一约束：同一小说中，每个章节号只能有一个活跃版本
    __table_args__ = (
        UniqueConstraint('novel_id', 'chapter_number', 'is_active', name='uq_novel_chapter_active'),
    )

    def __repr__(self):
        return f"<Chapter(id={self.id}, novel_id={self.novel_id}, chapter_number={self.chapter_number})>" 