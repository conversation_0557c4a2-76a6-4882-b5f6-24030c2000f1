#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
小说管理API端点
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    NotFoundException, QuotaExceededException, PermissionDeniedException,
    DatabaseException, ErrorCode
)
from app.models.user import User
from app.models.novel import Novel, NovelStatus
from app.schemas.novel import (
    NovelCreate, NovelUpdate, NovelResponse, NovelListResponse
)
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=NovelResponse, summary="创建新小说")
async def create_novel(
    novel_data: NovelCreate,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    创建新的小说项目
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 检查用户配额
        if current_user.quota_used >= current_user.quota_limit:
            raise QuotaExceededException(
                message="已达到小说创建配额限制",
                error_code=ErrorCode.USER_QUOTA_EXCEEDED
            )
        
        # 创建小说
        novel = Novel(
            user_id=current_user.id,
            title=novel_data.title,
            description=novel_data.description,
            genre=novel_data.genre,
            target_length=novel_data.target_length,
            style_settings=novel_data.style_settings,
            status=novel_data.status if hasattr(novel_data, 'status') else NovelStatus.DRAFT
        )
        
        session.add(novel)
        await session.commit()
        await session.refresh(novel)
        
        # 更新用户配额
        current_user.quota_used += 1
        await session.commit()
        
        # 记录业务操作日志
        log_business_operation(
            logger=logger.logger,
            operation="create_novel",
            details={
                "novel_id": novel.id,
                "title": novel.title,
                "genre": novel.genre
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return NovelResponse.model_validate(novel)
        
    except (QuotaExceededException, PermissionDeniedException):
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"创建小说失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'create_novel'
        })
        raise DatabaseException(
            message="创建小说失败",
            details={"title": novel_data.title}
        )


@router.get("/", response_model=NovelListResponse, summary="获取用户小说列表")
async def get_user_novels(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取当前用户的小说列表
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 构建查询
        query = select(Novel).where(Novel.user_id == current_user.id)
        
        if status_filter:
            query = query.where(Novel.status == status_filter)
        
        # 获取总数
        count_query = select(Novel).where(Novel.user_id == current_user.id)
        if status_filter:
            count_query = count_query.where(Novel.status == status_filter)
        
        total_result = await session.execute(count_query)
        total = len(total_result.scalars().all())
        
        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(Novel.created_at.desc())
        result = await session.execute(query)
        novels = result.scalars().all()
        
        # 记录业务操作日志
        log_business_operation(
            logger=logger.logger,
            operation="get_user_novels",
            details={
                "total_novels": total,
                "status_filter": status_filter,
                "page": skip // limit + 1,
                "per_page": limit
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return NovelListResponse(
            novels=[NovelResponse.model_validate(novel) for novel in novels],
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
        
    except Exception as e:
        logger.error(f"获取小说列表失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_user_novels'
        })
        raise DatabaseException(
            message="获取小说列表失败",
            details={"user_id": user_id}
        )


@router.get("/{novel_id}", response_model=NovelResponse, summary="获取小说详情")
async def get_novel_detail(
    novel_id: UUID,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取指定小说的详细信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Novel).where(
            and_(Novel.id == novel_id, Novel.user_id == current_user.id)
        ).options(selectinload(Novel.chapters))
        
        result = await session.execute(query)
        novel = result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 记录业务操作日志
        log_business_operation(
            logger=logger.logger,
            operation="get_novel_detail",
            details={
                "novel_id": novel_id,
                "title": novel.title
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return NovelResponse.model_validate(novel)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取小说详情失败 - 用户ID: {user_id}, 小说ID: {novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_novel_detail'
        })
        raise DatabaseException(
            message="获取小说详情失败",
            details={"novel_id": novel_id}
        )


@router.put("/{novel_id}", response_model=NovelResponse, summary="更新小说信息")
async def update_novel(
    novel_id: UUID,
    update_data: NovelUpdate,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    更新小说信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Novel).where(
            and_(Novel.id == novel_id, Novel.user_id == current_user.id)
        )
        result = await session.execute(query)
        novel = result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 记录更新前的信息
        update_fields = list(update_data.dict(exclude_unset=True).keys())
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(novel, field, value)
        
        await session.commit()
        await session.refresh(novel)
        
        # 记录业务操作日志
        log_business_operation(
            logger=logger.logger,
            operation="update_novel",
            details={
                "novel_id": novel_id,
                "title": novel.title,
                "updated_fields": update_fields
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return NovelResponse.model_validate(novel)
        
    except NotFoundException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"更新小说失败 - 用户ID: {user_id}, 小说ID: {novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'update_novel'
        })
        raise DatabaseException(
            message="更新小说失败",
            details={"novel_id": novel_id}
        )


@router.delete("/{novel_id}", summary="删除小说")
async def delete_novel(
    novel_id: UUID,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    删除指定小说及其所有章节
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Novel).where(
            and_(Novel.id == novel_id, Novel.user_id == current_user.id)
        )
        result = await session.execute(query)
        novel = result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 记录删除前的信息
        novel_info = {
            "novel_id": novel_id,
            "title": novel.title,
            "genre": novel.genre
        }
        
        await session.delete(novel)
        await session.commit()
        
        # 更新用户配额
        current_user.quota_used = max(0, current_user.quota_used - 1)
        await session.commit()
        
        # 记录业务操作日志
        log_business_operation(
            logger=logger.logger,
            operation="delete_novel",
            details=novel_info,
            user_id=user_id,
            request_id=request_id
        )
        
        return {"message": "小说已成功删除"}
        
    except NotFoundException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"删除小说失败 - 用户ID: {user_id}, 小说ID: {novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'delete_novel'
        })
        raise DatabaseException(
            message="删除小说失败",
            details={"novel_id": novel_id}
        ) 
