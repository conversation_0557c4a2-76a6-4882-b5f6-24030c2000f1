"""
认知写作核心服务 - 基于认知科学的智能小说生成引擎
实现分层认知架构：故事DNA + 角色心智 + 世界记忆
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from dataclasses import dataclass
from enum import Enum
import json
import re

# 导入模型类型
from app.models.document import Document, DocumentType
from app.models.novel import Novel

logger = logging.getLogger(__name__)


class CognitiveState(Enum):
    """认知状态枚举"""
    UNINITIALIZED = "uninitialized"
    LOADING = "loading"
    READY = "ready"
    ERROR = "error"


@dataclass
class StoryDNA:
    """故事基因 - 包含故事的核心遗传信息"""
    theme_core: str          # 主题内核
    conflict_seed: str       # 冲突种子
    emotional_core: str      # 情感内核
    genre_signature: str     # 类型特征
    narrative_voice: str     # 叙事声音
    tension_pattern: str     # 张力模式


@dataclass
class CharacterMind:
    """角色心智模型 - 基于心智理论的角色认知系统"""
    character_id: str
    core_beliefs: Dict[str, float]     # 核心信念系统（信念-强度）
    emotional_state: Dict[str, float]  # 情感状态（情感-强度）
    motivation_hierarchy: List[str]    # 动机层次
    cognitive_biases: List[str]        # 认知偏差
    behavioral_patterns: Dict[str, str] # 行为模式
    relationship_models: Dict[str, Dict] # 对其他角色的心智模型
    memory_anchors: List[str]          # 记忆锚点


@dataclass
class WorldMemory:
    """世界记忆系统 - 分层存储世界信息"""
    semantic_memory: Dict[str, Any]    # 语义记忆（概念、规则）
    episodic_memory: List[Dict]        # 情景记忆（事件、经历）
    procedural_memory: Dict[str, str]  # 程序记忆（流程、习惯）
    working_memory: Dict[str, Any]     # 工作记忆（当前活跃信息）


class CognitiveWritingCore:
    """认知写作核心引擎"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.state = CognitiveState.UNINITIALIZED
        self.novel_id: Optional[UUID] = None
        
        # 三大认知模块
        self.story_dna: Optional[StoryDNA] = None
        self.character_minds: Dict[str, CharacterMind] = {}
        self.world_memory: Optional[WorldMemory] = None
        
        # 认知缓存
        self._context_cache: Dict[str, Any] = {}
        self._generation_history: List[Dict] = []
    
    async def initialize_from_novel(self, novel_id: UUID) -> bool:
        """从小说数据初始化认知核心"""
        try:
            self.state = CognitiveState.LOADING
            self.novel_id = novel_id
            
            # 1. 加载故事DNA
            await self._load_story_dna()
            
            # 2. 加载角色心智模型
            await self._load_character_minds()
            
            # 3. 加载世界记忆
            await self._load_world_memory()
            
            # 4. 初始化工作记忆
            await self._initialize_working_memory()
            
            self.state = CognitiveState.READY
            logger.info(f"Cognitive core initialized for novel {novel_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize cognitive core: {e}")
            self.state = CognitiveState.ERROR
            
            # 尝试回滚任何可能的数据库操作
            try:
                if hasattr(self.session, 'rollback'):
                    await self.session.rollback()
            except Exception as rollback_error:
                logger.error(f"Failed to rollback session in cognitive core: {rollback_error}")
            
            return False
    
    async def _load_story_dna(self):
        """加载故事DNA"""
        try:
            # 获取小说基本信息
            novel_query = select(Novel).where(Novel.id == self.novel_id)
            result = await self.session.execute(novel_query)
            novel = result.scalar_one_or_none()
            
            if not novel:
                raise Exception("Novel not found")
            
            # 获取架构文档
            arch_query = select(Document).where(
                and_(
                    Document.novel_id == self.novel_id,
                    Document.doc_type == DocumentType.ARCHITECTURE
                )
            )
            result = await self.session.execute(arch_query)
            arch_doc = result.scalar_one_or_none()
            
            if arch_doc:
                # 从架构文档中提取故事DNA
                self.story_dna = await self._extract_story_dna(arch_doc.content, novel)
            else:
                # 创建默认故事DNA
                self.story_dna = StoryDNA(
                    theme_core=novel.description or "未定义主题",
                    conflict_seed="待发展的冲突",
                    emotional_core="待确定的情感基调",
                    genre_signature=novel.genre or "通用",
                    narrative_voice="第三人称全知",
                    tension_pattern="渐进式"
                )
                
        except Exception as e:
            logger.error(f"Failed to load story DNA: {e}")
            raise
    
    async def _extract_story_dna(self, architecture_content: str, novel) -> StoryDNA:
        """从架构文档中提取故事DNA"""
        try:
            from app.services.llm_service import llm_service
            
            extraction_prompt = f"""请从以下小说架构中提取故事的核心DNA信息：

小说架构：
{architecture_content[:2000]}

请按以下格式提取信息：
主题内核: [核心主题的一句话概括]
冲突种子: [主要冲突的本质]
情感内核: [故事的情感基调]
类型特征: [文学类型的特点]
叙事声音: [叙事视角和语调]
张力模式: [悬念和张力的构建方式]

请用简洁的语言提取，每项不超过50字。"""
            
            response = await llm_service.stream_invoke_with_retry(extraction_prompt)
            
            # 解析响应
            dna_data = self._parse_story_dna_response(response)
            
            return StoryDNA(
                theme_core=dna_data.get("主题内核", novel.description or "未定义主题"),
                conflict_seed=dna_data.get("冲突种子", "待发展的冲突"),
                emotional_core=dna_data.get("情感内核", "待确定的情感基调"),
                genre_signature=dna_data.get("类型特征", novel.genre or "通用"),
                narrative_voice=dna_data.get("叙事声音", "第三人称全知"),
                tension_pattern=dna_data.get("张力模式", "渐进式")
            )
            
        except Exception as e:
            logger.error(f"Failed to extract story DNA: {e}")
            # 返回默认值
            return StoryDNA(
                theme_core=novel.description or "未定义主题",
                conflict_seed="待发展的冲突",
                emotional_core="待确定的情感基调",
                genre_signature=novel.genre or "通用",
                narrative_voice="第三人称全知",
                tension_pattern="渐进式"
            )
    
    def _parse_story_dna_response(self, response: str) -> Dict[str, str]:
        """解析故事DNA提取响应"""
        dna_data = {}
        lines = response.strip().split('\n')
        
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()
                if key and value:
                    dna_data[key] = value
        
        return dna_data
    
    async def _load_character_minds(self):
        """加载角色心智模型"""
        try:
            # 获取角色状态文档
            char_query = select(Document).where(
                and_(
                    Document.novel_id == self.novel_id,
                    Document.doc_type == DocumentType.CHARACTER_STATE
                )
            ).order_by(Document.updated_at.desc())
            
            result = await self.session.execute(char_query)
            char_doc = result.scalar_one_or_none()
            
            if char_doc:
                # 从角色状态文档中提取角色心智
                await self._extract_character_minds(char_doc.content)
            else:
                logger.warning("No character state document found")
                
        except Exception as e:
            logger.error(f"Failed to load character minds: {e}")
            raise
    
    async def _extract_character_minds(self, character_content: str):
        """从角色状态文档中提取心智模型"""
        try:
            from app.services.llm_service import llm_service
            
            extraction_prompt = f"""请从以下角色状态信息中提取主要角色的心智模型：

角色状态：
{character_content[:1500]}

请为每个主要角色提取以下心智信息：
1. 核心信念（3-5个关键信念及其强度1-10）
2. 情感状态（当前主要情感及强度1-10）
3. 动机层次（按重要性排序的3-5个动机）
4. 认知偏差（影响决策的偏差模式）
5. 行为模式（典型的行为反应）

请按以下格式输出：
角色名1:
核心信念: 信念1(强度), 信念2(强度)...
情感状态: 情感1(强度), 情感2(强度)...
动机层次: 动机1, 动机2, 动机3...
认知偏差: 偏差1, 偏差2...
行为模式: 模式1, 模式2..."""
            
            response = await llm_service.stream_invoke_with_retry(extraction_prompt)
            
            # 解析响应并构建心智模型
            self._parse_character_minds_response(response)
            
        except Exception as e:
            logger.error(f"Failed to extract character minds: {e}")
    
    def _parse_character_minds_response(self, response: str):
        """解析角色心智提取响应"""
        try:
            current_character = None
            current_section = None
            
            for line in response.strip().split('\n'):
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是新角色
                if line.endswith(':') and not any(keyword in line for keyword in ['核心信念', '情感状态', '动机层次', '认知偏差', '行为模式']):
                    current_character = line[:-1].strip()
                    self.character_minds[current_character] = CharacterMind(
                        character_id=current_character,
                        core_beliefs={},
                        emotional_state={},
                        motivation_hierarchy=[],
                        cognitive_biases=[],
                        behavioral_patterns={},
                        relationship_models={},
                        memory_anchors=[]
                    )
                    continue
                
                if current_character is None:
                    continue
                
                # 解析各个部分
                if line.startswith('核心信念:'):
                    beliefs_str = line[4:].strip()
                    self.character_minds[current_character].core_beliefs = self._parse_beliefs(beliefs_str)
                elif line.startswith('情感状态:'):
                    emotions_str = line[4:].strip()
                    self.character_minds[current_character].emotional_state = self._parse_emotions(emotions_str)
                elif line.startswith('动机层次:'):
                    motivations_str = line[4:].strip()
                    self.character_minds[current_character].motivation_hierarchy = [m.strip() for m in motivations_str.split(',') if m.strip()]
                elif line.startswith('认知偏差:'):
                    biases_str = line[4:].strip()
                    self.character_minds[current_character].cognitive_biases = [b.strip() for b in biases_str.split(',') if b.strip()]
                elif line.startswith('行为模式:'):
                    patterns_str = line[4:].strip()
                    patterns = [p.strip() for p in patterns_str.split(',') if p.strip()]
                    self.character_minds[current_character].behavioral_patterns = {f"pattern_{i}": p for i, p in enumerate(patterns)}
                    
        except Exception as e:
            logger.error(f"Failed to parse character minds response: {e}")
    
    def _parse_beliefs(self, beliefs_str: str) -> Dict[str, float]:
        """解析信念字符串"""
        beliefs = {}
        items = beliefs_str.split(',')
        for item in items:
            item = item.strip()
            if '(' in item and ')' in item:
                belief = item.split('(')[0].strip()
                try:
                    strength = float(re.search(r'\((\d+(?:\.\d+)?)\)', item).group(1))
                    beliefs[belief] = strength
                except:
                    beliefs[belief] = 5.0  # 默认强度
            else:
                beliefs[item] = 5.0  # 默认强度
        return beliefs
    
    def _parse_emotions(self, emotions_str: str) -> Dict[str, float]:
        """解析情感字符串"""
        emotions = {}
        items = emotions_str.split(',')
        for item in items:
            item = item.strip()
            if '(' in item and ')' in item:
                emotion = item.split('(')[0].strip()
                try:
                    intensity = float(re.search(r'\((\d+(?:\.\d+)?)\)', item).group(1))
                    emotions[emotion] = intensity
                except:
                    emotions[emotion] = 5.0  # 默认强度
            else:
                emotions[item] = 5.0  # 默认强度
        return emotions
    
    async def _load_world_memory(self):
        """加载世界记忆系统"""
        try:
            # 获取世界设定文档
            world_query = select(Document).where(
                and_(
                    Document.novel_id == self.novel_id,
                    Document.doc_type == DocumentType.WORLD_BUILDING
                )
            )
            result = await self.session.execute(world_query)
            world_doc = result.scalar_one_or_none()
            
            # 获取架构文档作为补充
            arch_query = select(Document).where(
                and_(
                    Document.novel_id == self.novel_id,
                    Document.doc_type == DocumentType.ARCHITECTURE
                )
            )
            result = await self.session.execute(arch_query)
            arch_doc = result.scalar_one_or_none()
            
            # 构建世界记忆
            semantic_memory = {}
            episodic_memory = []
            procedural_memory = {}
            
            if world_doc:
                semantic_memory["world_setting"] = world_doc.content
            
            if arch_doc:
                semantic_memory["story_architecture"] = arch_doc.content
                # 从架构中提取程序记忆
                procedural_memory = await self._extract_procedural_memory(arch_doc.content)
            
            self.world_memory = WorldMemory(
                semantic_memory=semantic_memory,
                episodic_memory=episodic_memory,
                procedural_memory=procedural_memory,
                working_memory={}
            )
            
        except Exception as e:
            logger.error(f"Failed to load world memory: {e}")
            # 创建默认世界记忆
            self.world_memory = WorldMemory(
                semantic_memory={},
                episodic_memory=[],
                procedural_memory={},
                working_memory={}
            )
    
    async def _extract_procedural_memory(self, architecture_content: str) -> Dict[str, str]:
        """从架构中提取程序记忆（流程、规则、习惯）"""
        try:
            from app.services.llm_service import llm_service
            
            extraction_prompt = f"""请从以下小说架构中提取程序性知识（规则、流程、习惯）：

架构内容：
{architecture_content[:1000]}

请提取以下类型的程序性知识：
1. 世界运行规则
2. 角色行为习惯
3. 社会流程制度
4. 故事发展模式

请按格式输出：
规则类型: 具体内容"""
            
            response = await llm_service.stream_invoke_with_retry(extraction_prompt)
            
            # 解析响应
            procedural_memory = {}
            for line in response.strip().split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip()
                    if key and value:
                        procedural_memory[key] = value
            
            return procedural_memory
            
        except Exception as e:
            logger.error(f"Failed to extract procedural memory: {e}")
            return {}
    
    async def _initialize_working_memory(self):
        """初始化工作记忆"""
        if not self.world_memory:
            return
        
        # 将关键信息加载到工作记忆
        self.world_memory.working_memory = {
            "current_story_phase": "初始化",
            "active_characters": list(self.character_minds.keys()),
            "recent_events": [],
            "pending_conflicts": [],
            "emotional_tone": self.story_dna.emotional_core if self.story_dna else "中性"
        }
    
    async def generate_context_for_chapter(self, chapter_number: int, 
                                         user_guidance: str = "") -> Dict[str, Any]:
        """为章节生成认知上下文"""
        if self.state != CognitiveState.READY:
            logger.warning("Cognitive core not ready")
            return {}
        
        try:
            context = {
                "story_dna": self._story_dna_to_dict(),
                "character_insights": await self._generate_character_insights(chapter_number),
                "world_context": self._get_world_context(),
                "cognitive_guidance": await self._generate_cognitive_guidance(chapter_number, user_guidance),
                "emotional_trajectory": await self._calculate_emotional_trajectory(chapter_number),
                "narrative_focus": await self._determine_narrative_focus(chapter_number)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to generate context for chapter {chapter_number}: {e}")
            return {}
    
    def _story_dna_to_dict(self) -> Dict[str, str]:
        """将故事DNA转换为字典"""
        if not self.story_dna:
            return {}
        
        return {
            "theme_core": self.story_dna.theme_core,
            "conflict_seed": self.story_dna.conflict_seed,
            "emotional_core": self.story_dna.emotional_core,
            "genre_signature": self.story_dna.genre_signature,
            "narrative_voice": self.story_dna.narrative_voice,
            "tension_pattern": self.story_dna.tension_pattern
        }
    
    async def _generate_character_insights(self, chapter_number: int) -> Dict[str, Any]:
        """生成角色洞察"""
        insights = {}
        
        for char_name, mind in self.character_minds.items():
            insights[char_name] = {
                "dominant_emotion": max(mind.emotional_state.items(), key=lambda x: x[1])[0] if mind.emotional_state else "平静",
                "primary_motivation": mind.motivation_hierarchy[0] if mind.motivation_hierarchy else "未知",
                "behavioral_prediction": await self._predict_character_behavior(mind, chapter_number),
                "relationship_dynamics": mind.relationship_models
            }
        
        return insights
    
    async def _predict_character_behavior(self, mind: CharacterMind, chapter_number: int) -> str:
        """预测角色行为"""
        try:
            # 基于心智模型预测行为
            dominant_beliefs = sorted(mind.core_beliefs.items(), key=lambda x: x[1], reverse=True)[:2]
            dominant_emotions = sorted(mind.emotional_state.items(), key=lambda x: x[1], reverse=True)[:2]
            
            prediction = f"基于信念'{dominant_beliefs[0][0] if dominant_beliefs else '未知'}'和情感'{dominant_emotions[0][0] if dominant_emotions else '平静'}'，"
            
            if mind.behavioral_patterns:
                pattern = list(mind.behavioral_patterns.values())[0]
                prediction += f"可能表现出{pattern}的行为模式"
            else:
                prediction += "行为模式待观察"
            
            return prediction
            
        except Exception as e:
            logger.error(f"Failed to predict character behavior: {e}")
            return "行为预测暂不可用"
    
    def _get_world_context(self) -> Dict[str, Any]:
        """获取世界上下文"""
        if not self.world_memory:
            return {}
        
        return {
            "semantic_knowledge": list(self.world_memory.semantic_memory.keys()),
            "recent_events": self.world_memory.episodic_memory[-5:] if self.world_memory.episodic_memory else [],
            "active_rules": list(self.world_memory.procedural_memory.keys()),
            "working_focus": self.world_memory.working_memory
        }
    
    async def _generate_cognitive_guidance(self, chapter_number: int, user_guidance: str) -> str:
        """生成认知指导"""
        try:
            from app.services.llm_service import llm_service
            
            guidance_prompt = f"""基于认知写作理论，为第{chapter_number}章提供写作指导：

故事DNA：
- 主题内核：{self.story_dna.theme_core if self.story_dna else '未定义'}
- 冲突种子：{self.story_dna.conflict_seed if self.story_dna else '未定义'}
- 情感内核：{self.story_dna.emotional_core if self.story_dna else '未定义'}

角色心智状态：
{self._summarize_character_minds()}

用户指导：{user_guidance}

请提供认知层面的写作建议，包括：
1. 认知负荷平衡建议
2. 角色心智一致性要求
3. 情感认知引导
4. 叙事认知优化

请用200字以内简洁回答："""
            
            response = await llm_service.stream_invoke_with_retry(guidance_prompt)
            return response.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate cognitive guidance: {e}")
            return "基于当前认知状态，保持角色行为一致性和情感逻辑连贯性。"
    
    def _summarize_character_minds(self) -> str:
        """总结角色心智状态"""
        summary = []
        for char_name, mind in self.character_minds.items():
            dominant_emotion = max(mind.emotional_state.items(), key=lambda x: x[1])[0] if mind.emotional_state else "平静"
            primary_motivation = mind.motivation_hierarchy[0] if mind.motivation_hierarchy else "未知"
            summary.append(f"{char_name}: {dominant_emotion}情绪，{primary_motivation}动机")
        
        return "; ".join(summary[:3])  # 只显示前3个角色
    
    async def _calculate_emotional_trajectory(self, chapter_number: int) -> Dict[str, Any]:
        """计算情感轨迹"""
        try:
            # 基于故事DNA和角色心智计算情感发展轨迹
            base_emotion = self.story_dna.emotional_core if self.story_dna else "中性"
            
            # 计算角色情感平均值
            character_emotions = []
            for mind in self.character_minds.values():
                if mind.emotional_state:
                    avg_intensity = sum(mind.emotional_state.values()) / len(mind.emotional_state)
                    character_emotions.append(avg_intensity)
            
            avg_character_emotion = sum(character_emotions) / len(character_emotions) if character_emotions else 5.0
            
            return {
                "base_emotional_tone": base_emotion,
                "character_emotional_intensity": avg_character_emotion,
                "recommended_emotional_direction": "渐进上升" if avg_character_emotion < 6 else "适当缓解",
                "emotional_balance_score": min(10, max(1, avg_character_emotion))
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate emotional trajectory: {e}")
            return {
                "base_emotional_tone": "中性",
                "character_emotional_intensity": 5.0,
                "recommended_emotional_direction": "保持平衡",
                "emotional_balance_score": 5.0
            }
    
    async def _determine_narrative_focus(self, chapter_number: int) -> Dict[str, str]:
        """确定叙事焦点"""
        try:
            # 基于章节位置和认知状态确定叙事焦点
            if chapter_number <= 3:
                focus_type = "世界建构"
                focus_detail = "重点建立世界观和角色关系"
            elif chapter_number <= 10:
                focus_type = "角色发展"
                focus_detail = "深化角色心智和动机"
            elif chapter_number <= 20:
                focus_type = "冲突推进"
                focus_detail = "发展主要冲突和张力"
            else:
                focus_type = "情感升华"
                focus_detail = "处理情感高潮和价值确认"
            
            return {
                "focus_type": focus_type,
                "focus_detail": focus_detail,
                "cognitive_priority": "保持认知一致性",
                "narrative_strategy": self.story_dna.tension_pattern if self.story_dna else "渐进式"
            }
            
        except Exception as e:
            logger.error(f"Failed to determine narrative focus: {e}")
            return {
                "focus_type": "平衡发展",
                "focus_detail": "均衡推进各个方面",
                "cognitive_priority": "保持基本一致性",
                "narrative_strategy": "渐进式"
            }
    
    async def update_cognitive_state(self, chapter_content: str, chapter_number: int):
        """更新认知状态"""
        try:
            if self.state != CognitiveState.READY:
                return
            
            # 更新工作记忆
            if self.world_memory:
                self.world_memory.working_memory["current_chapter"] = chapter_number
                self.world_memory.working_memory["recent_events"].append({
                    "chapter": chapter_number,
                    "summary": chapter_content[:200] + "..." if len(chapter_content) > 200 else chapter_content,
                    "timestamp": "now"
                })
                
                # 保持工作记忆的容量限制
                if len(self.world_memory.working_memory["recent_events"]) > 5:
                    self.world_memory.working_memory["recent_events"] = self.world_memory.working_memory["recent_events"][-5:]
            
            # 更新角色情感状态（基于章节内容分析）
            await self._update_character_emotions_from_content(chapter_content)
            
            logger.info(f"Updated cognitive state for chapter {chapter_number}")
            
        except Exception as e:
            logger.error(f"Failed to update cognitive state: {e}")
    
    async def _update_character_emotions_from_content(self, chapter_content: str):
        """从章节内容中更新角色情感状态"""
        try:
            from app.services.llm_service import llm_service
            
            if not self.character_minds:
                return
            
            character_names = list(self.character_minds.keys())
            
            emotion_analysis_prompt = f"""分析以下章节内容中角色的情感变化：

章节内容：
{chapter_content[:1000]}

角色列表：{', '.join(character_names)}

请为每个出现的角色评估其情感状态变化（1-10分）：
格式：角色名: 情感1(强度), 情感2(强度)

只分析明确出现的角色。"""
            
            response = await llm_service.stream_invoke_with_retry(emotion_analysis_prompt)
            
            # 解析响应并更新角色情感
            for line in response.strip().split('\n'):
                if ':' in line:
                    char_name = line.split(':')[0].strip()
                    if char_name in self.character_minds:
                        emotions_str = line.split(':', 1)[1].strip()
                        new_emotions = self._parse_emotions(emotions_str)
                        
                        # 更新情感状态（加权平均）
                        for emotion, intensity in new_emotions.items():
                            if emotion in self.character_minds[char_name].emotional_state:
                                # 加权平均：70%旧状态 + 30%新状态
                                old_intensity = self.character_minds[char_name].emotional_state[emotion]
                                self.character_minds[char_name].emotional_state[emotion] = old_intensity * 0.7 + intensity * 0.3
                            else:
                                self.character_minds[char_name].emotional_state[emotion] = intensity
                        
        except Exception as e:
            logger.error(f"Failed to update character emotions from content: {e}")
    
    def get_cognitive_summary(self) -> Dict[str, Any]:
        """获取认知状态摘要"""
        if self.state != CognitiveState.READY:
            return {"status": "not_ready"}
        
        return {
            "status": "ready",
            "story_dna": self._story_dna_to_dict(),
            "character_count": len(self.character_minds),
            "character_names": list(self.character_minds.keys()),
            "world_memory_items": len(self.world_memory.semantic_memory) if self.world_memory else 0,
            "working_memory_active": bool(self.world_memory and self.world_memory.working_memory),
            "generation_history_count": len(self._generation_history)
        } 