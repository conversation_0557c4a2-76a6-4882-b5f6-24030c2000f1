#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端口清理功能测试脚本
用于测试VSCode调试脚本的端口清理功能
"""
import socket
import time
import threading
from start_vscode_debug import VSCodeDebugManager


def create_dummy_server(port):
    """创建一个占用指定端口的虚拟服务器"""
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        server_socket.bind(('127.0.0.1', port))
        server_socket.listen(1)
        print(f"虚拟服务器已启动，占用端口 {port}")
        
        # 保持服务器运行
        while True:
            try:
                client_socket, address = server_socket.accept()
                client_socket.close()
            except:
                break
                
    except Exception as e:
        print(f"虚拟服务器启动失败: {e}")
    finally:
        server_socket.close()
        print(f"虚拟服务器已关闭，释放端口 {port}")


def test_port_cleanup():
    """测试端口清理功能"""
    print("🧪 开始测试端口清理功能")
    print("=" * 40)
    
    # 创建管理器实例
    manager = VSCodeDebugManager(auto_kill_port=True)
    
    # 测试端口
    test_port = 8000
    
    # 1. 测试端口可用性检查
    print(f"\n1. 测试端口 {test_port} 可用性检查...")
    is_available = manager.check_port_available(test_port)
    print(f"端口 {test_port} 可用: {is_available}")
    
    if is_available:
        print(f"✅ 端口 {test_port} 当前可用")
    else:
        print(f"⚠️ 端口 {test_port} 当前被占用")
        
        # 2. 测试查找占用进程
        print(f"\n2. 查找占用端口 {test_port} 的进程...")
        pid = manager.find_process_using_port(test_port)
        if pid:
            print(f"找到占用进程 PID: {pid}")
        else:
            print("未找到占用进程")
    
    # 3. 创建虚拟服务器占用端口
    print(f"\n3. 创建虚拟服务器占用端口 {test_port}...")
    server_thread = threading.Thread(target=create_dummy_server, args=(test_port,), daemon=True)
    server_thread.start()
    time.sleep(2)  # 等待服务器启动
    
    # 4. 再次检查端口可用性
    print(f"\n4. 再次检查端口 {test_port} 可用性...")
    is_available = manager.check_port_available(test_port)
    print(f"端口 {test_port} 可用: {is_available}")
    
    if not is_available:
        print(f"✅ 端口 {test_port} 现在被占用（符合预期）")
        
        # 5. 测试端口清理
        print(f"\n5. 测试端口清理功能...")
        cleanup_success = manager.cleanup_port(test_port)
        print(f"端口清理结果: {cleanup_success}")
        
        if cleanup_success:
            print(f"✅ 端口 {test_port} 清理成功")
        else:
            print(f"❌ 端口 {test_port} 清理失败")
            
        # 6. 最终检查端口状态
        print(f"\n6. 最终检查端口 {test_port} 状态...")
        is_available = manager.check_port_available(test_port)
        print(f"端口 {test_port} 可用: {is_available}")
        
        if is_available:
            print(f"✅ 端口 {test_port} 现在可用")
        else:
            print(f"❌ 端口 {test_port} 仍被占用")
    
    print("\n" + "=" * 40)
    print("🧪 端口清理功能测试完成")


if __name__ == "__main__":
    test_port_cleanup()
