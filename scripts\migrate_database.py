#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 应用Novel模型字段更新
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import AsyncSessionLocal
from app.core.logging_config import get_logger

logger = get_logger(__name__)


async def migrate_novel_fields():
    """迁移Novel表，添加新字段"""
    async with AsyncSessionLocal() as session:
        try:
            logger.info("开始迁移Novel表字段...")
            
            # 检查字段是否已存在
            check_description = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='novels' AND column_name='description'
            """)
            
            result = await session.execute(check_description)
            if not result.scalar():
                # 添加description字段
                await session.execute(text(
                    "ALTER TABLE novels ADD COLUMN description TEXT"
                ))
                logger.info("✅ 添加description字段")
            else:
                logger.info("ℹ️ description字段已存在")
            
            # 检查target_length字段
            check_target_length = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='novels' AND column_name='target_length'
            """)
            
            result = await session.execute(check_target_length)
            if not result.scalar():
                # 添加target_length字段
                await session.execute(text(
                    "ALTER TABLE novels ADD COLUMN target_length INTEGER"
                ))
                logger.info("✅ 添加target_length字段")
            else:
                logger.info("ℹ️ target_length字段已存在")
            
            # 检查style_settings字段
            check_style_settings = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='novels' AND column_name='style_settings'
            """)
            
            result = await session.execute(check_style_settings)
            if not result.scalar():
                # 添加style_settings字段
                await session.execute(text(
                    "ALTER TABLE novels ADD COLUMN style_settings JSONB"
                ))
                logger.info("✅ 添加style_settings字段")
            else:
                logger.info("ℹ️ style_settings字段已存在")
            
            await session.commit()
            logger.info("🎉 Novel表字段迁移完成")
            
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 迁移失败: {e}")
            raise


async def migrate_existing_data():
    """迁移现有数据，设置默认值"""
    async with AsyncSessionLocal() as session:
        try:
            logger.info("开始迁移现有数据...")
            
            # 为空的description设置默认值
            await session.execute(text("""
                UPDATE novels 
                SET description = COALESCE(description, '由AI智能生成的小说') 
                WHERE description IS NULL
            """))
            
            # 为空的target_length设置默认值（基于现有总字数或默认值）
            await session.execute(text("""
                UPDATE novels 
                SET target_length = COALESCE(target_length, 
                    CASE 
                        WHEN total_words > 0 THEN total_words 
                        ELSE 50000 
                    END
                ) 
                WHERE target_length IS NULL
            """))
            
            # 为空的style_settings设置默认值
            await session.execute(text("""
                UPDATE novels 
                SET style_settings = COALESCE(style_settings, '{"style": "细腻温馨", "pace": "medium"}') 
                WHERE style_settings IS NULL
            """))
            
            await session.commit()
            logger.info("🎉 现有数据迁移完成")
            
        except Exception as e:
            await session.rollback()
            logger.error(f"❌ 数据迁移失败: {e}")
            raise


async def verify_migration():
    """验证迁移结果"""
    async with AsyncSessionLocal() as session:
        try:
            logger.info("验证迁移结果...")
            
            # 检查字段是否存在
            result = await session.execute(text("""
                SELECT 
                    column_name,
                    data_type,
                    is_nullable
                FROM information_schema.columns 
                WHERE table_name='novels' 
                    AND column_name IN ('description', 'target_length', 'style_settings')
                ORDER BY column_name
            """))
            
            columns = result.fetchall()
            for column in columns:
                logger.info(f"✅ {column.column_name}: {column.data_type} (nullable: {column.is_nullable})")
            
            # 检查数据
            result = await session.execute(text("""
                SELECT 
                    COUNT(*) as total_novels,
                    COUNT(description) as novels_with_description,
                    COUNT(target_length) as novels_with_target_length,
                    COUNT(style_settings) as novels_with_style_settings
                FROM novels
            """))
            
            stats = result.fetchone()
            logger.info(f"📊 统计信息:")
            logger.info(f"   - 总小说数: {stats.total_novels}")
            logger.info(f"   - 有描述的小说: {stats.novels_with_description}")
            logger.info(f"   - 有目标字数的小说: {stats.novels_with_target_length}")
            logger.info(f"   - 有风格设置的小说: {stats.novels_with_style_settings}")
            
        except Exception as e:
            logger.error(f"❌ 验证失败: {e}")
            raise


async def main():
    """主函数"""
    try:
        logger.info("🚀 开始数据库迁移...")
        
        # 1. 迁移表结构
        await migrate_novel_fields()
        
        # 2. 迁移现有数据
        await migrate_existing_data()
        
        # 3. 验证迁移结果
        await verify_migration()
        
        logger.info("🎉 数据库迁移完成！")
        
    except Exception as e:
        logger.error(f"💥 迁移过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 