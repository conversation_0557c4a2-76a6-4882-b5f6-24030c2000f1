# AI小说生成器 - 后端架构说明文件

## 📋 系统定位
基于FastAPI的现代化AI小说生成后端系统，专为微信小程序提供智能长篇小说创作服务。采用认知写作学理论，实现四步渐进式小说创作流程。

---

## 🏗️ 技术架构层次

### 1. **应用框架层**
```
FastAPI + Uvicorn + SQLAlchemy 2.0 (异步)
├── Python 3.8+
├── Pydantic 数据验证
├── JWT 认证
└── WebSocket 实时通信
```

### 2. **中间件栈** (按执行顺序)
```python
# 在 app/main.py 中按此顺序配置
ErrorHandlingMiddleware       # 最外层 - 全局异常处理
SecurityMiddleware           # 安全头设置 (X-Content-Type-Options, X-Frame-Options等)
RateLimitMiddleware         # API限流 (内存限流，100req/60s)
ResponseFormatterMiddleware  # 统一响应格式 (添加success/timestamp/request_id)
RequestContextMiddleware     # 请求上下文 (生成request_id, 记录API调用)
CORSMiddleware              # 跨域处理 (自定义实现)
```

### 3. **数据存储层**
```
PostgreSQL 15+ (主数据库)
├── 用户数据
├── 小说内容
├── 生成任务状态
└── 系统配置

Redis 7+ (缓存/队列)
├── 会话缓存
├── 任务队列 (Celery)
├── 实时数据
└── 临时存储

ChromaDB (向量数据库)
├── 语义检索
├── 内容相似度
└── 智能推荐
```

---

## 📁 项目目录结构

```
app/
├── api/                    # API层
│   └── api_v1/
│       ├── api.py         # 路由汇总
│       └── endpoints/     # 具体端点
│           ├── auth.py         # 认证
│           ├── users.py        # 用户管理  
│           ├── novels.py       # 小说管理
│           ├── chapters.py     # 章节管理
│           ├── generation.py   # AI生成
│           ├── tasks.py        # 任务管理
│           └── llm_config.py   # LLM配置
├── core/                   # 核心模块
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   ├── exceptions.py      # 统一异常
│   ├── logging_config.py  # 日志配置
│   ├── middleware.py      # 中间件
│   ├── websocket.py       # WebSocket
│   └── celery_app.py      # 任务队列
├── models/                 # 数据模型
│   ├── user.py            # 用户模型
│   ├── novel.py           # 小说模型
│   ├── chapter.py         # 章节模型
│   ├── generation_task.py # 任务模型
│   ├── generation_state.py# 生成状态
│   ├── prompt_template.py # 提示词模板
│   └── llm_config.py      # LLM配置
├── schemas/               # 数据传输对象
│   ├── auth.py           # 认证相关
│   ├── novel.py          # 小说相关
│   ├── chapter.py        # 章节相关
│   ├── generation.py     # 生成相关
│   └── task.py           # 任务相关
├── services/              # 业务服务层
│   ├── auth_service.py        # 认证服务
│   ├── llm_service.py         # LLM服务
│   ├── llm_config_service.py  # LLM配置服务
│   ├── prompt_service.py      # 提示词服务
│   ├── generation_tasks.py    # 生成任务引擎
│   ├── cognitive_core.py      # 认知核心
│   ├── vector_service.py      # 向量服务
│   ├── summary_service.py     # 摘要服务
│   └── quality_monitor.py     # 质量监控
├── tasks/                 # Celery任务
│   └── generation_tasks.py    # 生成任务
└── main.py               # 应用入口
```

---

## 🗄️ 数据库设计

### 核心表结构
```sql
-- 用户表
users (
    id: UUID PRIMARY KEY,
    wx_openid: VARCHAR(128) UNIQUE,
    nickname: VARCHAR(255),
    avatar_url: VARCHAR(500),
    quota_used: INTEGER DEFAULT 0,
    quota_limit: INTEGER DEFAULT 10,
    is_vip: BOOLEAN DEFAULT FALSE,
    settings: JSON
)

-- 小说表  
novels (
    id: UUID PRIMARY KEY,
    user_id: UUID -> users.id,
    title: VARCHAR(255),
    description: TEXT,
    genre: VARCHAR(50),
    target_length: INTEGER,
    style_settings: JSON,
    status: ENUM('draft','generating','completed','paused'),
    chapter_count: INTEGER DEFAULT 0,
    total_words: INTEGER DEFAULT 0
)

-- 章节表
chapters (
    id: UUID PRIMARY KEY,
    novel_id: UUID -> novels.id,
    chapter_number: INTEGER,
    title: VARCHAR(255),
    content: TEXT,
    summary: TEXT,
    status: ENUM('draft','published','archived'),
    word_count: INTEGER DEFAULT 0,
    version: INTEGER DEFAULT 1,
    parent_id: UUID -> chapters.id,
    is_active: BOOLEAN DEFAULT TRUE
)

-- 生成任务表
generation_tasks (
    id: UUID PRIMARY KEY,
    task_id: VARCHAR(255) UNIQUE,
    user_id: UUID -> users.id,
    novel_id: UUID -> novels.id,
    task_type: ENUM('generate_architecture','generate_blueprint','generate_chapter','regenerate_paragraph','consistency_check','finalize_chapter'),
    status: ENUM('PENDING','IN_PROGRESS','SUCCESS','FAILED','CANCELLED'),
    progress: INTEGER DEFAULT 0,
    parameters: TEXT,
    result_doc_id: UUID,
    result_chapter_id: UUID,
    error_message: TEXT,
    started_at: TIMESTAMP,
    completed_at: TIMESTAMP
)



-- 文档表 (架构、蓝图、角色设定等)
documents (
    id: UUID PRIMARY KEY,
    novel_id: UUID -> novels.id,
    doc_type: ENUM('architecture','blueprint','summary','character_state','world_building','plot_outline','immediate_summary','medium_summary','global_summary','character_summary'),
    title: VARCHAR(200),
    content: TEXT,
    version: INTEGER DEFAULT 1,
    parent_id: UUID -> documents.id,
    is_active: BOOLEAN DEFAULT TRUE,
    summary_level: ENUM('immediate','medium','global','character'),
    chapter_range_start: INTEGER,
    chapter_range_end: INTEGER
)

-- 生成状态表 (断点续传)
generation_states (
    id: UUID PRIMARY KEY,
    novel_id: UUID,
    task_id: VARCHAR,
    generation_type: VARCHAR,
    current_step: VARCHAR,
    completed_steps: JSON DEFAULT '{}',
    step_progress: INTEGER DEFAULT 0,
    total_progress: INTEGER DEFAULT 0,
    is_completed: BOOLEAN DEFAULT FALSE,
    is_failed: BOOLEAN DEFAULT FALSE,
    error_message: TEXT
)

-- LLM配置表
llm_configs (
    id: UUID PRIMARY KEY,
    user_id: UUID -> users.id,
    name: VARCHAR(255),
    provider: VARCHAR(50),
    is_default: BOOLEAN DEFAULT FALSE,
    is_active: BOOLEAN DEFAULT TRUE,
    api_key: VARCHAR(500),
    base_url: VARCHAR(500),
    api_version: VARCHAR(50),
    model: VARCHAR(100),
    temperature: FLOAT DEFAULT 0.7,
    max_tokens: INTEGER DEFAULT 2048,
    top_p: FLOAT DEFAULT 1.0,
    frequency_penalty: FLOAT DEFAULT 0.0,
    presence_penalty: FLOAT DEFAULT 0.0,
    timeout: INTEGER DEFAULT 60,
    max_retries: INTEGER DEFAULT 3,
    retry_delay: FLOAT DEFAULT 1.0,
    custom_headers: JSON,
    custom_params: JSON,
    total_requests: INTEGER DEFAULT 0,
    total_tokens: INTEGER DEFAULT 0,
    last_used_at: TIMESTAMP
)

-- LLM使用日志表
llm_usage_logs (
    id: UUID PRIMARY KEY,
    user_id: UUID -> users.id,
    config_id: UUID -> llm_configs.id,
    request_id: VARCHAR(100),
    endpoint: VARCHAR(200),
    method: VARCHAR(10),
    prompt_tokens: INTEGER DEFAULT 0,
    completion_tokens: INTEGER DEFAULT 0,
    total_tokens: INTEGER DEFAULT 0,
    response_time: FLOAT,
    success: BOOLEAN DEFAULT TRUE,
    error_message: TEXT,
    estimated_cost: FLOAT
)

-- 提示词模板表
prompt_templates (
    id: UUID PRIMARY KEY,
    name: VARCHAR(100) UNIQUE,
    template: TEXT,
    description: TEXT,
    version: INTEGER DEFAULT 1,
    is_active: BOOLEAN DEFAULT TRUE
)
```

### 表关系图
```
users (1) ────┬─→ novels (N) ────┬─→ chapters (N) ────→ chapters (N) [版本关系]
              │                   ├─→ documents (N) ──→ documents (N) [版本关系]
              │                   ├─→ generation_tasks (N)
              │                   └─→ generation_states (N)
              ├─→ generation_tasks (N)
              ├─→ llm_configs (N) ──→ llm_usage_logs (N)
              └─→ llm_usage_logs (N)

prompt_templates (独立表)

注意：所有表的主键和外键现在都使用UUID类型，提供更好的分布式唯一性和安全性。
```

---

## 🚀 服务层架构

### 1. **LLM服务适配器模式**
```python
class LLMService:
    adapters = {
        "openai": OpenAIAdapter,
        "azure": AzureOpenAIAdapter, 
        "dashscope": DashScopeAdapter
    }
    
    async def invoke_with_retry(prompt, adapter_name, **kwargs)
    async def stream_generate(prompt, adapter_name, **kwargs)
```

### 2. **四步生成引擎**
```python
class CognitiveWritingCore:
    # 认知写作学四步法 (带断点续传)
    async def generate_core_seed()      # 核心种子
    async def build_character_dynamics() # 角色动力学  
    async def construct_worldview()     # 世界观构建
    async def design_plot_architecture() # 情节架构
    
    # 新增断点续传支持
    async def load_or_create_generation_state()
    async def update_generation_state()
    async def complete_generation_state()
```

### 3. **任务队列系统**
```python
# Celery异步任务 (继承CallbackTask)
@celery_app.task(bind=True, base=CallbackTask)
def generate_novel_architecture(self, task_id, user_id, novel_id, parameters)
def generate_chapter_content(self, task_id, user_id, novel_id, chapter_id, parameters)

# CallbackTask基类
class CallbackTask(Task):
    def on_success(self, retval, task_id, args, kwargs)
    def on_failure(self, exc, task_id, args, kwargs, einfo)
    def on_retry(self, exc, task_id, args, kwargs, einfo)
    def _run_async_task(self, async_func, *args, **kwargs)

# 任务状态管理
async def _update_task_status(session, task_id, status, progress, result=None, error_message=None)
```

### 4. **实时通信系统**
```python
# WebSocket管理
class ConnectionManager:
    active_connections: Dict[str, List[WebSocket]] = {}
    connection_tasks: Dict[WebSocket, str] = {}
    
    async def connect(websocket, task_id)
    async def disconnect(websocket)
    async def send_personal_message(message, websocket)
    async def broadcast_to_task(task_id, message)
```

### 5. **其他核心服务**
```python
# 提示词服务
class PromptService:
    async def format_prompt(template_name, **kwargs)
    async def get_template(name)

# 向量存储服务  
class VectorStoreService:
    async def store_document(content, metadata)
    async def similarity_search(query, k=5)

# 分层摘要服务
class HierarchicalSummaryService:
    async def update_immediate_summary()  # 最近3章
    async def update_medium_summary()     # 前10章
    async def update_global_summary()     # 全书

# 质量监控服务
class QualityMonitor:
    async def check_consistency()
    async def evaluate_quality()

# 认证服务
class AuthService:
    async def wechat_login(code, nickname, avatar_url)
    async def create_access_token(data)
    async def get_current_user(token)

# LLM配置服务
class LLMConfigService:
    async def create_config(db, user_id, config_data)
    async def get_configs(db, user_id, skip, limit, active_only)
    async def test_config(config)
```

---

## 🔧 核心配置系统

### 环境配置优先级
```python
1. 环境变量 (.env文件)
2. 默认配置 (config.py)
3. 运行时动态配置
```

### 关键配置项
```python
# 数据库
DATABASE_URL = "postgresql+asyncpg://user:pass@host:port/db"
REDIS_URL = "redis://host:port/db"

# LLM服务 (必须配置其一)
LLM_PROVIDER = "openai"  # openai/azure/dashscope
LLM_API_KEY = "your-api-key"
LLM_BASE_URL = "https://api.openai.com/v1"
LLM_MODEL = "gpt-3.5-turbo"
LLM_TEMPERATURE = 0.7
LLM_MAX_TOKENS = 2048
LLM_TIMEOUT = 600

# Azure OpenAI特殊配置
AZURE_OPENAI_ENDPOINT = "https://your-resource.openai.azure.com/"
AZURE_OPENAI_API_VERSION = "2024-02-15-preview"

# 通义千问特殊配置
DASHSCOPE_API_KEY = "your-dashscope-key"

# 微信小程序
WECHAT_APP_ID = "wx_app_id"
WECHAT_APP_SECRET = "wx_app_secret"

# JWT认证
SECRET_KEY = "32字符以上强密码"
ACCESS_TOKEN_EXPIRE_MINUTES = 11520  # 8天
```

---

## 🛡️ 安全架构

### 1. **认证授权系统**
```python
# JWT + 微信小程序双认证
class AuthService:
    async def wechat_login(code, nickname, avatar_url)
    async def create_access_token(data)
    async def get_current_user(token)
    
# 权限装饰器
@Depends(AuthService.get_current_user)
```

### 2. **数据安全**
```python
# 自动过滤敏感信息
SENSITIVE_KEYS = {
    'password', 'token', 'api_key', 'secret',
    'authorization', 'access_token', 'jwt'
}

# 用户数据隔离
WHERE user_id = current_user.id
```

### 3. **API安全**
```python
# 限流
RateLimitMiddleware(max_requests=100, window_seconds=60)

# CORS配置
BACKEND_CORS_ORIGINS = ["https://your-domain.com"]

# 可信主机
TRUSTED_HOSTS = ["localhost", "127.0.0.1", "your-domain.com"]
```

---

## 📊 监控和日志系统

### 统一日志格式
```json
{
  "timestamp": "2025-01-01T00:00:00Z",
  "level": "INFO",
  "logger": "app.api.novels",
  "request_id": "abc12345",
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "operation": "create_novel",
  "message": "小说创建成功",
  "details": {"novel_id": "550e8400-e29b-41d4-a716-446655440001", "title": "我的小说"}
}
```

### 日志文件分类
```
logs/
├── app.log      # 应用日志 (INFO级别)
├── error.log    # 错误日志 (ERROR级别)
└── access.log   # API访问日志
```

### 异常处理层次
```python
1. 业务异常 (BaseAPIException)
   ├── AuthenticationException
   ├── NotFoundException  
   ├── QuotaExceededException
   └── DatabaseException

2. 全局异常处理器
   ├── base_api_exception_handler
   ├── validation_exception_handler
   └── general_exception_handler

3. 中间件异常捕获
   └── ErrorHandlingMiddleware
```

---

## 🔄 部署架构

### 1. **开发环境**
```bash
# 最小启动
docker-compose up -d postgres redis  # 基础服务
python scripts/start_api.py          # API服务  
python scripts/start_worker_windows.py # Worker进程
```

### 2. **生产环境**
```bash
# 完整容器化
docker-compose --profile app up -d   # 完整应用栈
docker-compose --profile monitoring up -d # 添加监控
```

### 3. **服务健康检查**
```python
GET /health          # 基础健康检查
GET /             # 根路径状态
WebSocket: /ws/novels/{id} # 实时连接测试
```

---

## ⚡ 性能架构

### 1. **数据库优化**
```sql
-- 关键索引
CREATE INDEX idx_novels_user_id ON novels(user_id);
CREATE INDEX idx_chapters_novel_id ON chapters(novel_id);
CREATE INDEX idx_tasks_novel_id ON generation_tasks(novel_id);
```

### 2. **缓存策略**
```python
# Redis缓存层次
Level 1: 用户会话 (JWT token)
Level 2: 提示词模板 (热数据)
Level 3: 生成任务状态 (实时数据)
```

### 3. **异步处理**
```python
# 全异步架构
async def database_operations()  # 数据库异步
async def llm_service_calls()    # LLM服务异步
@celery_app.task               # 长任务异步
```

---

## 🎯 扩展架构

### 水平扩展能力
```
API层: 多实例 + 负载均衡
Worker层: 多Worker + 任务分发  
数据库: 读写分离 + 连接池
缓存: Redis集群
```

### 微服务拆分潜力
```
当前: 单体架构 (适合初期)
未来: 
├── 用户服务 (auth, users)
├── 内容服务 (novels, chapters)  
├── 生成服务 (generation, tasks)
└── 配置服务 (llm_config, prompts)
```

---

## 📋 技术决策记录

### 选型理由
- **FastAPI**: 高性能 + 自动文档 + 类型安全
- **SQLAlchemy 2.0**: 异步ORM + 成熟生态
- **PostgreSQL**: 事务支持 + JSON字段 + 全文检索
- **Redis**: 高性能缓存 + 消息队列
- **Celery**: 成熟的任务队列 + 分布式支持
- **ChromaDB**: 轻量级向量数据库 + 本地部署

### 架构约束
- 用户数据严格隔离 (user_id过滤)
- 所有写操作需要事务保护
- 敏感数据自动过滤日志
- API接口统一响应格式
- 异常处理层次化管理

---

## 🔑 UUID架构设计

### 1. **UUID实现细节**
```python
# 数据模型中的UUID实现
from sqlalchemy.dialects.postgresql import UUID
import uuid

class User(Base):
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
# Pydantic Schema中的UUID实现
from uuid import UUID
from pydantic import BaseModel

class UserResponse(BaseModel):
    id: UUID
    nickname: str
```

### 2. **UUID优势**
- **分布式唯一性**: 无需中心化ID生成
- **安全性**: 防止ID枚举攻击
- **可预测性**: 避免暴露业务规模
- **兼容性**: 标准RFC 4122格式
- **性能**: PostgreSQL原生UUID支持

### 3. **迁移策略**
- 采用**破坏性迁移**方式
- 重建所有表以使用UUID主键
- 保持所有外键关系完整性
- 历史数据需要重新生成(开发阶段可接受)

### 4. **API影响**
- 所有ID字段从`int`改为`UUID`
- 路径参数支持UUID格式验证
- 响应体中ID字段为UUID字符串格式
- WebSocket连接使用UUID任务标识

---

**架构版本**: v2.0 (UUID重构版)  
**最后更新**: 2025年6月19日
**技术栈版本**: Python 3.8+ / FastAPI 0.104+ / PostgreSQL 15+ / Redis 7+ 