# AI小说生成器 - Docker部署指南

## 📋 概览

本指南介绍如何使用Docker和Docker Compose部署AI小说生成器后端系统，支持多种部署模式以满足不同的使用场景。

---

## 🐳 部署模式

### 1. 仅基础服务模式（推荐开发环境）
只启动PostgreSQL和Redis，应用服务在本地运行：
```bash
# 启动基础服务
docker-compose up -d postgres redis

# 本地运行应用
novel_ai_env\Scripts\Activate.ps1
python scripts/start_api.py
python scripts/start_worker_windows.py
```

### 2. 完整容器化模式（推荐生产环境）
所有服务都在容器中运行：
```bash
# 启动完整应用堆栈
docker-compose --profile app up -d
```

### 3. 包含监控的完整模式
启动应用 + Prometheus/Grafana监控：
```bash
# 启动应用和监控服务
docker-compose --profile app --profile monitoring up -d
```

### 4. 包含向量数据库的完整模式
启动应用 + ChromaDB向量数据库：
```bash
# 启动应用和向量数据库
docker-compose --profile app --profile vector_db up -d
```

---

## 🚀 快速开始

### 1. 环境准备

确保已安装Docker和Docker Compose：
```bash
# 检查Docker版本
docker --version
docker-compose --version
```

### 2. 配置环境文件

```bash
# 复制环境配置文件
Copy-Item env.example .env

# 编辑配置文件，修改LLM_API_KEY等关键配置
```

### 3. 构建和启动服务

#### 方式一：分步启动（推荐）
```bash
# 1. 启动基础服务
docker-compose up -d postgres redis

# 2. 等待服务启动
Start-Sleep -Seconds 10

# 3. 运行数据库迁移
python scripts/migrate_database.py

# 4. 启动应用服务
docker-compose --profile app up -d api celery_worker
```

#### 方式二：一键启动
```bash
# 启动所有服务
docker-compose --profile app up -d
```

---

## 📊 服务架构

### 核心服务
- **postgres**: PostgreSQL数据库 (端口5432)
- **redis**: Redis缓存和消息队列 (端口6379)
- **api**: FastAPI应用服务 (端口8000)
- **celery_worker**: Celery异步任务处理器
- **celery_beat**: Celery定时任务调度器

### 可选服务
- **chromadb**: ChromaDB向量数据库 (端口8001)
- **prometheus**: 监控数据收集 (端口9090)
- **grafana**: 监控仪表板 (端口3000)
- **rabbitmq**: RabbitMQ消息队列 (端口5672, 管理界面15672)

---

## 🔧 配置说明

### 环境变量配置

Docker部署时，需要注意以下配置差异：

#### 本地开发配置
```bash
POSTGRES_HOST=localhost
REDIS_HOST=localhost
```

#### 容器化部署配置
```bash
POSTGRES_HOST=novel_postgres
REDIS_HOST=novel_redis
```

### Docker Compose Profile说明

- **默认**: 仅启动postgres和redis
- **app**: 启动完整应用服务
- **monitoring**: 启动监控服务
- **vector_db**: 启动向量数据库服务
- **rabbitmq**: 启动RabbitMQ（替代Redis作为消息队列）

---

## 📝 常用命令

### 服务管理
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs api
docker-compose logs celery_worker
docker-compose logs postgres

# 重启服务
docker-compose restart api
docker-compose restart celery_worker

# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### 应用管理
```bash
# 进入API容器
docker-compose exec api bash

# 进入数据库容器
docker-compose exec postgres psql -U novel_user -d ai_novel_generator

# 查看Celery Worker状态
docker-compose exec celery_worker celery -A app.core.celery_app inspect active

# 运行数据库迁移
docker-compose exec api python scripts/migrate_database.py
```

### 监控和调试
```bash
# 实时查看API日志
docker-compose logs -f api

# 查看容器资源使用
docker stats

# 清理未使用的镜像和容器
docker system prune -f
```

---

## 🔍 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 错误：端口已被占用
Error: bind: address already in use

# 解决：检查端口占用
netstat -an | findstr :8000
netstat -an | findstr :5432

# 或修改docker-compose.yml中的端口映射
```

#### 2. 数据库连接失败
```bash
# 错误：数据库连接被拒绝
sqlalchemy.exc.OperationalError: connection refused

# 解决：检查数据库服务状态
docker-compose ps postgres
docker-compose logs postgres

# 重启数据库服务
docker-compose restart postgres
```

#### 3. Celery Worker无法启动
```bash
# 错误：Celery连接失败
kombu.exceptions.OperationalError: Error connecting to broker

# 解决：检查Redis服务
docker-compose ps redis
docker-compose logs redis

# 重启Redis服务
docker-compose restart redis
```

#### 4. 镜像构建失败
```bash
# 错误：构建失败
ERROR: failed to solve: process "/bin/sh -c pip install..." didn't complete

# 解决：清理缓存重新构建
docker-compose build --no-cache api
```

### 数据持久化

所有重要数据都通过Docker卷持久化：
- `postgres_data`: PostgreSQL数据
- `redis_data`: Redis数据
- `chroma_data`: ChromaDB向量数据
- `./logs`: 应用日志
- `./uploads`: 上传文件
- `./chroma_db`: 本地向量数据库

---

## 🛠️ 开发和调试

### 开发模式部署

对于开发环境，推荐使用混合模式：
```bash
# 1. 启动基础服务
docker-compose up -d postgres redis

# 2. 本地运行应用（便于调试）
novel_ai_env\Scripts\Activate.ps1
python scripts/start_api.py
```

### 热重载配置

在开发模式下，API容器支持代码热重载：
```yaml
# docker-compose.override.yml
services:
  api:
    volumes:
      - .:/app
    environment:
      - DEBUG=true
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 调试技巧

1. **进入容器调试**：
   ```bash
   docker-compose exec api bash
   python -c "from app.core.database import engine; print('DB OK')"
   ```

2. **查看详细日志**：
   ```bash
   # 设置调试级别
   echo "LOG_LEVEL=DEBUG" >> .env
   docker-compose restart api
   ```

3. **数据库调试**：
   ```bash
   # 连接数据库
   docker-compose exec postgres psql -U novel_user -d ai_novel_generator
   \dt  # 查看表
   \d novels  # 查看表结构
   ```

---

## 📈 性能优化

### 生产环境配置

```yaml
# docker-compose.prod.yml
services:
  api:
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
  
  celery_worker:
    command: celery -A app.core.celery_app worker -l info --concurrency=8
    deploy:
      replicas: 2
```

### 监控配置

访问监控界面：
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **RabbitMQ管理界面**: http://localhost:15672 (novel_user/novel_password)

---

## 🔒 安全配置

### 生产环境安全检查清单

- [ ] 修改默认密码（数据库、Redis、RabbitMQ）
- [ ] 启用Redis密码认证
- [ ] 配置防火墙规则
- [ ] 使用HTTPS（通过反向代理）
- [ ] 定期备份数据卷
- [ ] 监控容器资源使用

### 备份和恢复

```bash
# 备份数据库
docker-compose exec postgres pg_dump -U novel_user ai_novel_generator > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U novel_user ai_novel_generator < backup.sql

# 备份数据卷
docker run --rm -v novel_postgres_data:/data -v $(pwd):/backup ubuntu tar czf /backup/postgres_backup.tar.gz /data
```

---

## 📞 获取帮助

### 日志文件位置
- API日志: `docker-compose logs api`
- Worker日志: `docker-compose logs celery_worker`
- 数据库日志: `docker-compose logs postgres`

### 健康检查
```bash
# 检查所有服务健康状态
docker-compose ps

# API健康检查
curl http://localhost:8000/health

# 数据库健康检查
docker-compose exec postgres pg_isready -U novel_user
```

---

**文档版本**: v1.0  
**最后更新**: 2025年6月19日  
**Docker Compose版本**: 3.8+ 