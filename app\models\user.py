#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用户模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.core.database import Base


class User(Base):
    """用户表"""
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    wx_openid = Column(String(128), unique=True, index=True, nullable=False, comment="微信openid")
    nickname = Column(String(255), nullable=True, comment="用户昵称")
    avatar_url = Column(String(500), nullable=True, comment="用户头像URL")
    
    # 用户配额相关
    quota_used = Column(Integer, default=0, comment="已使用配额")
    quota_limit = Column(Integer, default=10, comment="配额限制")
    is_vip = Column(<PERSON><PERSON><PERSON>, default=False, comment="是否VIP用户")
    
    # 用户设置
    settings = Column(JSON, nullable=True, comment="用户设置JSON")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    novels = relationship("Novel", back_populates="user", cascade="all, delete-orphan")
    llm_configs = relationship("LLMConfig", back_populates="user", cascade="all, delete-orphan")
    generation_tasks = relationship("GenerationTask", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(id={self.id}, nickname={self.nickname})>" 