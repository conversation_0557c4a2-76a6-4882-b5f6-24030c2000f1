#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 简化启动脚本
只启动API服务，不捕获日志输出
"""
import os
import sys
import subprocess


def main():
    """主函数"""
    print("🚀 AI小说生成器 - 简化启动")
    print("=" * 40)
    
    # 检查环境
    if not os.path.exists("app"):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', os.getcwd())
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    print(f"✅ 工作目录: {os.getcwd()}")
    print(f"✅ Python解释器: {sys.executable}")
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "app.main:app",
        "--host=0.0.0.0",
        "--port=8000",
        "--reload",
        "--log-level=info"
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print("🌐 服务将在 http://localhost:8000 启动")
    print("📚 API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 40)
    
    try:
        # 直接启动，不捕获输出
        subprocess.run(cmd, cwd=os.getcwd())
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
