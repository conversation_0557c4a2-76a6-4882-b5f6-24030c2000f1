#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查系统整体状态
"""
import requests
import redis
import time


def check_api_status():
    """检查API状态"""
    print("🔍 检查API服务状态...")
    
    try:
        # 检查健康端点
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API服务正常")
            print(f"  - 状态: {data.get('status', 'N/A')}")
            print(f"  - 时间: {data.get('timestamp', 'N/A')}")
            return True
        else:
            print(f"❌ API服务异常，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API服务不可用: {e}")
        return False


def check_redis_status():
    """检查Redis状态"""
    print("\n🔍 检查Redis状态...")
    
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        
        # 检查队列状态
        default_queue = r.llen('default')
        generation_queue = r.llen('generation')
        
        print("✅ Redis服务正常")
        print(f"  - default队列: {default_queue} 个任务")
        print(f"  - generation队列: {generation_queue} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis服务不可用: {e}")
        return False


def check_websocket_endpoint():
    """检查WebSocket端点是否可访问"""
    print("\n🔍 检查WebSocket端点...")
    
    try:
        # 尝试连接WebSocket端点（这里只是检查HTTP升级请求）
        response = requests.get("http://localhost:8000/ws/test", timeout=5)
        # WebSocket端点通常返回400或426状态码
        if response.status_code in [400, 426]:
            print("✅ WebSocket端点可访问")
            return True
        else:
            print(f"⚠️ WebSocket端点响应异常，状态码: {response.status_code}")
            return True  # 仍然认为是正常的
            
    except Exception as e:
        print(f"❌ WebSocket端点不可用: {e}")
        return False


def main():
    """主函数"""
    print("🔧 AI小说生成器 - 系统状态检查")
    print("=" * 40)
    
    # 检查各个组件
    api_ok = check_api_status()
    redis_ok = check_redis_status()
    ws_ok = check_websocket_endpoint()
    
    print("\n" + "=" * 40)
    print("📋 系统状态汇总:")
    print(f"  - API服务: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"  - Redis服务: {'✅ 正常' if redis_ok else '❌ 异常'}")
    print(f"  - WebSocket: {'✅ 正常' if ws_ok else '❌ 异常'}")
    
    if api_ok and redis_ok and ws_ok:
        print("\n🎉 系统状态良好，可以进行流式传输测试！")
        print("\n💡 建议执行:")
        print("  python run_test.py")
        print("  选择选项 2 (WebSocket模式)")
    else:
        print("\n⚠️ 系统存在问题，请检查相关服务")
        
        if not api_ok:
            print("  - 请确保API服务正在运行")
        if not redis_ok:
            print("  - 请确保Redis服务正在运行")
        if not ws_ok:
            print("  - 请检查WebSocket配置")


if __name__ == "__main__":
    main()
