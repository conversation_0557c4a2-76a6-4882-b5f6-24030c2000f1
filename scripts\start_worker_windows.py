#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows环境下启动Celery Worker的脚本
支持自动环境检查和错误恢复
"""
import os
import sys
import subprocess
import time
from pathlib import Path


def check_environment():
    """检查运行环境"""
    print("正在检查运行环境...")
    
    # 检查是否在项目根目录
    if not os.path.exists("app"):
        print("错误：请在项目根目录运行此脚本")
        return False
    
    # 检查虚拟环境
    if not os.environ.get('VIRTUAL_ENV'):
        print("警告：未检测到虚拟环境，建议先激活虚拟环境")
    
    # 检查必要的环境变量
    required_env = ['REDIS_URL', 'DATABASE_URL']
    missing_env = []
    
    for env_var in required_env:
        if not os.environ.get(env_var):
            missing_env.append(env_var)
    
    if missing_env:
        print(f"警告：缺少环境变量：{', '.join(missing_env)}")
        print("将使用默认配置")
    
    return True


def setup_environment():
    """设置环境变量"""
    # 设置Python路径
    current_dir = os.getcwd()
    python_path = os.environ.get('PYTHONPATH', '')
    if current_dir not in python_path:
        os.environ['PYTHONPATH'] = f"{current_dir};{python_path}" if python_path else current_dir
    
    # 设置默认环境变量（如果不存在）
    defaults = {
        'REDIS_URL': 'redis://localhost:6379/0',
        'DATABASE_URL': 'postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator',
        'CELERY_BROKER_URL': 'redis://localhost:6379/0',
        'CELERY_RESULT_BACKEND': 'redis://localhost:6379/0'
    }
    
    for key, value in defaults.items():
        if not os.environ.get(key):
            os.environ[key] = value
            print(f"设置默认环境变量: {key}")


def start_celery_worker():
    """启动Celery Worker"""
    print("正在启动Celery Worker...")
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "celery",
        "-A", "app.core.celery_app:celery_app",
        "worker",
        "--loglevel=info",
        "--concurrency=2",  # Windows下建议较少的并发数
        "--pool=solo",      # Windows下使用solo pool
        "--without-mingle", # 避免Windows下的mingle问题
        "--without-gossip"  # 避免Windows下的gossip问题
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            env=os.environ.copy()
        )
        
        print(f"Celery Worker已启动，PID: {process.pid}")
        print("按 Ctrl+C 停止Worker")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n正在停止Celery Worker...")
        try:
            process.terminate()
            process.wait(timeout=10)
        except subprocess.TimeoutExpired:
            print("强制终止Worker进程...")
            process.kill()
        print("Celery Worker已停止")
        
    except Exception as e:
        print(f"启动Celery Worker时出错: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("AI小说生成器 - Celery Worker 启动脚本 (Windows)")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 启动Worker
    if not start_celery_worker():
        sys.exit(1)


if __name__ == "__main__":
    main() 