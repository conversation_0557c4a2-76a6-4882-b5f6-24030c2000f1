#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
流式内容模型 - 支持增量内容存储和版本管理
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid

from app.core.database import Base


class ContentStatus(str, enum.Enum):
    """内容状态枚举"""
    STREAMING = "streaming"      # 正在流式传输
    COMPLETED = "completed"      # 传输完成
    INTERRUPTED = "interrupted"  # 传输中断
    RECOVERED = "recovered"      # 已恢复


class StreamingContent(Base):
    """流式内容表 - 存储实时生成的内容片段"""
    __tablename__ = "streaming_contents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    task_id = Column(String(255), ForeignKey("generation_tasks.task_id"), nullable=False, index=True, comment="关联的任务ID")
    stage = Column(String(100), nullable=False, index=True, comment="生成阶段")
    
    # 内容信息
    chunk_index = Column(Integer, nullable=False, comment="内容片段索引")
    chunk_content = Column(Text, nullable=False, comment="内容片段")
    accumulated_content = Column(Text, nullable=True, comment="累积内容（可选，用于快速恢复）")
    
    # 状态信息
    status = Column(String(20), default=ContentStatus.STREAMING.value, comment="内容状态")
    total_length = Column(Integer, default=0, comment="总长度")
    is_final = Column(Boolean, default=False, comment="是否为最终片段")
    
    # 元数据
    extra_metadata = Column(Text, nullable=True, comment="额外元数据JSON")
    checksum = Column(String(64), nullable=True, comment="内容校验和")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<StreamingContent(id={self.id}, task_id={self.task_id}, stage={self.stage}, chunk_index={self.chunk_index})>"


class ContentRecoveryPoint(Base):
    """内容恢复点表 - 支持断点续传"""
    __tablename__ = "content_recovery_points"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    task_id = Column(String(255), nullable=False, index=True, comment="任务ID")
    stage = Column(String(100), nullable=False, comment="生成阶段")
    
    # 恢复信息
    last_chunk_index = Column(Integer, nullable=False, comment="最后成功的片段索引")
    accumulated_content = Column(Text, nullable=False, comment="累积内容")
    total_length = Column(Integer, nullable=False, comment="总长度")
    
    # 恢复状态
    is_active = Column(Boolean, default=True, comment="是否为活跃恢复点")
    recovery_count = Column(Integer, default=0, comment="恢复次数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    def __repr__(self):
        return f"<ContentRecoveryPoint(id={self.id}, task_id={self.task_id}, stage={self.stage})>"


class StreamingMetrics(Base):
    """流式传输指标表 - 用于性能监控和优化"""
    __tablename__ = "streaming_metrics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    task_id = Column(String(255), nullable=False, index=True, comment="任务ID")
    stage = Column(String(100), nullable=False, comment="生成阶段")
    
    # 性能指标
    total_chunks = Column(Integer, nullable=False, comment="总片段数")
    total_bytes = Column(Integer, nullable=False, comment="总字节数")
    streaming_duration = Column(Float, nullable=False, comment="流式传输持续时间（秒）")
    average_chunk_size = Column(Float, nullable=False, comment="平均片段大小")
    chunks_per_second = Column(Float, nullable=False, comment="每秒片段数")
    
    # 错误统计
    error_count = Column(Integer, default=0, comment="错误次数")
    retry_count = Column(Integer, default=0, comment="重试次数")
    recovery_count = Column(Integer, default=0, comment="恢复次数")
    
    # 质量指标
    completion_rate = Column(Float, default=1.0, comment="完成率")
    data_integrity_score = Column(Float, default=1.0, comment="数据完整性评分")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    def __repr__(self):
        return f"<StreamingMetrics(id={self.id}, task_id={self.task_id}, stage={self.stage})>"
