#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
小说项目模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON, Enum, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid

from app.core.database import Base


class NovelStatus(str, enum.Enum):
    """小说状态枚举"""
    DRAFT = "draft"  # 草稿
    GENERATING = "generating"  # 生成中
    COMPLETED = "completed"  # 已完成
    PAUSED = "paused"  # 暂停


class Novel(Base):
    """小说项目表"""
    __tablename__ = "novels"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    
    # 基本信息
    title = Column(String(255), nullable=False, comment="小说标题")
    description = Column(Text, nullable=True, comment="小说简介")
    genre = Column(String(50), nullable=True, comment="小说类型")
    target_length = Column(Integer, nullable=True, comment="目标字数")
    style_settings = Column(JSON, nullable=True, comment="风格设置JSON")
    status = Column(Enum(NovelStatus, values_callable=lambda obj: [e.value for e in obj]), default=NovelStatus.DRAFT.value, comment="小说状态")
    
    # 配置信息
    config = Column(JSON, nullable=True, comment="小说配置JSON")
    
    # 统计信息
    chapter_count = Column(Integer, default=0, comment="章节数量")
    total_words = Column(Integer, default=0, comment="总字数")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="novels")
    documents = relationship("Document", back_populates="novel", cascade="all, delete-orphan")
    chapters = relationship("Chapter", back_populates="novel", cascade="all, delete-orphan")
    generation_tasks = relationship("GenerationTask", back_populates="novel", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Novel(id={self.id}, title={self.title}, status={self.status})>" 