<?xml version="1.0"?>
<net name="Model0" version="11">
	<layers>
		<layer id="2" name="input_ids" type="Parameter" version="opset1">
			<data shape="?,?" element_type="i64" />
			<output>
				<port id="0" precision="I64" names="input_ids">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="attention_mask" type="Parameter" version="opset1">
			<data shape="?,?" element_type="i64" />
			<output>
				<port id="0" precision="I64" names="attention_mask">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="0" name="token_type_ids" type="Parameter" version="opset1">
			<data shape="?,?" element_type="i64" />
			<output>
				<port id="0" precision="I64" names="token_type_ids">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="Constant_27536122" type="Const" version="opset1">
			<data element_type="i8" shape="30522, 384" offset="0" size="11720448" />
			<output>
				<port id="0" precision="I8">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="Convert_27536123" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="Constant_27536124" type="Const" version="opset1">
			<data element_type="f32" shape="30522, 1" offset="11720448" size="122088" />
			<output>
				<port id="0" precision="FP32">
					<dim>30522</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="__module.embeddings.word_embeddings/aten::embedding/Gather/fq_weights_0" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>30522</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="__module.embeddings.word_embeddings/aten::embedding/Convert" type="Convert" version="opset1">
			<data destination_type="i32" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="__module.embeddings.word_embeddings/aten::embedding/Constant" type="Const" version="opset1">
			<data element_type="i32" shape="" offset="11842536" size="4" />
			<output>
				<port id="0" precision="I32" />
			</output>
		</layer>
		<layer id="9" name="__module.embeddings.word_embeddings/aten::embedding/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>30522</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="2" precision="I32" />
			</input>
			<output>
				<port id="3" precision="FP32" names="79,inputs_embeds">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="Constant_27536126" type="Const" version="opset1">
			<data element_type="i8" shape="2, 384" offset="11842540" size="768" />
			<output>
				<port id="0" precision="I8">
					<dim>2</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="Convert_27536127" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>2</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="Constant_27536128" type="Const" version="opset1">
			<data element_type="f32" shape="2, 1" offset="11843308" size="8" />
			<output>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="__module.embeddings.token_type_embeddings/aten::embedding/Gather/fq_weights_0" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>2</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>2</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="__module.embeddings.token_type_embeddings/aten::embedding/Convert" type="Convert" version="opset1">
			<data destination_type="i32" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="__module.embeddings.token_type_embeddings/aten::embedding/Constant" type="Const" version="opset1">
			<data element_type="i32" shape="" offset="11842536" size="4" />
			<output>
				<port id="0" precision="I32" />
			</output>
		</layer>
		<layer id="16" name="__module.embeddings.token_type_embeddings/aten::embedding/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>2</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="2" precision="I32" />
			</input>
			<output>
				<port id="3" precision="FP32" names="81,token_type_embeddings.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="__module.embeddings/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="82_1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="Constant_27536130" type="Const" version="opset1">
			<data element_type="i8" shape="512, 384" offset="11843316" size="196608" />
			<output>
				<port id="0" precision="I8">
					<dim>512</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="Convert_27536131" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>512</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>512</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="Constant_27536132" type="Const" version="opset1">
			<data element_type="f32" shape="512, 1" offset="12039924" size="2048" />
			<output>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="__module.embeddings.position_embeddings/aten::embedding/Gather/fq_weights_0" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>512</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>512</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="__module.embeddings/aten::slice/Slice" type="Const" version="opset1">
			<data element_type="i64" shape="1, 512" offset="12041972" size="4096" />
			<output>
				<port id="0" precision="I64" names="76">
					<dim>1</dim>
					<dim>512</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="__module.embeddings/aten::slice/Reshape" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="ShapeOf_6355" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="25" name="Constant_6476" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="Constant_6357" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="27" name="Gather_6358" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="10,72,74,75,8">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="__module.embeddings/aten::slice/Reshape_2" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="__module.embeddings/aten::slice/Reshape_3" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="__module.embeddings/aten::slice/Slice_1" type="Slice" version="opset8">
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
					<dim>512</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
				<port id="4" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="5" precision="I64" names="77">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="__module.embeddings.position_embeddings/aten::embedding/Convert" type="Convert" version="opset1">
			<data destination_type="i32" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I32">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="__module.embeddings.position_embeddings/aten::embedding/Constant" type="Const" version="opset1">
			<data element_type="i32" shape="" offset="11842536" size="4" />
			<output>
				<port id="0" precision="I32" />
			</output>
		</layer>
		<layer id="33" name="__module.embeddings.position_embeddings/aten::embedding/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>512</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="2" precision="I32" />
			</input>
			<output>
				<port id="3" precision="FP32" names="84,position_embeddings.1">
					<dim>1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="__module.embeddings/aten::add_/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="82,embeddings.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="__module.embeddings.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="__module.embeddings.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="Constant_6230" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12046088" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="__module.embeddings.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="Constant_6231" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12047624" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="__module.embeddings.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="89,input.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12049160" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12050696" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="44" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12050700" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="45" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12050696" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="46" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12050700" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="47" name="__module.embeddings.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="Constant_27536134" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="12050704" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="Convert_27536135" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="Constant_27536136" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="12198160" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="__module.encoder.layer.0.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="__module.encoder.layer.0.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="Constant_6232" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12199696" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="129,x.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12201232" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="56" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12201236" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="57" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12201232" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="58" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12201236" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="59" name="__module.encoder.layer.0.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="__module.encoder.layer.0.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="__module.encoder.layer.0.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="133,x.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="Constant_247" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="134">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="__module.encoder.layer.0.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="135">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="Constant_27536138" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="12201304" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="65" name="Convert_27536139" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="Constant_27536140" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="12348760" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="__module.encoder.layer.0.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="__module.encoder.layer.0.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="Constant_6233" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12350296" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="138,x.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12351832" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="72" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12351836" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="73" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12351832" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="74" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12351836" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="75" name="__module.encoder.layer.0.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="__module.encoder.layer.0.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="__module.encoder.layer.0.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="142,x.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="Constant_272" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="143">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="__module.encoder.layer.0.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="144">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="Constant_27536142" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="12351840" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="Convert_27536143" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="Constant_27536144" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="12499296" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="__module.encoder.layer.0.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="__module.encoder.layer.0.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="Constant_6234" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12500832" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="__module.encoder.layer.0.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="147,x.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="__module.encoder.layer.0.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="__module.encoder.layer.0.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="151,x.11">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="Constant_297" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="152">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="__module.encoder.layer.0.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="153">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="Constant_6236" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="12502368" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="25" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64" names="25" />
			</output>
		</layer>
		<layer id="93" name="aten::unsqueeze/Unsqueeze" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="26">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="27" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12502372" size="8" />
			<output>
				<port id="0" precision="I64" names="27" />
			</output>
		</layer>
		<layer id="95" name="aten::unsqueeze/Unsqueeze_1" type="Unsqueeze" version="opset1">
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I64" />
			</input>
			<output>
				<port id="2" precision="I64" names="28,33">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="ShapeOf_6363" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="Constant_6479" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="Constant_6365" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="99" name="Gather_6366" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="13,15">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="Constant_5460" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="Constant_6482" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12046076" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="Constant_6373" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="103" name="Gather_6374" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="17,19">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="prim::ListConstruct/Concat" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64" names="35">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="aten::expand/Broadcast" type="Broadcast" version="opset3">
			<data mode="bidirectional" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="37">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="aten::to/Convert" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I64">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="42">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="Constant_6235" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1, 1" offset="12502368" size="4" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="aten::rsub/Multiply" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="aten::rsub/Subtract" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="45,inverted_mask">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="aten::to/Convert_1" type="Convert" version="opset1">
			<data destination_type="boolean" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="BOOL" names="50">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="aten::masked_fill/ConvertLike" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12502380" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="112" name="aten::masked_fill/Select" type="Select" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="BOOL">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="52">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="__module.encoder.layer.0.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="154,attn_output.1">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="__module.encoder.layer.0.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="__module.encoder.layer.0.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="155,attn_output.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="__module.encoder.layer.0.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="Constant_5737" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="Constant_5738" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="119" name="Gather_5739" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="120" name="__module.encoder.layer.0.attention.self/prim::ListConstruct/Reshape_1_3" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="12502416" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="__module.encoder.layer.0.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="156">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="157">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12502424" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12503960" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="126" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12503964" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="127" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12503960" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="128" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12503964" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="129" name="__module.encoder.layer.0.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="Constant_27536146" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="12503968" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="Convert_27536147" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="Constant_27536148" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="12651424" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="__module.encoder.layer.0.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="__module.encoder.layer.0.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="Constant_6237" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12652960" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="__module.encoder.layer.0.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="163,input.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="__module.encoder.layer.0.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="165">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="Constant_6238" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12654496" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="Constant_6239" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12656032" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="169,input_tensor.1">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="12657568" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12659104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="147" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12659108" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="148" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12659104" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="149" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="12659108" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="150" name="__module.encoder.layer.0.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="Constant_27536150" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="12659112" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="Convert_27536151" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="Constant_27536152" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="13248936" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="__module.encoder.layer.0.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="__module.encoder.layer.0.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="Constant_6240" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="13255080" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="__module.encoder.layer.0.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="174">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="175">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="13261224" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13267368" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="162" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13267372" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="163" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13267368" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="164" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13267372" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="165" name="__module.encoder.layer.0.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="Constant_27536154" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="13267376" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="Convert_27536155" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="Constant_27536156" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="13857200" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="__module.encoder.layer.0.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="__module.encoder.layer.0.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="Constant_6241" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="13858736" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="__module.encoder.layer.0.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="181,input.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="__module.encoder.layer.0.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="183">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="175" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="Constant_6242" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="13860272" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="Constant_6243" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="13861808" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="187,hidden_states.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="13863344" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13864880" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="183" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13864884" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="184" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13864880" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="185" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="13864884" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="186" name="__module.encoder.layer.0.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="Constant_27536158" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="13864888" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="Convert_27536159" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="Constant_27536160" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="14012344" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="__module.encoder.layer.1.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="__module.encoder.layer.1.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="Constant_6244" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14013880" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="200,x.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14015416" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="195" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14015420" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="196" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14015416" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="197" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14015420" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="198" name="__module.encoder.layer.1.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="__module.encoder.layer.1.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="__module.encoder.layer.1.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="204,x.15">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="Constant_479" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="205">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="__module.encoder.layer.1.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="206">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="Constant_27536162" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="14015424" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="Convert_27536163" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="Constant_27536164" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="14162880" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="__module.encoder.layer.1.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="__module.encoder.layer.1.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="Constant_6245" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14164416" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="209,x.17">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14165952" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="211" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14165956" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="212" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14165952" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="213" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14165956" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="214" name="__module.encoder.layer.1.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="__module.encoder.layer.1.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="__module.encoder.layer.1.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="213,x.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="Constant_502" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="214">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="__module.encoder.layer.1.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="215">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="Constant_27536166" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="14165960" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="Convert_27536167" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="Constant_27536168" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="14313416" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="__module.encoder.layer.1.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="__module.encoder.layer.1.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="Constant_6246" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14314952" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="__module.encoder.layer.1.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="218,x.21">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="__module.encoder.layer.1.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="__module.encoder.layer.1.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="222,x.23">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="Constant_525" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="223">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="229" name="__module.encoder.layer.1.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="224">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="__module.encoder.layer.1.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="225,attn_output.5">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="__module.encoder.layer.1.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="__module.encoder.layer.1.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="226,attn_output.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="__module.encoder.layer.1.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="Constant_5757" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="Constant_5758" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="236" name="Gather_5759" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="__module.encoder.layer.1.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="227">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="228">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14316488" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14318024" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="242" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14318028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="243" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14318024" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="244" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14318028" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="245" name="__module.encoder.layer.1.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="Constant_27536170" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="14318032" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="Convert_27536171" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="Constant_27536172" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="14465488" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="__module.encoder.layer.1.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="__module.encoder.layer.1.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="Constant_6247" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14467024" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="__module.encoder.layer.1.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="234,input.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="__module.encoder.layer.1.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="236">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="Constant_6248" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14468560" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="Constant_6249" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14470096" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="240,input_tensor.3">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="14471632" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14473168" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="263" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14473172" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="264" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14473168" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="265" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="14473172" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="266" name="__module.encoder.layer.1.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="Constant_27536174" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="14473176" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="Convert_27536175" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="Constant_27536176" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="15063000" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="__module.encoder.layer.1.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="__module.encoder.layer.1.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="Constant_6250" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="15069144" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="__module.encoder.layer.1.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="245">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="246">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="15075288" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15081432" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="278" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15081436" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="279" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15081432" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="280" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15081436" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="281" name="__module.encoder.layer.1.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="Constant_27536178" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="15081440" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="Convert_27536179" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="Constant_27536180" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="15671264" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="__module.encoder.layer.1.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="__module.encoder.layer.1.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="Constant_6251" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15672800" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="__module.encoder.layer.1.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="252,input.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="__module.encoder.layer.1.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="254">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="Constant_6252" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15674336" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="Constant_6253" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15675872" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="258,hidden_states.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15677408" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15678944" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="299" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15678948" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="300" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15678944" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="301" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15678948" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="302" name="__module.encoder.layer.1.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="Constant_27536182" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="15678952" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="Convert_27536183" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="Constant_27536184" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="15826408" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="__module.encoder.layer.2.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="__module.encoder.layer.2.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="Constant_6254" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15827944" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="271,x.25">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15829480" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="311" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15829484" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="312" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15829480" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="313" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15829484" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="314" name="__module.encoder.layer.2.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="__module.encoder.layer.2.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="__module.encoder.layer.2.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="275,x.27">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="Constant_705" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="276">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="__module.encoder.layer.2.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="277">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="Constant_27536186" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="15829488" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="Convert_27536187" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="Constant_27536188" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="15976944" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="__module.encoder.layer.2.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="__module.encoder.layer.2.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="Constant_6255" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="15978480" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="280,x.29">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15980016" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="327" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15980020" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="328" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15980016" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="329" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="15980020" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="330" name="__module.encoder.layer.2.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="__module.encoder.layer.2.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="__module.encoder.layer.2.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="284,x.31">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="Constant_728" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="285">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="__module.encoder.layer.2.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="286">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="Constant_27536190" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="15980024" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="Convert_27536191" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="Constant_27536192" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="16127480" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="__module.encoder.layer.2.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="__module.encoder.layer.2.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="Constant_6256" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16129016" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="__module.encoder.layer.2.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="289,x.33">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="__module.encoder.layer.2.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="__module.encoder.layer.2.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="293,x.35">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="Constant_751" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="294">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="345" name="__module.encoder.layer.2.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="295">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="__module.encoder.layer.2.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="296,attn_output.9">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="__module.encoder.layer.2.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="__module.encoder.layer.2.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="297,attn_output.11">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="__module.encoder.layer.2.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="Constant_5777" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="Constant_5778" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="352" name="Gather_5779" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="__module.encoder.layer.2.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="298">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="299">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16130552" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16132088" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="358" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16132092" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="359" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16132088" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="360" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16132092" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="361" name="__module.encoder.layer.2.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="Constant_27536194" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="16132096" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="Convert_27536195" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="Constant_27536196" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="16279552" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="__module.encoder.layer.2.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="__module.encoder.layer.2.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="Constant_6257" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16281088" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="__module.encoder.layer.2.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="305,input.11">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="__module.encoder.layer.2.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="307">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="Constant_6258" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16282624" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="Constant_6259" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16284160" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="311,input_tensor.5">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="16285696" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16287232" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="379" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16287236" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="380" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16287232" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="381" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16287236" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="382" name="__module.encoder.layer.2.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="Constant_27536198" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="16287240" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="Convert_27536199" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="Constant_27536200" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="16877064" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="__module.encoder.layer.2.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="__module.encoder.layer.2.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="Constant_6260" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="16883208" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="__module.encoder.layer.2.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="316">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="317">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="16889352" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16895496" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="394" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16895500" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="395" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16895496" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="396" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="16895500" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="397" name="__module.encoder.layer.2.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="Constant_27536202" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="16895504" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="Convert_27536203" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="Constant_27536204" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="17485328" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="__module.encoder.layer.2.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="__module.encoder.layer.2.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="Constant_6261" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17486864" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="__module.encoder.layer.2.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="323,input.13">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="__module.encoder.layer.2.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="325">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="407" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="408" name="Constant_6262" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17488400" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="409" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="410" name="Constant_6263" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17489936" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="329,hidden_states.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17491472" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17493008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="415" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17493012" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="416" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17493008" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="417" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17493012" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="418" name="__module.encoder.layer.2.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="Constant_27536206" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="17493016" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="Convert_27536207" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="Constant_27536208" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="17640472" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="__module.encoder.layer.3.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="__module.encoder.layer.3.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="Constant_6264" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17642008" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="342,x.37">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17643544" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="427" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17643548" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="428" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17643544" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="429" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17643548" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="430" name="__module.encoder.layer.3.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="__module.encoder.layer.3.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="__module.encoder.layer.3.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="346,x.39">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="Constant_931" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="347">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="__module.encoder.layer.3.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="348">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="Constant_27536210" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="17643552" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="Convert_27536211" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="Constant_27536212" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="17791008" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="__module.encoder.layer.3.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="__module.encoder.layer.3.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="Constant_6265" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17792544" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="351,x.41">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17794080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="443" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17794084" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="444" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17794080" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="445" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17794084" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="446" name="__module.encoder.layer.3.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="__module.encoder.layer.3.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="__module.encoder.layer.3.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="355,x.43">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="Constant_954" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="356">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="__module.encoder.layer.3.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="357">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="Constant_27536214" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="17794088" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="Convert_27536215" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="Constant_27536216" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="17941544" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="__module.encoder.layer.3.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="__module.encoder.layer.3.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="Constant_6266" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17943080" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="__module.encoder.layer.3.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="360,x.45">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="__module.encoder.layer.3.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="__module.encoder.layer.3.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="364,x.47">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="Constant_977" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="365">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="__module.encoder.layer.3.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="366">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="__module.encoder.layer.3.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="367,attn_output.13">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="__module.encoder.layer.3.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="__module.encoder.layer.3.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="368,attn_output.15">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="__module.encoder.layer.3.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="Constant_5797" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="Constant_5798" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="468" name="Gather_5799" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="__module.encoder.layer.3.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="369">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="370">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="17944616" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17946152" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="474" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17946156" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="475" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17946152" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="476" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="17946156" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="477" name="__module.encoder.layer.3.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="Constant_27536218" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="17946160" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="Convert_27536219" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="Constant_27536220" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="18093616" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="481" name="__module.encoder.layer.3.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="482" name="__module.encoder.layer.3.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="483" name="Constant_6267" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="18095152" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="484" name="__module.encoder.layer.3.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="376,input.15">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="485" name="__module.encoder.layer.3.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="378">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="486" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="487" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="488" name="Constant_6268" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="18096688" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="Constant_6269" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="18098224" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="491" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="382,input_tensor.7">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="492" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="18099760" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="493" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="494" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18101296" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="495" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18101300" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="496" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18101296" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="497" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18101300" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="498" name="__module.encoder.layer.3.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="Constant_27536222" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="18101304" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="Convert_27536223" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="Constant_27536224" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="18691128" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="__module.encoder.layer.3.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="__module.encoder.layer.3.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="Constant_6270" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="18697272" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="__module.encoder.layer.3.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="387">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="506" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="388">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="507" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="18703416" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="508" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="509" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18709560" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="510" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18709564" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="511" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18709560" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="512" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="18709564" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="513" name="__module.encoder.layer.3.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="Constant_27536226" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="18709568" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="Convert_27536227" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="Constant_27536228" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="19299392" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="__module.encoder.layer.3.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="__module.encoder.layer.3.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="Constant_6271" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19300928" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="__module.encoder.layer.3.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="394,input.17">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="__module.encoder.layer.3.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="396">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="Constant_6272" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19302464" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="Constant_6273" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19304000" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="527" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="400,hidden_states.25">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="528" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19305536" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="529" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="530" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19307072" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="531" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19307076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="532" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19307072" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="533" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19307076" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="534" name="__module.encoder.layer.3.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="Constant_27536230" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="19307080" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="Convert_27536231" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="Constant_27536232" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="19454536" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="__module.encoder.layer.4.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="__module.encoder.layer.4.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="Constant_6274" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19456072" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="541" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="413,x.49">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="542" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19457608" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="543" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19457612" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="544" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19457608" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="545" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19457612" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="546" name="__module.encoder.layer.4.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="547" name="__module.encoder.layer.4.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="548" name="__module.encoder.layer.4.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="417,x.51">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="549" name="Constant_1157" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="418">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="550" name="__module.encoder.layer.4.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="419">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="551" name="Constant_27536234" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="19457616" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="552" name="Convert_27536235" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="553" name="Constant_27536236" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="19605072" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="554" name="__module.encoder.layer.4.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="555" name="__module.encoder.layer.4.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="556" name="Constant_6275" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19606608" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="557" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="422,x.53">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="558" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19608144" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="559" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19608148" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="560" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19608144" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="561" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19608148" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="562" name="__module.encoder.layer.4.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="563" name="__module.encoder.layer.4.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="564" name="__module.encoder.layer.4.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="426,x.55">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="565" name="Constant_1180" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="427">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="566" name="__module.encoder.layer.4.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="428">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="567" name="Constant_27536238" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="19608152" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="568" name="Convert_27536239" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="569" name="Constant_27536240" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="19755608" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="570" name="__module.encoder.layer.4.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="571" name="__module.encoder.layer.4.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="572" name="Constant_6276" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19757144" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="573" name="__module.encoder.layer.4.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="431,x.57">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="574" name="__module.encoder.layer.4.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="575" name="__module.encoder.layer.4.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="435,x.59">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="576" name="Constant_1203" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="436">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="577" name="__module.encoder.layer.4.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="437">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="578" name="__module.encoder.layer.4.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="438,attn_output.17">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="579" name="__module.encoder.layer.4.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="580" name="__module.encoder.layer.4.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="439,attn_output.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="581" name="__module.encoder.layer.4.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="582" name="Constant_5817" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="583" name="Constant_5818" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="584" name="Gather_5819" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="585" name="__module.encoder.layer.4.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="440">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="586" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="441">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="587" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19758680" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="588" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="589" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19760216" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="590" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19760220" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="591" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19760216" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="592" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19760220" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="593" name="__module.encoder.layer.4.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="594" name="Constant_27536242" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="19760224" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="595" name="Convert_27536243" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="596" name="Constant_27536244" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="19907680" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="597" name="__module.encoder.layer.4.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="598" name="__module.encoder.layer.4.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="599" name="Constant_6277" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19909216" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="600" name="__module.encoder.layer.4.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="447,input.19">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="601" name="__module.encoder.layer.4.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="449">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="602" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="603" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="604" name="Constant_6278" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19910752" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="605" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="606" name="Constant_6279" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19912288" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="607" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="453,input_tensor.9">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="608" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="19913824" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="609" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="610" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19915360" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="611" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19915364" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="612" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19915360" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="613" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="19915364" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="614" name="__module.encoder.layer.4.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="615" name="Constant_27536246" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="19915368" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="616" name="Convert_27536247" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="617" name="Constant_27536248" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="20505192" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="618" name="__module.encoder.layer.4.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="619" name="__module.encoder.layer.4.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="620" name="Constant_6280" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="20511336" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="621" name="__module.encoder.layer.4.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="458">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="622" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="459">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="623" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="20517480" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="624" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="625" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="20523624" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="626" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="20523628" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="627" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="20523624" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="628" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="20523628" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="629" name="__module.encoder.layer.4.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="630" name="Constant_27536250" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="20523632" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="631" name="Convert_27536251" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="632" name="Constant_27536252" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="21113456" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="633" name="__module.encoder.layer.4.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="634" name="__module.encoder.layer.4.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="635" name="Constant_6281" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21114992" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="636" name="__module.encoder.layer.4.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="465,input.21">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="637" name="__module.encoder.layer.4.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="467">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="638" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="639" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="640" name="Constant_6282" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21116528" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="641" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="642" name="Constant_6283" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21118064" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="643" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="471,hidden_states.31">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="644" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21119600" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="645" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="646" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21121136" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="647" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21121140" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="648" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21121136" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="649" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21121140" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="650" name="__module.encoder.layer.4.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="651" name="Constant_27536254" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="21121144" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="652" name="Convert_27536255" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="653" name="Constant_27536256" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="21268600" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="654" name="__module.encoder.layer.5.attention.self.query/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="655" name="__module.encoder.layer.5.attention.self.query/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="656" name="Constant_6284" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21270136" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="657" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="484,x.61">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="658" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21271672" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="659" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21271676" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="660" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21271672" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="661" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21271676" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="662" name="__module.encoder.layer.5.attention.self.query/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="663" name="__module.encoder.layer.5.attention.self/prim::ListConstruct/Concat" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="664" name="__module.encoder.layer.5.attention.self/aten::view/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="488,x.63">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="665" name="Constant_1383" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="489">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="666" name="__module.encoder.layer.5.attention.self/aten::permute/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="490">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="667" name="Constant_27536258" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="21271680" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="668" name="Convert_27536259" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="669" name="Constant_27536260" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="21419136" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="670" name="__module.encoder.layer.5.attention.self.key/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="671" name="__module.encoder.layer.5.attention.self.key/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="672" name="Constant_6285" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21420672" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="673" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="493,x.65">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="674" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21422208" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="675" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21422212" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="676" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21422208" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="677" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21422212" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="678" name="__module.encoder.layer.5.attention.self.key/aten::linear/Add/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="679" name="__module.encoder.layer.5.attention.self/prim::ListConstruct/Concat_1" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="680" name="__module.encoder.layer.5.attention.self/aten::view/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="497,x.67">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="681" name="Constant_1406" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="498">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="682" name="__module.encoder.layer.5.attention.self/aten::permute/Transpose_1" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="499">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="683" name="Constant_27536262" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="21422216" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="684" name="Convert_27536263" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="685" name="Constant_27536264" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="21569672" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="686" name="__module.encoder.layer.5.attention.self.value/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="687" name="__module.encoder.layer.5.attention.self.value/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="688" name="Constant_6286" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21571208" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="689" name="__module.encoder.layer.5.attention.self.value/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="502,x.69">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="690" name="__module.encoder.layer.5.attention.self/prim::ListConstruct/Concat_2" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201240" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="691" name="__module.encoder.layer.5.attention.self/aten::view/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="506,x">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="692" name="Constant_1429" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="12201272" size="32" />
			<output>
				<port id="0" precision="I64" names="507">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="693" name="__module.encoder.layer.5.attention.self/aten::permute/Transpose_2" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="508">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="694" name="__module.encoder.layer.5.attention.self/aten::scaled_dot_product_attention/ScaledDotProductAttention" type="ScaledDotProductAttention" version="opset13">
			<data causal="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>-1</dim>
					<dim>1</dim>
					<dim>-1</dim>
					<dim>-1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="509,attn_output.21">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="695" name="__module.encoder.layer.5.attention.self/aten::transpose/ScatterElementsUpdate" type="Const" version="opset1">
			<data element_type="i32" shape="4" offset="12502384" size="16" />
			<output>
				<port id="0" precision="I32">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="696" name="__module.encoder.layer.5.attention.self/aten::transpose/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>12</dim>
					<dim>-1</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="510,attn_output">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
			</output>
		</layer>
		<layer id="697" name="__module.encoder.layer.5.attention.self/aten::size/ShapeOf_6" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="698" name="Constant_5837" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="12502400" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="699" name="Constant_5838" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="12046068" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="700" name="Gather_5839" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="701" name="__module.encoder.layer.5.attention.self/prim::ListConstruct/Concat_3" type="Concat" version="opset1">
			<data axis="0" />
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="511">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="702" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape" type="Reshape" version="opset1">
			<data special_zero="false" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>12</dim>
					<dim>32</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="512">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="703" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21572744" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="704" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="705" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21574280" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="706" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21574284" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="707" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21574280" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="708" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21574284" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="709" name="__module.encoder.layer.5.attention.self/aten::reshape/Reshape_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="710" name="Constant_27536266" type="Const" version="opset1">
			<data element_type="i8" shape="384, 384" offset="21574288" size="147456" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="711" name="Convert_27536267" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="712" name="Constant_27536268" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="21721744" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="713" name="__module.encoder.layer.5.attention.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="714" name="__module.encoder.layer.5.attention.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="715" name="Constant_6287" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21723280" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="716" name="__module.encoder.layer.5.attention.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="518,input.23">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="717" name="__module.encoder.layer.5.attention.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="520">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="718" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="719" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="720" name="Constant_6288" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21724816" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="721" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="722" name="Constant_6289" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21726352" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="723" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="524,input_tensor">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="724" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="21727888" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="725" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="726" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21729424" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="727" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21729428" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="728" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21729424" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="729" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="21729428" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="730" name="__module.encoder.layer.5.attention.output.LayerNorm/aten::layer_norm/Add_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="731" name="Constant_27536270" type="Const" version="opset1">
			<data element_type="i8" shape="1536, 384" offset="21729432" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="732" name="Convert_27536271" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="733" name="Constant_27536272" type="Const" version="opset1">
			<data element_type="f32" shape="1536, 1" offset="22319256" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="734" name="__module.encoder.layer.5.intermediate.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="735" name="__module.encoder.layer.5.intermediate.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1536</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="736" name="Constant_6290" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="22325400" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="737" name="__module.encoder.layer.5.intermediate.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="529">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="738" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu" type="Gelu" version="opset7">
			<data approximation_mode="ERF" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="530">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="739" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/scale" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 1536" offset="22331544" size="6144" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="740" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="741" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="22337688" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="742" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/input_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="22337692" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="743" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_low" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="22337688" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="744" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0/output_high" type="Const" version="opset1">
			<data element_type="f32" shape="" offset="22337692" size="4" />
			<output>
				<port id="0" precision="FP32" />
			</output>
		</layer>
		<layer id="745" name="__module.encoder.layer.5.intermediate.intermediate_act_fn/aten::gelu/Gelu_0_0/nncf_smooth_quant/fq_output_0" type="FakeQuantize" version="opset1">
			<data levels="256" auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32" />
				<port id="2" precision="FP32" />
				<port id="3" precision="FP32" />
				<port id="4" precision="FP32" />
			</input>
			<output>
				<port id="5" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="746" name="Constant_27536274" type="Const" version="opset1">
			<data element_type="i8" shape="384, 1536" offset="22337696" size="589824" />
			<output>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="747" name="Convert_27536275" type="Convert" version="opset1">
			<data destination_type="f32" />
			<input>
				<port id="0" precision="I8">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="748" name="Constant_27536276" type="Const" version="opset1">
			<data element_type="f32" shape="384, 1" offset="22927520" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="749" name="__module.encoder.layer.5.output.dense/aten::linear/MatMul/fq_weights_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</output>
		</layer>
		<layer id="750" name="__module.encoder.layer.5.output.dense/aten::linear/MatMul" type="MatMul" version="opset1">
			<data transpose_a="false" transpose_b="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>1536</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>384</dim>
					<dim>1536</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="751" name="Constant_6291" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="22929056" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="752" name="__module.encoder.layer.5.output.dense/aten::linear/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="536,input">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="753" name="__module.encoder.layer.5.output/aten::add/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="538">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="754" name="__module.encoder.layer.5.output.LayerNorm/aten::layer_norm/Multiply" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="12046084" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="755" name="__module.encoder.layer.5.output.LayerNorm/aten::layer_norm/MVN" type="MVN" version="opset6">
			<data eps="9.999999960041972e-13" normalize_variance="true" eps_mode="INSIDE_SQRT" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="756" name="Constant_6292" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="22930592" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="757" name="__module.encoder.layer.5.output.LayerNorm/aten::layer_norm/Multiply_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="758" name="Constant_6293" type="Const" version="opset1">
			<data element_type="f32" shape="1, 1, 384" offset="22932128" size="1536" />
			<output>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="759" name="__module.encoder.layer.5.output.LayerNorm/aten::layer_norm/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>384</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="last_hidden_state">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</output>
		</layer>
		<layer id="760" name="Result_2691" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>-1</dim>
					<dim>-1</dim>
					<dim>384</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="14" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="93" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="96" to-port="0" />
		<edge from-layer="2" from-port="0" to-layer="7" to-port="0" />
		<edge from-layer="2" from-port="0" to-layer="24" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="0" />
		<edge from-layer="4" from-port="1" to-layer="6" to-port="0" />
		<edge from-layer="5" from-port="0" to-layer="6" to-port="1" />
		<edge from-layer="6" from-port="2" to-layer="9" to-port="0" />
		<edge from-layer="7" from-port="1" to-layer="9" to-port="1" />
		<edge from-layer="8" from-port="0" to-layer="9" to-port="2" />
		<edge from-layer="9" from-port="3" to-layer="17" to-port="0" />
		<edge from-layer="10" from-port="0" to-layer="11" to-port="0" />
		<edge from-layer="11" from-port="1" to-layer="13" to-port="0" />
		<edge from-layer="12" from-port="0" to-layer="13" to-port="1" />
		<edge from-layer="13" from-port="2" to-layer="16" to-port="0" />
		<edge from-layer="14" from-port="1" to-layer="16" to-port="1" />
		<edge from-layer="15" from-port="0" to-layer="16" to-port="2" />
		<edge from-layer="16" from-port="3" to-layer="17" to-port="1" />
		<edge from-layer="17" from-port="2" to-layer="34" to-port="0" />
		<edge from-layer="18" from-port="0" to-layer="19" to-port="0" />
		<edge from-layer="19" from-port="1" to-layer="21" to-port="0" />
		<edge from-layer="20" from-port="0" to-layer="21" to-port="1" />
		<edge from-layer="21" from-port="2" to-layer="33" to-port="0" />
		<edge from-layer="22" from-port="0" to-layer="30" to-port="0" />
		<edge from-layer="23" from-port="0" to-layer="30" to-port="1" />
		<edge from-layer="24" from-port="1" to-layer="27" to-port="0" />
		<edge from-layer="25" from-port="0" to-layer="27" to-port="1" />
		<edge from-layer="26" from-port="0" to-layer="27" to-port="2" />
		<edge from-layer="27" from-port="3" to-layer="30" to-port="2" />
		<edge from-layer="27" from-port="3" to-layer="104" to-port="2" />
		<edge from-layer="28" from-port="0" to-layer="30" to-port="3" />
		<edge from-layer="29" from-port="0" to-layer="30" to-port="4" />
		<edge from-layer="30" from-port="5" to-layer="31" to-port="0" />
		<edge from-layer="31" from-port="1" to-layer="33" to-port="1" />
		<edge from-layer="32" from-port="0" to-layer="33" to-port="2" />
		<edge from-layer="33" from-port="3" to-layer="34" to-port="1" />
		<edge from-layer="34" from-port="2" to-layer="36" to-port="0" />
		<edge from-layer="35" from-port="0" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="38" to-port="0" />
		<edge from-layer="37" from-port="0" to-layer="38" to-port="1" />
		<edge from-layer="38" from-port="2" to-layer="40" to-port="0" />
		<edge from-layer="39" from-port="0" to-layer="40" to-port="1" />
		<edge from-layer="40" from-port="2" to-layer="42" to-port="0" />
		<edge from-layer="40" from-port="2" to-layer="116" to-port="0" />
		<edge from-layer="40" from-port="2" to-layer="137" to-port="1" />
		<edge from-layer="41" from-port="0" to-layer="42" to-port="1" />
		<edge from-layer="42" from-port="2" to-layer="47" to-port="0" />
		<edge from-layer="43" from-port="0" to-layer="47" to-port="1" />
		<edge from-layer="44" from-port="0" to-layer="47" to-port="2" />
		<edge from-layer="45" from-port="0" to-layer="47" to-port="3" />
		<edge from-layer="46" from-port="0" to-layer="47" to-port="4" />
		<edge from-layer="47" from-port="5" to-layer="68" to-port="0" />
		<edge from-layer="47" from-port="5" to-layer="84" to-port="0" />
		<edge from-layer="47" from-port="5" to-layer="52" to-port="0" />
		<edge from-layer="48" from-port="0" to-layer="49" to-port="0" />
		<edge from-layer="49" from-port="1" to-layer="51" to-port="0" />
		<edge from-layer="50" from-port="0" to-layer="51" to-port="1" />
		<edge from-layer="51" from-port="2" to-layer="52" to-port="1" />
		<edge from-layer="52" from-port="2" to-layer="54" to-port="0" />
		<edge from-layer="53" from-port="0" to-layer="54" to-port="1" />
		<edge from-layer="54" from-port="2" to-layer="59" to-port="0" />
		<edge from-layer="55" from-port="0" to-layer="59" to-port="1" />
		<edge from-layer="56" from-port="0" to-layer="59" to-port="2" />
		<edge from-layer="57" from-port="0" to-layer="59" to-port="3" />
		<edge from-layer="58" from-port="0" to-layer="59" to-port="4" />
		<edge from-layer="59" from-port="5" to-layer="61" to-port="0" />
		<edge from-layer="60" from-port="0" to-layer="61" to-port="1" />
		<edge from-layer="61" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="62" from-port="0" to-layer="63" to-port="1" />
		<edge from-layer="63" from-port="2" to-layer="113" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="65" to-port="0" />
		<edge from-layer="65" from-port="1" to-layer="67" to-port="0" />
		<edge from-layer="66" from-port="0" to-layer="67" to-port="1" />
		<edge from-layer="67" from-port="2" to-layer="68" to-port="1" />
		<edge from-layer="68" from-port="2" to-layer="70" to-port="0" />
		<edge from-layer="69" from-port="0" to-layer="70" to-port="1" />
		<edge from-layer="70" from-port="2" to-layer="75" to-port="0" />
		<edge from-layer="71" from-port="0" to-layer="75" to-port="1" />
		<edge from-layer="72" from-port="0" to-layer="75" to-port="2" />
		<edge from-layer="73" from-port="0" to-layer="75" to-port="3" />
		<edge from-layer="74" from-port="0" to-layer="75" to-port="4" />
		<edge from-layer="75" from-port="5" to-layer="77" to-port="0" />
		<edge from-layer="76" from-port="0" to-layer="77" to-port="1" />
		<edge from-layer="77" from-port="2" to-layer="79" to-port="0" />
		<edge from-layer="78" from-port="0" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="113" to-port="1" />
		<edge from-layer="80" from-port="0" to-layer="81" to-port="0" />
		<edge from-layer="81" from-port="1" to-layer="83" to-port="0" />
		<edge from-layer="82" from-port="0" to-layer="83" to-port="1" />
		<edge from-layer="83" from-port="2" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="86" to-port="0" />
		<edge from-layer="85" from-port="0" to-layer="86" to-port="1" />
		<edge from-layer="86" from-port="2" to-layer="88" to-port="0" />
		<edge from-layer="87" from-port="0" to-layer="88" to-port="1" />
		<edge from-layer="88" from-port="2" to-layer="90" to-port="0" />
		<edge from-layer="89" from-port="0" to-layer="90" to-port="1" />
		<edge from-layer="90" from-port="2" to-layer="113" to-port="2" />
		<edge from-layer="91" from-port="0" to-layer="109" to-port="0" />
		<edge from-layer="92" from-port="0" to-layer="93" to-port="1" />
		<edge from-layer="93" from-port="2" to-layer="95" to-port="0" />
		<edge from-layer="94" from-port="0" to-layer="95" to-port="1" />
		<edge from-layer="95" from-port="2" to-layer="105" to-port="0" />
		<edge from-layer="96" from-port="1" to-layer="99" to-port="0" />
		<edge from-layer="96" from-port="1" to-layer="103" to-port="0" />
		<edge from-layer="97" from-port="0" to-layer="99" to-port="1" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="2" />
		<edge from-layer="99" from-port="3" to-layer="104" to-port="0" />
		<edge from-layer="100" from-port="0" to-layer="104" to-port="1" />
		<edge from-layer="101" from-port="0" to-layer="103" to-port="1" />
		<edge from-layer="102" from-port="0" to-layer="103" to-port="2" />
		<edge from-layer="103" from-port="3" to-layer="104" to-port="3" />
		<edge from-layer="104" from-port="4" to-layer="105" to-port="1" />
		<edge from-layer="105" from-port="2" to-layer="106" to-port="0" />
		<edge from-layer="106" from-port="1" to-layer="108" to-port="0" />
		<edge from-layer="107" from-port="0" to-layer="108" to-port="1" />
		<edge from-layer="108" from-port="2" to-layer="109" to-port="1" />
		<edge from-layer="109" from-port="2" to-layer="110" to-port="0" />
		<edge from-layer="109" from-port="2" to-layer="112" to-port="2" />
		<edge from-layer="110" from-port="1" to-layer="112" to-port="0" />
		<edge from-layer="111" from-port="0" to-layer="112" to-port="1" />
		<edge from-layer="112" from-port="3" to-layer="113" to-port="3" />
		<edge from-layer="112" from-port="3" to-layer="694" to-port="3" />
		<edge from-layer="112" from-port="3" to-layer="578" to-port="3" />
		<edge from-layer="112" from-port="3" to-layer="462" to-port="3" />
		<edge from-layer="112" from-port="3" to-layer="346" to-port="3" />
		<edge from-layer="112" from-port="3" to-layer="230" to-port="3" />
		<edge from-layer="113" from-port="4" to-layer="115" to-port="0" />
		<edge from-layer="114" from-port="0" to-layer="115" to-port="1" />
		<edge from-layer="115" from-port="2" to-layer="122" to-port="0" />
		<edge from-layer="116" from-port="1" to-layer="119" to-port="0" />
		<edge from-layer="117" from-port="0" to-layer="119" to-port="1" />
		<edge from-layer="118" from-port="0" to-layer="119" to-port="2" />
		<edge from-layer="119" from-port="3" to-layer="121" to-port="0" />
		<edge from-layer="120" from-port="0" to-layer="121" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="469" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="585" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="237" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="701" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="353" to-port="1" />
		<edge from-layer="121" from-port="2" to-layer="122" to-port="1" />
		<edge from-layer="122" from-port="2" to-layer="124" to-port="0" />
		<edge from-layer="123" from-port="0" to-layer="124" to-port="1" />
		<edge from-layer="124" from-port="2" to-layer="129" to-port="0" />
		<edge from-layer="125" from-port="0" to-layer="129" to-port="1" />
		<edge from-layer="126" from-port="0" to-layer="129" to-port="2" />
		<edge from-layer="127" from-port="0" to-layer="129" to-port="3" />
		<edge from-layer="128" from-port="0" to-layer="129" to-port="4" />
		<edge from-layer="129" from-port="5" to-layer="134" to-port="0" />
		<edge from-layer="130" from-port="0" to-layer="131" to-port="0" />
		<edge from-layer="131" from-port="1" to-layer="133" to-port="0" />
		<edge from-layer="132" from-port="0" to-layer="133" to-port="1" />
		<edge from-layer="133" from-port="2" to-layer="134" to-port="1" />
		<edge from-layer="134" from-port="2" to-layer="136" to-port="0" />
		<edge from-layer="135" from-port="0" to-layer="136" to-port="1" />
		<edge from-layer="136" from-port="2" to-layer="137" to-port="0" />
		<edge from-layer="137" from-port="2" to-layer="139" to-port="0" />
		<edge from-layer="138" from-port="0" to-layer="139" to-port="1" />
		<edge from-layer="139" from-port="2" to-layer="141" to-port="0" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="1" />
		<edge from-layer="141" from-port="2" to-layer="143" to-port="0" />
		<edge from-layer="142" from-port="0" to-layer="143" to-port="1" />
		<edge from-layer="143" from-port="2" to-layer="173" to-port="1" />
		<edge from-layer="143" from-port="2" to-layer="145" to-port="0" />
		<edge from-layer="144" from-port="0" to-layer="145" to-port="1" />
		<edge from-layer="145" from-port="2" to-layer="150" to-port="0" />
		<edge from-layer="146" from-port="0" to-layer="150" to-port="1" />
		<edge from-layer="147" from-port="0" to-layer="150" to-port="2" />
		<edge from-layer="148" from-port="0" to-layer="150" to-port="3" />
		<edge from-layer="149" from-port="0" to-layer="150" to-port="4" />
		<edge from-layer="150" from-port="5" to-layer="155" to-port="0" />
		<edge from-layer="151" from-port="0" to-layer="152" to-port="0" />
		<edge from-layer="152" from-port="1" to-layer="154" to-port="0" />
		<edge from-layer="153" from-port="0" to-layer="154" to-port="1" />
		<edge from-layer="154" from-port="2" to-layer="155" to-port="1" />
		<edge from-layer="155" from-port="2" to-layer="157" to-port="0" />
		<edge from-layer="156" from-port="0" to-layer="157" to-port="1" />
		<edge from-layer="157" from-port="2" to-layer="158" to-port="0" />
		<edge from-layer="158" from-port="1" to-layer="160" to-port="0" />
		<edge from-layer="159" from-port="0" to-layer="160" to-port="1" />
		<edge from-layer="160" from-port="2" to-layer="165" to-port="0" />
		<edge from-layer="161" from-port="0" to-layer="165" to-port="1" />
		<edge from-layer="162" from-port="0" to-layer="165" to-port="2" />
		<edge from-layer="163" from-port="0" to-layer="165" to-port="3" />
		<edge from-layer="164" from-port="0" to-layer="165" to-port="4" />
		<edge from-layer="165" from-port="5" to-layer="170" to-port="0" />
		<edge from-layer="166" from-port="0" to-layer="167" to-port="0" />
		<edge from-layer="167" from-port="1" to-layer="169" to-port="0" />
		<edge from-layer="168" from-port="0" to-layer="169" to-port="1" />
		<edge from-layer="169" from-port="2" to-layer="170" to-port="1" />
		<edge from-layer="170" from-port="2" to-layer="172" to-port="0" />
		<edge from-layer="171" from-port="0" to-layer="172" to-port="1" />
		<edge from-layer="172" from-port="2" to-layer="173" to-port="0" />
		<edge from-layer="173" from-port="2" to-layer="175" to-port="0" />
		<edge from-layer="174" from-port="0" to-layer="175" to-port="1" />
		<edge from-layer="175" from-port="2" to-layer="177" to-port="0" />
		<edge from-layer="176" from-port="0" to-layer="177" to-port="1" />
		<edge from-layer="177" from-port="2" to-layer="179" to-port="0" />
		<edge from-layer="178" from-port="0" to-layer="179" to-port="1" />
		<edge from-layer="179" from-port="2" to-layer="181" to-port="0" />
		<edge from-layer="179" from-port="2" to-layer="233" to-port="0" />
		<edge from-layer="179" from-port="2" to-layer="253" to-port="1" />
		<edge from-layer="180" from-port="0" to-layer="181" to-port="1" />
		<edge from-layer="181" from-port="2" to-layer="186" to-port="0" />
		<edge from-layer="182" from-port="0" to-layer="186" to-port="1" />
		<edge from-layer="183" from-port="0" to-layer="186" to-port="2" />
		<edge from-layer="184" from-port="0" to-layer="186" to-port="3" />
		<edge from-layer="185" from-port="0" to-layer="186" to-port="4" />
		<edge from-layer="186" from-port="5" to-layer="191" to-port="0" />
		<edge from-layer="186" from-port="5" to-layer="207" to-port="0" />
		<edge from-layer="186" from-port="5" to-layer="223" to-port="0" />
		<edge from-layer="187" from-port="0" to-layer="188" to-port="0" />
		<edge from-layer="188" from-port="1" to-layer="190" to-port="0" />
		<edge from-layer="189" from-port="0" to-layer="190" to-port="1" />
		<edge from-layer="190" from-port="2" to-layer="191" to-port="1" />
		<edge from-layer="191" from-port="2" to-layer="193" to-port="0" />
		<edge from-layer="192" from-port="0" to-layer="193" to-port="1" />
		<edge from-layer="193" from-port="2" to-layer="198" to-port="0" />
		<edge from-layer="194" from-port="0" to-layer="198" to-port="1" />
		<edge from-layer="195" from-port="0" to-layer="198" to-port="2" />
		<edge from-layer="196" from-port="0" to-layer="198" to-port="3" />
		<edge from-layer="197" from-port="0" to-layer="198" to-port="4" />
		<edge from-layer="198" from-port="5" to-layer="200" to-port="0" />
		<edge from-layer="199" from-port="0" to-layer="200" to-port="1" />
		<edge from-layer="200" from-port="2" to-layer="202" to-port="0" />
		<edge from-layer="201" from-port="0" to-layer="202" to-port="1" />
		<edge from-layer="202" from-port="2" to-layer="230" to-port="0" />
		<edge from-layer="203" from-port="0" to-layer="204" to-port="0" />
		<edge from-layer="204" from-port="1" to-layer="206" to-port="0" />
		<edge from-layer="205" from-port="0" to-layer="206" to-port="1" />
		<edge from-layer="206" from-port="2" to-layer="207" to-port="1" />
		<edge from-layer="207" from-port="2" to-layer="209" to-port="0" />
		<edge from-layer="208" from-port="0" to-layer="209" to-port="1" />
		<edge from-layer="209" from-port="2" to-layer="214" to-port="0" />
		<edge from-layer="210" from-port="0" to-layer="214" to-port="1" />
		<edge from-layer="211" from-port="0" to-layer="214" to-port="2" />
		<edge from-layer="212" from-port="0" to-layer="214" to-port="3" />
		<edge from-layer="213" from-port="0" to-layer="214" to-port="4" />
		<edge from-layer="214" from-port="5" to-layer="216" to-port="0" />
		<edge from-layer="215" from-port="0" to-layer="216" to-port="1" />
		<edge from-layer="216" from-port="2" to-layer="218" to-port="0" />
		<edge from-layer="217" from-port="0" to-layer="218" to-port="1" />
		<edge from-layer="218" from-port="2" to-layer="230" to-port="1" />
		<edge from-layer="219" from-port="0" to-layer="220" to-port="0" />
		<edge from-layer="220" from-port="1" to-layer="222" to-port="0" />
		<edge from-layer="221" from-port="0" to-layer="222" to-port="1" />
		<edge from-layer="222" from-port="2" to-layer="223" to-port="1" />
		<edge from-layer="223" from-port="2" to-layer="225" to-port="0" />
		<edge from-layer="224" from-port="0" to-layer="225" to-port="1" />
		<edge from-layer="225" from-port="2" to-layer="227" to-port="0" />
		<edge from-layer="226" from-port="0" to-layer="227" to-port="1" />
		<edge from-layer="227" from-port="2" to-layer="229" to-port="0" />
		<edge from-layer="228" from-port="0" to-layer="229" to-port="1" />
		<edge from-layer="229" from-port="2" to-layer="230" to-port="2" />
		<edge from-layer="230" from-port="4" to-layer="232" to-port="0" />
		<edge from-layer="231" from-port="0" to-layer="232" to-port="1" />
		<edge from-layer="232" from-port="2" to-layer="238" to-port="0" />
		<edge from-layer="233" from-port="1" to-layer="236" to-port="0" />
		<edge from-layer="234" from-port="0" to-layer="236" to-port="1" />
		<edge from-layer="235" from-port="0" to-layer="236" to-port="2" />
		<edge from-layer="236" from-port="3" to-layer="237" to-port="0" />
		<edge from-layer="237" from-port="2" to-layer="238" to-port="1" />
		<edge from-layer="238" from-port="2" to-layer="240" to-port="0" />
		<edge from-layer="239" from-port="0" to-layer="240" to-port="1" />
		<edge from-layer="240" from-port="2" to-layer="245" to-port="0" />
		<edge from-layer="241" from-port="0" to-layer="245" to-port="1" />
		<edge from-layer="242" from-port="0" to-layer="245" to-port="2" />
		<edge from-layer="243" from-port="0" to-layer="245" to-port="3" />
		<edge from-layer="244" from-port="0" to-layer="245" to-port="4" />
		<edge from-layer="245" from-port="5" to-layer="250" to-port="0" />
		<edge from-layer="246" from-port="0" to-layer="247" to-port="0" />
		<edge from-layer="247" from-port="1" to-layer="249" to-port="0" />
		<edge from-layer="248" from-port="0" to-layer="249" to-port="1" />
		<edge from-layer="249" from-port="2" to-layer="250" to-port="1" />
		<edge from-layer="250" from-port="2" to-layer="252" to-port="0" />
		<edge from-layer="251" from-port="0" to-layer="252" to-port="1" />
		<edge from-layer="252" from-port="2" to-layer="253" to-port="0" />
		<edge from-layer="253" from-port="2" to-layer="255" to-port="0" />
		<edge from-layer="254" from-port="0" to-layer="255" to-port="1" />
		<edge from-layer="255" from-port="2" to-layer="257" to-port="0" />
		<edge from-layer="256" from-port="0" to-layer="257" to-port="1" />
		<edge from-layer="257" from-port="2" to-layer="259" to-port="0" />
		<edge from-layer="258" from-port="0" to-layer="259" to-port="1" />
		<edge from-layer="259" from-port="2" to-layer="289" to-port="1" />
		<edge from-layer="259" from-port="2" to-layer="261" to-port="0" />
		<edge from-layer="260" from-port="0" to-layer="261" to-port="1" />
		<edge from-layer="261" from-port="2" to-layer="266" to-port="0" />
		<edge from-layer="262" from-port="0" to-layer="266" to-port="1" />
		<edge from-layer="263" from-port="0" to-layer="266" to-port="2" />
		<edge from-layer="264" from-port="0" to-layer="266" to-port="3" />
		<edge from-layer="265" from-port="0" to-layer="266" to-port="4" />
		<edge from-layer="266" from-port="5" to-layer="271" to-port="0" />
		<edge from-layer="267" from-port="0" to-layer="268" to-port="0" />
		<edge from-layer="268" from-port="1" to-layer="270" to-port="0" />
		<edge from-layer="269" from-port="0" to-layer="270" to-port="1" />
		<edge from-layer="270" from-port="2" to-layer="271" to-port="1" />
		<edge from-layer="271" from-port="2" to-layer="273" to-port="0" />
		<edge from-layer="272" from-port="0" to-layer="273" to-port="1" />
		<edge from-layer="273" from-port="2" to-layer="274" to-port="0" />
		<edge from-layer="274" from-port="1" to-layer="276" to-port="0" />
		<edge from-layer="275" from-port="0" to-layer="276" to-port="1" />
		<edge from-layer="276" from-port="2" to-layer="281" to-port="0" />
		<edge from-layer="277" from-port="0" to-layer="281" to-port="1" />
		<edge from-layer="278" from-port="0" to-layer="281" to-port="2" />
		<edge from-layer="279" from-port="0" to-layer="281" to-port="3" />
		<edge from-layer="280" from-port="0" to-layer="281" to-port="4" />
		<edge from-layer="281" from-port="5" to-layer="286" to-port="0" />
		<edge from-layer="282" from-port="0" to-layer="283" to-port="0" />
		<edge from-layer="283" from-port="1" to-layer="285" to-port="0" />
		<edge from-layer="284" from-port="0" to-layer="285" to-port="1" />
		<edge from-layer="285" from-port="2" to-layer="286" to-port="1" />
		<edge from-layer="286" from-port="2" to-layer="288" to-port="0" />
		<edge from-layer="287" from-port="0" to-layer="288" to-port="1" />
		<edge from-layer="288" from-port="2" to-layer="289" to-port="0" />
		<edge from-layer="289" from-port="2" to-layer="291" to-port="0" />
		<edge from-layer="290" from-port="0" to-layer="291" to-port="1" />
		<edge from-layer="291" from-port="2" to-layer="293" to-port="0" />
		<edge from-layer="292" from-port="0" to-layer="293" to-port="1" />
		<edge from-layer="293" from-port="2" to-layer="295" to-port="0" />
		<edge from-layer="294" from-port="0" to-layer="295" to-port="1" />
		<edge from-layer="295" from-port="2" to-layer="297" to-port="0" />
		<edge from-layer="295" from-port="2" to-layer="349" to-port="0" />
		<edge from-layer="295" from-port="2" to-layer="369" to-port="1" />
		<edge from-layer="296" from-port="0" to-layer="297" to-port="1" />
		<edge from-layer="297" from-port="2" to-layer="302" to-port="0" />
		<edge from-layer="298" from-port="0" to-layer="302" to-port="1" />
		<edge from-layer="299" from-port="0" to-layer="302" to-port="2" />
		<edge from-layer="300" from-port="0" to-layer="302" to-port="3" />
		<edge from-layer="301" from-port="0" to-layer="302" to-port="4" />
		<edge from-layer="302" from-port="5" to-layer="323" to-port="0" />
		<edge from-layer="302" from-port="5" to-layer="339" to-port="0" />
		<edge from-layer="302" from-port="5" to-layer="307" to-port="0" />
		<edge from-layer="303" from-port="0" to-layer="304" to-port="0" />
		<edge from-layer="304" from-port="1" to-layer="306" to-port="0" />
		<edge from-layer="305" from-port="0" to-layer="306" to-port="1" />
		<edge from-layer="306" from-port="2" to-layer="307" to-port="1" />
		<edge from-layer="307" from-port="2" to-layer="309" to-port="0" />
		<edge from-layer="308" from-port="0" to-layer="309" to-port="1" />
		<edge from-layer="309" from-port="2" to-layer="314" to-port="0" />
		<edge from-layer="310" from-port="0" to-layer="314" to-port="1" />
		<edge from-layer="311" from-port="0" to-layer="314" to-port="2" />
		<edge from-layer="312" from-port="0" to-layer="314" to-port="3" />
		<edge from-layer="313" from-port="0" to-layer="314" to-port="4" />
		<edge from-layer="314" from-port="5" to-layer="316" to-port="0" />
		<edge from-layer="315" from-port="0" to-layer="316" to-port="1" />
		<edge from-layer="316" from-port="2" to-layer="318" to-port="0" />
		<edge from-layer="317" from-port="0" to-layer="318" to-port="1" />
		<edge from-layer="318" from-port="2" to-layer="346" to-port="0" />
		<edge from-layer="319" from-port="0" to-layer="320" to-port="0" />
		<edge from-layer="320" from-port="1" to-layer="322" to-port="0" />
		<edge from-layer="321" from-port="0" to-layer="322" to-port="1" />
		<edge from-layer="322" from-port="2" to-layer="323" to-port="1" />
		<edge from-layer="323" from-port="2" to-layer="325" to-port="0" />
		<edge from-layer="324" from-port="0" to-layer="325" to-port="1" />
		<edge from-layer="325" from-port="2" to-layer="330" to-port="0" />
		<edge from-layer="326" from-port="0" to-layer="330" to-port="1" />
		<edge from-layer="327" from-port="0" to-layer="330" to-port="2" />
		<edge from-layer="328" from-port="0" to-layer="330" to-port="3" />
		<edge from-layer="329" from-port="0" to-layer="330" to-port="4" />
		<edge from-layer="330" from-port="5" to-layer="332" to-port="0" />
		<edge from-layer="331" from-port="0" to-layer="332" to-port="1" />
		<edge from-layer="332" from-port="2" to-layer="334" to-port="0" />
		<edge from-layer="333" from-port="0" to-layer="334" to-port="1" />
		<edge from-layer="334" from-port="2" to-layer="346" to-port="1" />
		<edge from-layer="335" from-port="0" to-layer="336" to-port="0" />
		<edge from-layer="336" from-port="1" to-layer="338" to-port="0" />
		<edge from-layer="337" from-port="0" to-layer="338" to-port="1" />
		<edge from-layer="338" from-port="2" to-layer="339" to-port="1" />
		<edge from-layer="339" from-port="2" to-layer="341" to-port="0" />
		<edge from-layer="340" from-port="0" to-layer="341" to-port="1" />
		<edge from-layer="341" from-port="2" to-layer="343" to-port="0" />
		<edge from-layer="342" from-port="0" to-layer="343" to-port="1" />
		<edge from-layer="343" from-port="2" to-layer="345" to-port="0" />
		<edge from-layer="344" from-port="0" to-layer="345" to-port="1" />
		<edge from-layer="345" from-port="2" to-layer="346" to-port="2" />
		<edge from-layer="346" from-port="4" to-layer="348" to-port="0" />
		<edge from-layer="347" from-port="0" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="354" to-port="0" />
		<edge from-layer="349" from-port="1" to-layer="352" to-port="0" />
		<edge from-layer="350" from-port="0" to-layer="352" to-port="1" />
		<edge from-layer="351" from-port="0" to-layer="352" to-port="2" />
		<edge from-layer="352" from-port="3" to-layer="353" to-port="0" />
		<edge from-layer="353" from-port="2" to-layer="354" to-port="1" />
		<edge from-layer="354" from-port="2" to-layer="356" to-port="0" />
		<edge from-layer="355" from-port="0" to-layer="356" to-port="1" />
		<edge from-layer="356" from-port="2" to-layer="361" to-port="0" />
		<edge from-layer="357" from-port="0" to-layer="361" to-port="1" />
		<edge from-layer="358" from-port="0" to-layer="361" to-port="2" />
		<edge from-layer="359" from-port="0" to-layer="361" to-port="3" />
		<edge from-layer="360" from-port="0" to-layer="361" to-port="4" />
		<edge from-layer="361" from-port="5" to-layer="366" to-port="0" />
		<edge from-layer="362" from-port="0" to-layer="363" to-port="0" />
		<edge from-layer="363" from-port="1" to-layer="365" to-port="0" />
		<edge from-layer="364" from-port="0" to-layer="365" to-port="1" />
		<edge from-layer="365" from-port="2" to-layer="366" to-port="1" />
		<edge from-layer="366" from-port="2" to-layer="368" to-port="0" />
		<edge from-layer="367" from-port="0" to-layer="368" to-port="1" />
		<edge from-layer="368" from-port="2" to-layer="369" to-port="0" />
		<edge from-layer="369" from-port="2" to-layer="371" to-port="0" />
		<edge from-layer="370" from-port="0" to-layer="371" to-port="1" />
		<edge from-layer="371" from-port="2" to-layer="373" to-port="0" />
		<edge from-layer="372" from-port="0" to-layer="373" to-port="1" />
		<edge from-layer="373" from-port="2" to-layer="375" to-port="0" />
		<edge from-layer="374" from-port="0" to-layer="375" to-port="1" />
		<edge from-layer="375" from-port="2" to-layer="405" to-port="1" />
		<edge from-layer="375" from-port="2" to-layer="377" to-port="0" />
		<edge from-layer="376" from-port="0" to-layer="377" to-port="1" />
		<edge from-layer="377" from-port="2" to-layer="382" to-port="0" />
		<edge from-layer="378" from-port="0" to-layer="382" to-port="1" />
		<edge from-layer="379" from-port="0" to-layer="382" to-port="2" />
		<edge from-layer="380" from-port="0" to-layer="382" to-port="3" />
		<edge from-layer="381" from-port="0" to-layer="382" to-port="4" />
		<edge from-layer="382" from-port="5" to-layer="387" to-port="0" />
		<edge from-layer="383" from-port="0" to-layer="384" to-port="0" />
		<edge from-layer="384" from-port="1" to-layer="386" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="386" to-port="1" />
		<edge from-layer="386" from-port="2" to-layer="387" to-port="1" />
		<edge from-layer="387" from-port="2" to-layer="389" to-port="0" />
		<edge from-layer="388" from-port="0" to-layer="389" to-port="1" />
		<edge from-layer="389" from-port="2" to-layer="390" to-port="0" />
		<edge from-layer="390" from-port="1" to-layer="392" to-port="0" />
		<edge from-layer="391" from-port="0" to-layer="392" to-port="1" />
		<edge from-layer="392" from-port="2" to-layer="397" to-port="0" />
		<edge from-layer="393" from-port="0" to-layer="397" to-port="1" />
		<edge from-layer="394" from-port="0" to-layer="397" to-port="2" />
		<edge from-layer="395" from-port="0" to-layer="397" to-port="3" />
		<edge from-layer="396" from-port="0" to-layer="397" to-port="4" />
		<edge from-layer="397" from-port="5" to-layer="402" to-port="0" />
		<edge from-layer="398" from-port="0" to-layer="399" to-port="0" />
		<edge from-layer="399" from-port="1" to-layer="401" to-port="0" />
		<edge from-layer="400" from-port="0" to-layer="401" to-port="1" />
		<edge from-layer="401" from-port="2" to-layer="402" to-port="1" />
		<edge from-layer="402" from-port="2" to-layer="404" to-port="0" />
		<edge from-layer="403" from-port="0" to-layer="404" to-port="1" />
		<edge from-layer="404" from-port="2" to-layer="405" to-port="0" />
		<edge from-layer="405" from-port="2" to-layer="407" to-port="0" />
		<edge from-layer="406" from-port="0" to-layer="407" to-port="1" />
		<edge from-layer="407" from-port="2" to-layer="409" to-port="0" />
		<edge from-layer="408" from-port="0" to-layer="409" to-port="1" />
		<edge from-layer="409" from-port="2" to-layer="411" to-port="0" />
		<edge from-layer="410" from-port="0" to-layer="411" to-port="1" />
		<edge from-layer="411" from-port="2" to-layer="413" to-port="0" />
		<edge from-layer="411" from-port="2" to-layer="465" to-port="0" />
		<edge from-layer="411" from-port="2" to-layer="485" to-port="1" />
		<edge from-layer="412" from-port="0" to-layer="413" to-port="1" />
		<edge from-layer="413" from-port="2" to-layer="418" to-port="0" />
		<edge from-layer="414" from-port="0" to-layer="418" to-port="1" />
		<edge from-layer="415" from-port="0" to-layer="418" to-port="2" />
		<edge from-layer="416" from-port="0" to-layer="418" to-port="3" />
		<edge from-layer="417" from-port="0" to-layer="418" to-port="4" />
		<edge from-layer="418" from-port="5" to-layer="423" to-port="0" />
		<edge from-layer="418" from-port="5" to-layer="439" to-port="0" />
		<edge from-layer="418" from-port="5" to-layer="455" to-port="0" />
		<edge from-layer="419" from-port="0" to-layer="420" to-port="0" />
		<edge from-layer="420" from-port="1" to-layer="422" to-port="0" />
		<edge from-layer="421" from-port="0" to-layer="422" to-port="1" />
		<edge from-layer="422" from-port="2" to-layer="423" to-port="1" />
		<edge from-layer="423" from-port="2" to-layer="425" to-port="0" />
		<edge from-layer="424" from-port="0" to-layer="425" to-port="1" />
		<edge from-layer="425" from-port="2" to-layer="430" to-port="0" />
		<edge from-layer="426" from-port="0" to-layer="430" to-port="1" />
		<edge from-layer="427" from-port="0" to-layer="430" to-port="2" />
		<edge from-layer="428" from-port="0" to-layer="430" to-port="3" />
		<edge from-layer="429" from-port="0" to-layer="430" to-port="4" />
		<edge from-layer="430" from-port="5" to-layer="432" to-port="0" />
		<edge from-layer="431" from-port="0" to-layer="432" to-port="1" />
		<edge from-layer="432" from-port="2" to-layer="434" to-port="0" />
		<edge from-layer="433" from-port="0" to-layer="434" to-port="1" />
		<edge from-layer="434" from-port="2" to-layer="462" to-port="0" />
		<edge from-layer="435" from-port="0" to-layer="436" to-port="0" />
		<edge from-layer="436" from-port="1" to-layer="438" to-port="0" />
		<edge from-layer="437" from-port="0" to-layer="438" to-port="1" />
		<edge from-layer="438" from-port="2" to-layer="439" to-port="1" />
		<edge from-layer="439" from-port="2" to-layer="441" to-port="0" />
		<edge from-layer="440" from-port="0" to-layer="441" to-port="1" />
		<edge from-layer="441" from-port="2" to-layer="446" to-port="0" />
		<edge from-layer="442" from-port="0" to-layer="446" to-port="1" />
		<edge from-layer="443" from-port="0" to-layer="446" to-port="2" />
		<edge from-layer="444" from-port="0" to-layer="446" to-port="3" />
		<edge from-layer="445" from-port="0" to-layer="446" to-port="4" />
		<edge from-layer="446" from-port="5" to-layer="448" to-port="0" />
		<edge from-layer="447" from-port="0" to-layer="448" to-port="1" />
		<edge from-layer="448" from-port="2" to-layer="450" to-port="0" />
		<edge from-layer="449" from-port="0" to-layer="450" to-port="1" />
		<edge from-layer="450" from-port="2" to-layer="462" to-port="1" />
		<edge from-layer="451" from-port="0" to-layer="452" to-port="0" />
		<edge from-layer="452" from-port="1" to-layer="454" to-port="0" />
		<edge from-layer="453" from-port="0" to-layer="454" to-port="1" />
		<edge from-layer="454" from-port="2" to-layer="455" to-port="1" />
		<edge from-layer="455" from-port="2" to-layer="457" to-port="0" />
		<edge from-layer="456" from-port="0" to-layer="457" to-port="1" />
		<edge from-layer="457" from-port="2" to-layer="459" to-port="0" />
		<edge from-layer="458" from-port="0" to-layer="459" to-port="1" />
		<edge from-layer="459" from-port="2" to-layer="461" to-port="0" />
		<edge from-layer="460" from-port="0" to-layer="461" to-port="1" />
		<edge from-layer="461" from-port="2" to-layer="462" to-port="2" />
		<edge from-layer="462" from-port="4" to-layer="464" to-port="0" />
		<edge from-layer="463" from-port="0" to-layer="464" to-port="1" />
		<edge from-layer="464" from-port="2" to-layer="470" to-port="0" />
		<edge from-layer="465" from-port="1" to-layer="468" to-port="0" />
		<edge from-layer="466" from-port="0" to-layer="468" to-port="1" />
		<edge from-layer="467" from-port="0" to-layer="468" to-port="2" />
		<edge from-layer="468" from-port="3" to-layer="469" to-port="0" />
		<edge from-layer="469" from-port="2" to-layer="470" to-port="1" />
		<edge from-layer="470" from-port="2" to-layer="472" to-port="0" />
		<edge from-layer="471" from-port="0" to-layer="472" to-port="1" />
		<edge from-layer="472" from-port="2" to-layer="477" to-port="0" />
		<edge from-layer="473" from-port="0" to-layer="477" to-port="1" />
		<edge from-layer="474" from-port="0" to-layer="477" to-port="2" />
		<edge from-layer="475" from-port="0" to-layer="477" to-port="3" />
		<edge from-layer="476" from-port="0" to-layer="477" to-port="4" />
		<edge from-layer="477" from-port="5" to-layer="482" to-port="0" />
		<edge from-layer="478" from-port="0" to-layer="479" to-port="0" />
		<edge from-layer="479" from-port="1" to-layer="481" to-port="0" />
		<edge from-layer="480" from-port="0" to-layer="481" to-port="1" />
		<edge from-layer="481" from-port="2" to-layer="482" to-port="1" />
		<edge from-layer="482" from-port="2" to-layer="484" to-port="0" />
		<edge from-layer="483" from-port="0" to-layer="484" to-port="1" />
		<edge from-layer="484" from-port="2" to-layer="485" to-port="0" />
		<edge from-layer="485" from-port="2" to-layer="487" to-port="0" />
		<edge from-layer="486" from-port="0" to-layer="487" to-port="1" />
		<edge from-layer="487" from-port="2" to-layer="489" to-port="0" />
		<edge from-layer="488" from-port="0" to-layer="489" to-port="1" />
		<edge from-layer="489" from-port="2" to-layer="491" to-port="0" />
		<edge from-layer="490" from-port="0" to-layer="491" to-port="1" />
		<edge from-layer="491" from-port="2" to-layer="493" to-port="0" />
		<edge from-layer="491" from-port="2" to-layer="521" to-port="1" />
		<edge from-layer="492" from-port="0" to-layer="493" to-port="1" />
		<edge from-layer="493" from-port="2" to-layer="498" to-port="0" />
		<edge from-layer="494" from-port="0" to-layer="498" to-port="1" />
		<edge from-layer="495" from-port="0" to-layer="498" to-port="2" />
		<edge from-layer="496" from-port="0" to-layer="498" to-port="3" />
		<edge from-layer="497" from-port="0" to-layer="498" to-port="4" />
		<edge from-layer="498" from-port="5" to-layer="503" to-port="0" />
		<edge from-layer="499" from-port="0" to-layer="500" to-port="0" />
		<edge from-layer="500" from-port="1" to-layer="502" to-port="0" />
		<edge from-layer="501" from-port="0" to-layer="502" to-port="1" />
		<edge from-layer="502" from-port="2" to-layer="503" to-port="1" />
		<edge from-layer="503" from-port="2" to-layer="505" to-port="0" />
		<edge from-layer="504" from-port="0" to-layer="505" to-port="1" />
		<edge from-layer="505" from-port="2" to-layer="506" to-port="0" />
		<edge from-layer="506" from-port="1" to-layer="508" to-port="0" />
		<edge from-layer="507" from-port="0" to-layer="508" to-port="1" />
		<edge from-layer="508" from-port="2" to-layer="513" to-port="0" />
		<edge from-layer="509" from-port="0" to-layer="513" to-port="1" />
		<edge from-layer="510" from-port="0" to-layer="513" to-port="2" />
		<edge from-layer="511" from-port="0" to-layer="513" to-port="3" />
		<edge from-layer="512" from-port="0" to-layer="513" to-port="4" />
		<edge from-layer="513" from-port="5" to-layer="518" to-port="0" />
		<edge from-layer="514" from-port="0" to-layer="515" to-port="0" />
		<edge from-layer="515" from-port="1" to-layer="517" to-port="0" />
		<edge from-layer="516" from-port="0" to-layer="517" to-port="1" />
		<edge from-layer="517" from-port="2" to-layer="518" to-port="1" />
		<edge from-layer="518" from-port="2" to-layer="520" to-port="0" />
		<edge from-layer="519" from-port="0" to-layer="520" to-port="1" />
		<edge from-layer="520" from-port="2" to-layer="521" to-port="0" />
		<edge from-layer="521" from-port="2" to-layer="523" to-port="0" />
		<edge from-layer="522" from-port="0" to-layer="523" to-port="1" />
		<edge from-layer="523" from-port="2" to-layer="525" to-port="0" />
		<edge from-layer="524" from-port="0" to-layer="525" to-port="1" />
		<edge from-layer="525" from-port="2" to-layer="527" to-port="0" />
		<edge from-layer="526" from-port="0" to-layer="527" to-port="1" />
		<edge from-layer="527" from-port="2" to-layer="529" to-port="0" />
		<edge from-layer="527" from-port="2" to-layer="581" to-port="0" />
		<edge from-layer="527" from-port="2" to-layer="601" to-port="1" />
		<edge from-layer="528" from-port="0" to-layer="529" to-port="1" />
		<edge from-layer="529" from-port="2" to-layer="534" to-port="0" />
		<edge from-layer="530" from-port="0" to-layer="534" to-port="1" />
		<edge from-layer="531" from-port="0" to-layer="534" to-port="2" />
		<edge from-layer="532" from-port="0" to-layer="534" to-port="3" />
		<edge from-layer="533" from-port="0" to-layer="534" to-port="4" />
		<edge from-layer="534" from-port="5" to-layer="539" to-port="0" />
		<edge from-layer="534" from-port="5" to-layer="571" to-port="0" />
		<edge from-layer="534" from-port="5" to-layer="555" to-port="0" />
		<edge from-layer="535" from-port="0" to-layer="536" to-port="0" />
		<edge from-layer="536" from-port="1" to-layer="538" to-port="0" />
		<edge from-layer="537" from-port="0" to-layer="538" to-port="1" />
		<edge from-layer="538" from-port="2" to-layer="539" to-port="1" />
		<edge from-layer="539" from-port="2" to-layer="541" to-port="0" />
		<edge from-layer="540" from-port="0" to-layer="541" to-port="1" />
		<edge from-layer="541" from-port="2" to-layer="546" to-port="0" />
		<edge from-layer="542" from-port="0" to-layer="546" to-port="1" />
		<edge from-layer="543" from-port="0" to-layer="546" to-port="2" />
		<edge from-layer="544" from-port="0" to-layer="546" to-port="3" />
		<edge from-layer="545" from-port="0" to-layer="546" to-port="4" />
		<edge from-layer="546" from-port="5" to-layer="548" to-port="0" />
		<edge from-layer="547" from-port="0" to-layer="548" to-port="1" />
		<edge from-layer="548" from-port="2" to-layer="550" to-port="0" />
		<edge from-layer="549" from-port="0" to-layer="550" to-port="1" />
		<edge from-layer="550" from-port="2" to-layer="578" to-port="0" />
		<edge from-layer="551" from-port="0" to-layer="552" to-port="0" />
		<edge from-layer="552" from-port="1" to-layer="554" to-port="0" />
		<edge from-layer="553" from-port="0" to-layer="554" to-port="1" />
		<edge from-layer="554" from-port="2" to-layer="555" to-port="1" />
		<edge from-layer="555" from-port="2" to-layer="557" to-port="0" />
		<edge from-layer="556" from-port="0" to-layer="557" to-port="1" />
		<edge from-layer="557" from-port="2" to-layer="562" to-port="0" />
		<edge from-layer="558" from-port="0" to-layer="562" to-port="1" />
		<edge from-layer="559" from-port="0" to-layer="562" to-port="2" />
		<edge from-layer="560" from-port="0" to-layer="562" to-port="3" />
		<edge from-layer="561" from-port="0" to-layer="562" to-port="4" />
		<edge from-layer="562" from-port="5" to-layer="564" to-port="0" />
		<edge from-layer="563" from-port="0" to-layer="564" to-port="1" />
		<edge from-layer="564" from-port="2" to-layer="566" to-port="0" />
		<edge from-layer="565" from-port="0" to-layer="566" to-port="1" />
		<edge from-layer="566" from-port="2" to-layer="578" to-port="1" />
		<edge from-layer="567" from-port="0" to-layer="568" to-port="0" />
		<edge from-layer="568" from-port="1" to-layer="570" to-port="0" />
		<edge from-layer="569" from-port="0" to-layer="570" to-port="1" />
		<edge from-layer="570" from-port="2" to-layer="571" to-port="1" />
		<edge from-layer="571" from-port="2" to-layer="573" to-port="0" />
		<edge from-layer="572" from-port="0" to-layer="573" to-port="1" />
		<edge from-layer="573" from-port="2" to-layer="575" to-port="0" />
		<edge from-layer="574" from-port="0" to-layer="575" to-port="1" />
		<edge from-layer="575" from-port="2" to-layer="577" to-port="0" />
		<edge from-layer="576" from-port="0" to-layer="577" to-port="1" />
		<edge from-layer="577" from-port="2" to-layer="578" to-port="2" />
		<edge from-layer="578" from-port="4" to-layer="580" to-port="0" />
		<edge from-layer="579" from-port="0" to-layer="580" to-port="1" />
		<edge from-layer="580" from-port="2" to-layer="586" to-port="0" />
		<edge from-layer="581" from-port="1" to-layer="584" to-port="0" />
		<edge from-layer="582" from-port="0" to-layer="584" to-port="1" />
		<edge from-layer="583" from-port="0" to-layer="584" to-port="2" />
		<edge from-layer="584" from-port="3" to-layer="585" to-port="0" />
		<edge from-layer="585" from-port="2" to-layer="586" to-port="1" />
		<edge from-layer="586" from-port="2" to-layer="588" to-port="0" />
		<edge from-layer="587" from-port="0" to-layer="588" to-port="1" />
		<edge from-layer="588" from-port="2" to-layer="593" to-port="0" />
		<edge from-layer="589" from-port="0" to-layer="593" to-port="1" />
		<edge from-layer="590" from-port="0" to-layer="593" to-port="2" />
		<edge from-layer="591" from-port="0" to-layer="593" to-port="3" />
		<edge from-layer="592" from-port="0" to-layer="593" to-port="4" />
		<edge from-layer="593" from-port="5" to-layer="598" to-port="0" />
		<edge from-layer="594" from-port="0" to-layer="595" to-port="0" />
		<edge from-layer="595" from-port="1" to-layer="597" to-port="0" />
		<edge from-layer="596" from-port="0" to-layer="597" to-port="1" />
		<edge from-layer="597" from-port="2" to-layer="598" to-port="1" />
		<edge from-layer="598" from-port="2" to-layer="600" to-port="0" />
		<edge from-layer="599" from-port="0" to-layer="600" to-port="1" />
		<edge from-layer="600" from-port="2" to-layer="601" to-port="0" />
		<edge from-layer="601" from-port="2" to-layer="603" to-port="0" />
		<edge from-layer="602" from-port="0" to-layer="603" to-port="1" />
		<edge from-layer="603" from-port="2" to-layer="605" to-port="0" />
		<edge from-layer="604" from-port="0" to-layer="605" to-port="1" />
		<edge from-layer="605" from-port="2" to-layer="607" to-port="0" />
		<edge from-layer="606" from-port="0" to-layer="607" to-port="1" />
		<edge from-layer="607" from-port="2" to-layer="609" to-port="0" />
		<edge from-layer="607" from-port="2" to-layer="637" to-port="1" />
		<edge from-layer="608" from-port="0" to-layer="609" to-port="1" />
		<edge from-layer="609" from-port="2" to-layer="614" to-port="0" />
		<edge from-layer="610" from-port="0" to-layer="614" to-port="1" />
		<edge from-layer="611" from-port="0" to-layer="614" to-port="2" />
		<edge from-layer="612" from-port="0" to-layer="614" to-port="3" />
		<edge from-layer="613" from-port="0" to-layer="614" to-port="4" />
		<edge from-layer="614" from-port="5" to-layer="619" to-port="0" />
		<edge from-layer="615" from-port="0" to-layer="616" to-port="0" />
		<edge from-layer="616" from-port="1" to-layer="618" to-port="0" />
		<edge from-layer="617" from-port="0" to-layer="618" to-port="1" />
		<edge from-layer="618" from-port="2" to-layer="619" to-port="1" />
		<edge from-layer="619" from-port="2" to-layer="621" to-port="0" />
		<edge from-layer="620" from-port="0" to-layer="621" to-port="1" />
		<edge from-layer="621" from-port="2" to-layer="622" to-port="0" />
		<edge from-layer="622" from-port="1" to-layer="624" to-port="0" />
		<edge from-layer="623" from-port="0" to-layer="624" to-port="1" />
		<edge from-layer="624" from-port="2" to-layer="629" to-port="0" />
		<edge from-layer="625" from-port="0" to-layer="629" to-port="1" />
		<edge from-layer="626" from-port="0" to-layer="629" to-port="2" />
		<edge from-layer="627" from-port="0" to-layer="629" to-port="3" />
		<edge from-layer="628" from-port="0" to-layer="629" to-port="4" />
		<edge from-layer="629" from-port="5" to-layer="634" to-port="0" />
		<edge from-layer="630" from-port="0" to-layer="631" to-port="0" />
		<edge from-layer="631" from-port="1" to-layer="633" to-port="0" />
		<edge from-layer="632" from-port="0" to-layer="633" to-port="1" />
		<edge from-layer="633" from-port="2" to-layer="634" to-port="1" />
		<edge from-layer="634" from-port="2" to-layer="636" to-port="0" />
		<edge from-layer="635" from-port="0" to-layer="636" to-port="1" />
		<edge from-layer="636" from-port="2" to-layer="637" to-port="0" />
		<edge from-layer="637" from-port="2" to-layer="639" to-port="0" />
		<edge from-layer="638" from-port="0" to-layer="639" to-port="1" />
		<edge from-layer="639" from-port="2" to-layer="641" to-port="0" />
		<edge from-layer="640" from-port="0" to-layer="641" to-port="1" />
		<edge from-layer="641" from-port="2" to-layer="643" to-port="0" />
		<edge from-layer="642" from-port="0" to-layer="643" to-port="1" />
		<edge from-layer="643" from-port="2" to-layer="645" to-port="0" />
		<edge from-layer="643" from-port="2" to-layer="717" to-port="1" />
		<edge from-layer="643" from-port="2" to-layer="697" to-port="0" />
		<edge from-layer="644" from-port="0" to-layer="645" to-port="1" />
		<edge from-layer="645" from-port="2" to-layer="650" to-port="0" />
		<edge from-layer="646" from-port="0" to-layer="650" to-port="1" />
		<edge from-layer="647" from-port="0" to-layer="650" to-port="2" />
		<edge from-layer="648" from-port="0" to-layer="650" to-port="3" />
		<edge from-layer="649" from-port="0" to-layer="650" to-port="4" />
		<edge from-layer="650" from-port="5" to-layer="655" to-port="0" />
		<edge from-layer="650" from-port="5" to-layer="687" to-port="0" />
		<edge from-layer="650" from-port="5" to-layer="671" to-port="0" />
		<edge from-layer="651" from-port="0" to-layer="652" to-port="0" />
		<edge from-layer="652" from-port="1" to-layer="654" to-port="0" />
		<edge from-layer="653" from-port="0" to-layer="654" to-port="1" />
		<edge from-layer="654" from-port="2" to-layer="655" to-port="1" />
		<edge from-layer="655" from-port="2" to-layer="657" to-port="0" />
		<edge from-layer="656" from-port="0" to-layer="657" to-port="1" />
		<edge from-layer="657" from-port="2" to-layer="662" to-port="0" />
		<edge from-layer="658" from-port="0" to-layer="662" to-port="1" />
		<edge from-layer="659" from-port="0" to-layer="662" to-port="2" />
		<edge from-layer="660" from-port="0" to-layer="662" to-port="3" />
		<edge from-layer="661" from-port="0" to-layer="662" to-port="4" />
		<edge from-layer="662" from-port="5" to-layer="664" to-port="0" />
		<edge from-layer="663" from-port="0" to-layer="664" to-port="1" />
		<edge from-layer="664" from-port="2" to-layer="666" to-port="0" />
		<edge from-layer="665" from-port="0" to-layer="666" to-port="1" />
		<edge from-layer="666" from-port="2" to-layer="694" to-port="0" />
		<edge from-layer="667" from-port="0" to-layer="668" to-port="0" />
		<edge from-layer="668" from-port="1" to-layer="670" to-port="0" />
		<edge from-layer="669" from-port="0" to-layer="670" to-port="1" />
		<edge from-layer="670" from-port="2" to-layer="671" to-port="1" />
		<edge from-layer="671" from-port="2" to-layer="673" to-port="0" />
		<edge from-layer="672" from-port="0" to-layer="673" to-port="1" />
		<edge from-layer="673" from-port="2" to-layer="678" to-port="0" />
		<edge from-layer="674" from-port="0" to-layer="678" to-port="1" />
		<edge from-layer="675" from-port="0" to-layer="678" to-port="2" />
		<edge from-layer="676" from-port="0" to-layer="678" to-port="3" />
		<edge from-layer="677" from-port="0" to-layer="678" to-port="4" />
		<edge from-layer="678" from-port="5" to-layer="680" to-port="0" />
		<edge from-layer="679" from-port="0" to-layer="680" to-port="1" />
		<edge from-layer="680" from-port="2" to-layer="682" to-port="0" />
		<edge from-layer="681" from-port="0" to-layer="682" to-port="1" />
		<edge from-layer="682" from-port="2" to-layer="694" to-port="1" />
		<edge from-layer="683" from-port="0" to-layer="684" to-port="0" />
		<edge from-layer="684" from-port="1" to-layer="686" to-port="0" />
		<edge from-layer="685" from-port="0" to-layer="686" to-port="1" />
		<edge from-layer="686" from-port="2" to-layer="687" to-port="1" />
		<edge from-layer="687" from-port="2" to-layer="689" to-port="0" />
		<edge from-layer="688" from-port="0" to-layer="689" to-port="1" />
		<edge from-layer="689" from-port="2" to-layer="691" to-port="0" />
		<edge from-layer="690" from-port="0" to-layer="691" to-port="1" />
		<edge from-layer="691" from-port="2" to-layer="693" to-port="0" />
		<edge from-layer="692" from-port="0" to-layer="693" to-port="1" />
		<edge from-layer="693" from-port="2" to-layer="694" to-port="2" />
		<edge from-layer="694" from-port="4" to-layer="696" to-port="0" />
		<edge from-layer="695" from-port="0" to-layer="696" to-port="1" />
		<edge from-layer="696" from-port="2" to-layer="702" to-port="0" />
		<edge from-layer="697" from-port="1" to-layer="700" to-port="0" />
		<edge from-layer="698" from-port="0" to-layer="700" to-port="1" />
		<edge from-layer="699" from-port="0" to-layer="700" to-port="2" />
		<edge from-layer="700" from-port="3" to-layer="701" to-port="0" />
		<edge from-layer="701" from-port="2" to-layer="702" to-port="1" />
		<edge from-layer="702" from-port="2" to-layer="704" to-port="0" />
		<edge from-layer="703" from-port="0" to-layer="704" to-port="1" />
		<edge from-layer="704" from-port="2" to-layer="709" to-port="0" />
		<edge from-layer="705" from-port="0" to-layer="709" to-port="1" />
		<edge from-layer="706" from-port="0" to-layer="709" to-port="2" />
		<edge from-layer="707" from-port="0" to-layer="709" to-port="3" />
		<edge from-layer="708" from-port="0" to-layer="709" to-port="4" />
		<edge from-layer="709" from-port="5" to-layer="714" to-port="0" />
		<edge from-layer="710" from-port="0" to-layer="711" to-port="0" />
		<edge from-layer="711" from-port="1" to-layer="713" to-port="0" />
		<edge from-layer="712" from-port="0" to-layer="713" to-port="1" />
		<edge from-layer="713" from-port="2" to-layer="714" to-port="1" />
		<edge from-layer="714" from-port="2" to-layer="716" to-port="0" />
		<edge from-layer="715" from-port="0" to-layer="716" to-port="1" />
		<edge from-layer="716" from-port="2" to-layer="717" to-port="0" />
		<edge from-layer="717" from-port="2" to-layer="719" to-port="0" />
		<edge from-layer="718" from-port="0" to-layer="719" to-port="1" />
		<edge from-layer="719" from-port="2" to-layer="721" to-port="0" />
		<edge from-layer="720" from-port="0" to-layer="721" to-port="1" />
		<edge from-layer="721" from-port="2" to-layer="723" to-port="0" />
		<edge from-layer="722" from-port="0" to-layer="723" to-port="1" />
		<edge from-layer="723" from-port="2" to-layer="725" to-port="0" />
		<edge from-layer="723" from-port="2" to-layer="753" to-port="1" />
		<edge from-layer="724" from-port="0" to-layer="725" to-port="1" />
		<edge from-layer="725" from-port="2" to-layer="730" to-port="0" />
		<edge from-layer="726" from-port="0" to-layer="730" to-port="1" />
		<edge from-layer="727" from-port="0" to-layer="730" to-port="2" />
		<edge from-layer="728" from-port="0" to-layer="730" to-port="3" />
		<edge from-layer="729" from-port="0" to-layer="730" to-port="4" />
		<edge from-layer="730" from-port="5" to-layer="735" to-port="0" />
		<edge from-layer="731" from-port="0" to-layer="732" to-port="0" />
		<edge from-layer="732" from-port="1" to-layer="734" to-port="0" />
		<edge from-layer="733" from-port="0" to-layer="734" to-port="1" />
		<edge from-layer="734" from-port="2" to-layer="735" to-port="1" />
		<edge from-layer="735" from-port="2" to-layer="737" to-port="0" />
		<edge from-layer="736" from-port="0" to-layer="737" to-port="1" />
		<edge from-layer="737" from-port="2" to-layer="738" to-port="0" />
		<edge from-layer="738" from-port="1" to-layer="740" to-port="0" />
		<edge from-layer="739" from-port="0" to-layer="740" to-port="1" />
		<edge from-layer="740" from-port="2" to-layer="745" to-port="0" />
		<edge from-layer="741" from-port="0" to-layer="745" to-port="1" />
		<edge from-layer="742" from-port="0" to-layer="745" to-port="2" />
		<edge from-layer="743" from-port="0" to-layer="745" to-port="3" />
		<edge from-layer="744" from-port="0" to-layer="745" to-port="4" />
		<edge from-layer="745" from-port="5" to-layer="750" to-port="0" />
		<edge from-layer="746" from-port="0" to-layer="747" to-port="0" />
		<edge from-layer="747" from-port="1" to-layer="749" to-port="0" />
		<edge from-layer="748" from-port="0" to-layer="749" to-port="1" />
		<edge from-layer="749" from-port="2" to-layer="750" to-port="1" />
		<edge from-layer="750" from-port="2" to-layer="752" to-port="0" />
		<edge from-layer="751" from-port="0" to-layer="752" to-port="1" />
		<edge from-layer="752" from-port="2" to-layer="753" to-port="0" />
		<edge from-layer="753" from-port="2" to-layer="755" to-port="0" />
		<edge from-layer="754" from-port="0" to-layer="755" to-port="1" />
		<edge from-layer="755" from-port="2" to-layer="757" to-port="0" />
		<edge from-layer="756" from-port="0" to-layer="757" to-port="1" />
		<edge from-layer="757" from-port="2" to-layer="759" to-port="0" />
		<edge from-layer="758" from-port="0" to-layer="759" to-port="1" />
		<edge from-layer="759" from-port="2" to-layer="760" to-port="0" />
	</edges>
	<rt_info>
		<Runtime_version value="2024.4.1-16618-643f23d1318-releases/2024/4" />
		<conversion_parameters>
			<framework value="pytorch" />
			<is_python_object value="True" />
		</conversion_parameters>
		<nncf>
			<friendly_names_were_updated value="True" />
			<quantization>
				<advanced_parameters value="{'overflow_fix': 'disable', 'quantize_outputs': False, 'inplace_statistics': True, 'disable_channel_alignment': True, 'disable_bias_correction': False, 'batchwise_statistics': None, 'activations_quantization_params': None, 'weights_quantization_params': None, 'activations_range_estimator_params': {'min': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}, 'max': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}}, 'weights_range_estimator_params': {'min': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}, 'max': {'statistics_type': None, 'aggregator_type': None, 'clipping_value': None, 'quantile_outlier_prob': 0.0001}}, 'bias_correction_params': {'apply_for_all_nodes': False, 'threshold': None}, 'smooth_quant_alphas': {'convolution': -1, 'matmul': 0.95}, 'smooth_quant_alpha': None, 'backend_params': {}}" />
				<fast_bias_correction value="True" />
				<ignored_scope>
					<types value="['GroupNormalization']" />
				</ignored_scope>
				<model_type value="transformer" />
				<preset value="mixed" />
				<subset_size value="300" />
				<target_device value="ANY" />
			</quantization>
		</nncf>
		<optimum>
			<optimum_intel_version value="1.20.0.dev0+b31524c" />
			<optimum_version value="1.23.0" />
			<pytorch_version value="2.5.0.dev20240807+cu121" />
			<transformers_version value="4.43.4" />
		</optimum>
	</rt_info>
</net>
