#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用户管理API端点
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    DatabaseException, ErrorCode
)
from app.models.user import User
from app.schemas.auth import UserResponse, UserUpdateRequest
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.get("/profile", response_model=UserResponse, summary="获取用户档案")
async def get_user_profile(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    获取当前用户的完整档案信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    # 记录获取用户档案日志
    log_business_operation(
        logger=logger.logger,
        operation="get_user_profile",
        details={
            "user_id": user_id,
            "nickname": current_user.nickname
        },
        user_id=user_id,
        request_id=request_id
    )
    
    return UserResponse.model_validate(current_user)


@router.put("/profile", response_model=UserResponse, summary="更新用户档案")
async def update_user_profile(
    update_data: UserUpdateRequest,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    更新用户档案信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 记录更新前的信息
        update_fields = []
        
        # 更新用户信息
        if update_data.nickname is not None:
            current_user.nickname = update_data.nickname
            update_fields.append("nickname")
        if update_data.avatar_url is not None:
            current_user.avatar_url = update_data.avatar_url
            update_fields.append("avatar_url")
        if update_data.settings is not None:
            current_user.settings = update_data.settings
            update_fields.append("settings")
        
        await session.commit()
        await session.refresh(current_user)
        
        # 记录更新用户档案日志
        log_business_operation(
            logger=logger.logger,
            operation="update_user_profile",
            details={
                "user_id": user_id,
                "updated_fields": update_fields,
                "nickname": current_user.nickname
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return UserResponse.model_validate(current_user)
        
    except Exception as e:
        await session.rollback()
        logger.error(f"更新用户档案失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'update_user_profile'
        })
        raise DatabaseException(
            message="更新用户档案失败",
            details={"user_id": user_id}
        )


@router.get("/quota", summary="获取用户配额信息")
async def get_user_quota(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    获取用户配额使用情况
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    # 记录获取配额信息日志
    log_business_operation(
        logger=logger.logger,
        operation="get_user_quota",
        details={
            "user_id": user_id,
            "quota_used": current_user.quota_used,
            "quota_limit": current_user.quota_limit,
            "is_vip": current_user.is_vip
        },
        user_id=user_id,
        request_id=request_id
    )
    
    return {
        "quota_used": current_user.quota_used,
        "quota_limit": current_user.quota_limit,
        "quota_remaining": current_user.quota_limit - current_user.quota_used,
        "is_vip": current_user.is_vip
    }


@router.delete("/account", summary="删除账户")
async def delete_user_account(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    删除用户账户及所有相关数据
    
    注意：此操作不可逆
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 记录删除前的用户信息
        user_info = {
            "user_id": user_id,
            "nickname": current_user.nickname,
            "wx_openid": current_user.wx_openid
        }
        
        await session.delete(current_user)
        await session.commit()
        
        # 记录删除账户日志
        log_business_operation(
            logger=logger.logger,
            operation="delete_user_account",
            details=user_info,
            user_id=user_id,
            request_id=request_id
        )
        
        return {"message": "账户已成功删除"}
        
    except Exception as e:
        await session.rollback()
        logger.error(f"删除账户失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'delete_user_account'
        })
        raise DatabaseException(
            message="删除账户失败",
            details={"user_id": user_id}
        ) 
