#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库服务管理工具
用于检查和启动PostgreSQL服务
"""
import os
import subprocess
import socket
import time


def check_postgresql_port():
    """检查PostgreSQL端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(3)
            result = sock.connect_ex(('127.0.0.1', 5432))
            return result == 0
    except Exception:
        return False


def find_postgresql_services():
    """查找系统中的PostgreSQL服务"""
    services = []
    
    if os.name == 'nt':  # Windows
        try:
            # 查询所有服务
            result = subprocess.run('sc query type= service state= all', shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'SERVICE_NAME:' in line and 'postgresql' in line.lower():
                        service_name = line.split(':')[1].strip()
                        services.append(service_name)
        except Exception as e:
            print(f"查找PostgreSQL服务时出错: {e}")
    
    return services


def get_service_status(service_name):
    """获取服务状态"""
    try:
        result = subprocess.run(f'sc query {service_name}', shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            if 'RUNNING' in result.stdout:
                return 'RUNNING'
            elif 'STOPPED' in result.stdout:
                return 'STOPPED'
            else:
                return 'UNKNOWN'
        else:
            return 'NOT_FOUND'
    except Exception:
        return 'ERROR'


def start_postgresql_service(service_name):
    """启动PostgreSQL服务"""
    try:
        print(f"正在启动服务: {service_name}")
        result = subprocess.run(f'sc start {service_name}', shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"服务 {service_name} 启动成功")
            return True
        else:
            print(f"服务 {service_name} 启动失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"启动服务时出错: {e}")
        return False


def check_and_start_postgresql():
    """检查并启动PostgreSQL服务"""
    print("检查PostgreSQL服务状态...")

    # 首先检查端口
    if check_postgresql_port():
        print("PostgreSQL服务运行正常 (端口5432可访问)")
        return True

    print("PostgreSQL端口5432不可访问")
    
    # 查找PostgreSQL服务
    services = find_postgresql_services()
    
    if not services:
        print("未找到PostgreSQL服务")
        print("请确保PostgreSQL已正确安装")
        return False
    
    print(f"找到PostgreSQL服务: {services}")
    
    # 检查服务状态并尝试启动
    for service_name in services:
        status = get_service_status(service_name)
        print(f"服务 {service_name} 状态: {status}")
        
        if status == 'RUNNING':
            print(f"服务 {service_name} 已在运行，但端口不可访问")
            continue
        elif status == 'STOPPED':
            if start_postgresql_service(service_name):
                # 等待服务启动
                print("等待服务完全启动...")
                for i in range(10):
                    time.sleep(1)
                    if check_postgresql_port():
                        print("PostgreSQL服务启动成功，端口可访问")
                        return True
                    print(f"等待中... ({i+1}/10)")

                print("服务已启动但端口仍不可访问")
            else:
                print(f"无法启动服务 {service_name}")
    
    return False


def install_postgresql_guide():
    """显示PostgreSQL安装指南"""
    print("\nPostgreSQL安装指南:")
    print("=" * 50)
    print("1. 下载PostgreSQL:")
    print("   https://www.postgresql.org/download/windows/")
    print("\n2. 安装时请注意:")
    print("   - 记住设置的密码")
    print("   - 确保端口设置为5432")
    print("   - 勾选'Launch Stack Builder at exit'")
    print("\n3. 安装完成后:")
    print("   - 确保PostgreSQL服务已启动")
    print("   - 创建项目所需的数据库")
    print("\n4. 配置环境变量:")
    print("   - 将PostgreSQL的bin目录添加到PATH")
    print("   - 例如: C:\\Program Files\\PostgreSQL\\14\\bin")


def main():
    """主函数"""
    print("PostgreSQL服务管理工具")
    print("=" * 40)
    
    if check_and_start_postgresql():
        print("\nPostgreSQL服务检查完成，可以正常使用")

        # 显示连接信息
        print("\n连接信息:")
        print("  主机: localhost")
        print("  端口: 5432")
        print("  用户: postgres (默认)")

    else:
        print("\nPostgreSQL服务检查失败")
        
        # 检查是否安装了PostgreSQL
        try:
            result = subprocess.run('where psql', shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                print("\n未检测到PostgreSQL安装")
                install_postgresql_guide()
            else:
                print(f"\n检测到PostgreSQL安装: {result.stdout.strip()}")
                print("但服务可能未正确配置或启动")
        except Exception:
            print("\n无法检测PostgreSQL安装状态")
            install_postgresql_guide()

        print("\n故障排除建议:")
        print("1. 以管理员身份运行此脚本")
        print("2. 检查Windows服务管理器中的PostgreSQL服务")
        print("3. 重新安装PostgreSQL")
        print("4. 检查防火墙设置")


if __name__ == "__main__":
    main()
