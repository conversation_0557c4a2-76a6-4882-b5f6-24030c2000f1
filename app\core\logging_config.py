#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一日志配置模块
"""
import os
import sys
import json
import logging
import logging.config
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path
from pythonjsonlogger import jsonlogger
import uuid

from app.core.config import settings


class ContextFilter(logging.Filter):
    """上下文过滤器 - 添加请求追踪信息"""
    
    def filter(self, record):
        # 添加请求ID（可以从上下文变量中获取）
        if not hasattr(record, 'request_id'):
            record.request_id = getattr(record, 'request_id', str(uuid.uuid4())[:8])
        
        # 添加用户ID（如果有的话）
        if not hasattr(record, 'user_id'):
            record.user_id = getattr(record, 'user_id', 'anonymous')
            
        # 添加操作类型
        if not hasattr(record, 'operation'):
            record.operation = getattr(record, 'operation', 'unknown')
            
        return True


class SensitiveDataFilter(logging.Filter):
    """敏感数据过滤器 - 防止敏感信息泄露"""
    
    SENSITIVE_KEYS = {
        'password', 'token', 'api_key', 'secret', 'authorization',
        'access_token', 'refresh_token', 'jwt', 'openid', 'private_key'
    }
    
    def filter(self, record):
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            record.msg = self._mask_sensitive_data(record.msg)
        
        if hasattr(record, 'args') and record.args:
            record.args = tuple(
                self._mask_sensitive_data(str(arg)) if isinstance(arg, (str, dict)) else arg
                for arg in record.args
            )
        
        return True
    
    def _mask_sensitive_data(self, data: str) -> str:
        """屏蔽敏感数据"""
        if isinstance(data, dict):
            return self._mask_dict(data)
        
        # 简单的字符串屏蔽
        for key in self.SENSITIVE_KEYS:
            if key.lower() in data.lower():
                # 这里可以实现更复杂的屏蔽逻辑
                pass
        return data
    
    def _mask_dict(self, data: dict) -> dict:
        """屏蔽字典中的敏感数据"""
        masked = {}
        for key, value in data.items():
            if key.lower() in self.SENSITIVE_KEYS:
                masked[key] = "***MASKED***"
            elif isinstance(value, dict):
                masked[key] = self._mask_dict(value)
            else:
                masked[key] = value
        return masked


class CustomJSONFormatter(jsonlogger.JsonFormatter):
    """自定义JSON格式化器"""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]):
        super().add_fields(log_record, record, message_dict)
        
        # 添加时间戳
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        # 添加日志级别
        log_record['level'] = record.levelname
        
        # 添加模块信息
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # 添加应用信息
        log_record['service'] = settings.PROJECT_NAME
        log_record['version'] = settings.VERSION
        log_record['environment'] = settings.ENVIRONMENT


def setup_logging():
    """设置日志配置"""
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 日志配置
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)d] [%(request_id)s] [%(user_id)s] %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "[%(asctime)s] [%(levelname)s] [%(name)s:%(lineno)d] [%(request_id)s] [%(user_id)s] [%(operation)s] %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": CustomJSONFormatter,
                "format": "%(timestamp)s %(level)s %(name)s %(message)s"
            }
        },
        "filters": {
            "context_filter": {
                "()": ContextFilter
            },
            "sensitive_filter": {
                "()": SensitiveDataFilter
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "standard",
                "filters": ["context_filter", "sensitive_filter"],
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "detailed",
                "filters": ["context_filter", "sensitive_filter"],
                "filename": log_dir / "app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "ERROR",
                "formatter": "json",
                "filters": ["context_filter", "sensitive_filter"],
                "filename": log_dir / "error.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            },
            "access_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filters": ["context_filter", "sensitive_filter"],
                "filename": log_dir / "access.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 10,
                "encoding": "utf8"
            }
        },
        "loggers": {
            # 应用日志
            "app": {
                "handlers": ["console", "file", "error_file"],
                "level": settings.LOG_LEVEL,
                "propagate": False
            },
            # API访问日志
            "uvicorn.access": {
                "handlers": ["access_file"],
                "level": "INFO",
                "propagate": False
            },
            # 数据库日志
            "sqlalchemy.engine": {
                "handlers": ["file"],
                "level": "WARNING",
                "propagate": False
            },
            # Celery日志
            "celery": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False
            },
            # 第三方库日志
            "httpx": {
                "handlers": ["file"],
                "level": "WARNING",
                "propagate": False
            },
            "openai": {
                "handlers": ["file"],
                "level": "WARNING",
                "propagate": False
            }
        },
        "root": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console", "file"]
        }
    }
    
    # 应用日志配置
    logging.config.dictConfig(logging_config)
    
    # 设置全局日志级别
    if settings.DEBUG:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def get_logger(name: str, **context) -> logging.LoggerAdapter:
    """获取带上下文的日志记录器"""
    logger = logging.getLogger(name)
    return logging.LoggerAdapter(logger, context)


def log_api_call(logger: logging.Logger, method: str, path: str, status_code: int, 
                duration: float, user_id: Optional[str] = None, request_id: Optional[str] = None):
    """记录API调用日志"""
    extra = {
        'request_id': request_id or str(uuid.uuid4())[:8],
        'user_id': user_id or 'anonymous',
        'operation': 'api_call'
    }
    
    logger.info(
        f"API调用 - {method} {path} - 状态码: {status_code} - 耗时: {duration:.3f}s",
        extra=extra
    )


def log_business_operation(logger: logging.Logger, operation: str, details: dict, 
                         user_id: Optional[str] = None, request_id: Optional[str] = None):
    """记录业务操作日志"""
    extra = {
        'request_id': request_id or str(uuid.uuid4())[:8],
        'user_id': user_id or 'anonymous',
        'operation': operation
    }
    
    logger.info(f"业务操作 - {operation} - 详情: {details}", extra=extra)


def log_error(logger: logging.Logger, error: Exception, context: dict = None, 
              user_id: Optional[str] = None, request_id: Optional[str] = None):
    """记录错误日志"""
    extra = {
        'request_id': request_id or str(uuid.uuid4())[:8],
        'user_id': user_id or 'anonymous',
        'operation': 'error'
    }
    
    error_details = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'context': context or {}
    }
    
    logger.error(f"错误发生 - {error_details}", extra=extra, exc_info=True)


# 初始化日志配置
setup_logging() 