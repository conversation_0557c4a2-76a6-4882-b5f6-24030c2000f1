# AI小说生成器 - 数据库迁移和LLM配置完整指南

## 📋 概览

本指南将详细介绍如何完成AI小说生成器后端系统的数据库迁移和LLM服务配置，确保系统能够正常运行并提供AI生成功能。

---

## 🔧 环境准备

### 1. 虚拟环境激活

首先激活Python虚拟环境：

```powershell
python -m venv novel_ai_venv 
# Windows PowerShell
novel_ai_venv\Scripts\Activate.ps1

# 或者
.\novel_ai_venv\Scripts\Activate.ps1
```

验证激活成功：
```powershell
python --version
pip --version
```

### 2. 依赖安装

确保安装了所有必要的依赖：

```powershell
pip install -r app/requirements.txt
```

---

## 📄 环境配置

### 1. 创建环境配置文件

将项目根目录的 `env.example` 文件复制为 `.env`：

```powershell
Copy-Item env.example .env
```

### 2. 配置必要参数

编辑 `.env` 文件，修改以下关键配置：

#### 🔑 必须配置的参数

```bash
# 数据库密码 - 改为强密码
POSTGRES_PASSWORD=your_strong_database_password

# JWT密钥 - 必须是32字符以上的随机字符串
SECRET_KEY=your_super_secret_jwt_key_at_least_32_characters_long

# LLM服务配置 - 选择其中一种
LLM_PROVIDER=openai
LLM_API_KEY=sk-your-actual-openai-api-key
```

#### 📱 微信小程序配置（可选）

```bash
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

### 3. LLM服务提供商配置

根据你的AI服务提供商，选择对应的配置：

#### Option A: OpenAI 配置
```bash
LLM_PROVIDER=openai
LLM_API_KEY=sk-your-openai-api-key
LLM_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-3.5-turbo
```

#### Option B: Azure OpenAI 配置
```bash
LLM_PROVIDER=azure
LLM_API_KEY=your-azure-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_API_VERSION=2024-02-15-preview
LLM_MODEL=gpt-35-turbo
```

#### Option C: 阿里云通义千问配置
```bash
LLM_PROVIDER=dashscope
DASHSCOPE_API_KEY=sk-your-dashscope-api-key
LLM_MODEL=qwen-turbo
LLM_BASE_URL=https://dashscope.aliyuncs.com/api/v1
```

---

## 🗄️ 数据库部署

### 1. 启动基础服务

使用Docker启动PostgreSQL和Redis：

```powershell
# 启动数据库和缓存服务
docker-compose up -d postgres redis

# 检查服务状态
docker-compose ps
```

### 2. 验证服务连接

检查PostgreSQL连接：
```powershell
# 测试数据库连接
docker exec -it <postgres_container_name> psql -U novel_user -d ai_novel_generator -c "SELECT version();"
```

检查Redis连接：
```powershell
# 测试Redis连接
docker exec -it <redis_container_name> redis-cli ping
```

---

## 🚀 数据库初始化和迁移

### 方法一：使用初始化脚本（推荐新部署）

```powershell
# 初始化数据库表结构
python scripts/init_database.py
```

这个脚本会：
- 创建所有必要的数据库表
- 设置正确的外键关系
- 验证表结构创建成功

### 方法二：使用Alembic迁移（推荐已有数据）

```powershell
# 初始化Alembic迁移环境（仅首次需要）
alembic -c app/alembic.ini init app/migrations

# 应用所有迁移
alembic -c app/alembic.ini upgrade head
```

### 方法三：使用智能迁移脚本（推荐P0修复后）

针对P0问题修复后的Novel字段更新：

```powershell
# 运行智能迁移脚本
python scripts/migrate_database.py
```

这个脚本会：
- 检查字段是否已存在，避免重复操作
- 安全地添加缺失的字段（description, target_length, style_settings）
- 为现有数据设置合理的默认值
- 验证迁移结果

### 4. 验证迁移结果

```powershell
# 验证表结构
python -c "
import asyncio
from app.core.database import engine
from sqlalchemy import text

async def check_tables():
    async with engine.begin() as conn:
        result = await conn.execute(text(\"\"\"
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema='public'
            ORDER BY table_name
        \"\"\"))
        tables = result.fetchall()
        print('数据库表:')
        for table in tables:
            print(f'  - {table[0]}')

asyncio.run(check_tables())
"
```

---

## 🤖 LLM服务配置验证

### 1. 测试LLM连接

创建测试脚本 `test_llm.py`：

```python
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import llm_service, initialize_default_llm_service

async def test_llm():
    """测试LLM服务配置"""
    try:
        # 初始化LLM服务
        success = await initialize_default_llm_service()
        if not success:
            print("❌ LLM服务初始化失败")
            return
        
        print("✅ LLM服务初始化成功")
        
        # 测试简单调用
        test_prompt = "请说'Hello, AI小说生成器!'"
        response = await llm_service.invoke_with_retry(
            prompt=test_prompt,
            adapter_name="default",
            request_id="test_001"
        )
        
        print(f"✅ LLM测试调用成功: {response}")
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_llm())
```

运行测试：
```powershell
python test_llm.py
```

### 2. 通过API测试LLM配置

启动API服务后，可以通过以下端点测试：

```powershell
# 获取可用的LLM提供商
curl -X GET "http://localhost:8000/api/v1/llm-configs/providers/available"

# 测试LLM配置（需要认证）
curl -X POST "http://localhost:8000/api/v1/llm-configs/test" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "openai",
    "api_key": "your-api-key",
    "base_url": "https://api.openai.com/v1",
    "model": "gpt-3.5-turbo"
  }'
```

---

## 🚀 启动服务

### 1. 启动API服务

```powershell
# 启动FastAPI服务
python scripts/start_api.py
```

### 2. 启动Celery Worker

新开一个PowerShell终端：

```powershell
# 激活虚拟环境
novel_ai_venv\Scripts\Activate.ps1

# 启动Celery Worker
python scripts/start_worker_windows.py
```

### 3. 验证服务状态

检查健康状态：
```powershell
curl http://localhost:8000/health
```

查看API文档：
```
http://localhost:8000/docs
```

---

## 🔍 故障排除

### 数据库连接问题

#### 问题：连接被拒绝
```
sqlalchemy.exc.OperationalError: connection to server was refused
```

**解决方案：**
1. 检查PostgreSQL是否运行：`docker-compose ps postgres`
2. 检查端口是否被占用：`netstat -an | findstr :5432`
3. 重启数据库服务：`docker-compose restart postgres`

#### 问题：认证失败
```
sqlalchemy.exc.OperationalError: FATAL: password authentication failed
```

**解决方案：**
1. 检查.env文件中的数据库密码
2. 重新创建PostgreSQL容器：
   ```powershell
   docker-compose down postgres
   docker-compose up -d postgres
   ```

### LLM服务问题

#### 问题：API密钥无效
```
LLMServiceException: Invalid API key
```

**解决方案：**
1. 检查.env文件中的`LLM_API_KEY`是否正确
2. 验证API密钥是否有效：
   ```powershell
   # OpenAI测试
   curl -H "Authorization: Bearer sk-your-key" https://api.openai.com/v1/models
   ```

#### 问题：模型不存在
```
LLMServiceException: Model not found
```

**解决方案：**
1. 检查模型名称是否正确（如OpenAI使用`gpt-3.5-turbo`，Azure使用`gpt-35-turbo`）
2. 验证你的账户是否有权限访问该模型

#### 问题：网络连接超时
```
LLMServiceException: Request timeout
```

**解决方案：**
1. 检查网络连接
2. 调整超时设置：在.env中增加`LLM_TIMEOUT=900`
3. 检查是否需要代理设置

### Redis连接问题

#### 问题：Redis连接失败
```
redis.exceptions.ConnectionError: Error connecting to Redis
```

**解决方案：**
1. 检查Redis服务：`docker-compose ps redis`
2. 重启Redis：`docker-compose restart redis`
3. 检查端口占用：`netstat -an | findstr :6379`

### Celery Worker问题

#### 问题：Worker无法启动
```
kombu.exceptions.OperationalError: Error connecting to broker
```

**解决方案：**
1. 确保Redis服务正常运行
2. 检查CELERY_BROKER_URL配置
3. 重启Redis服务

---

## 📊 验证清单

完成配置后，使用以下清单验证系统状态：

### ✅ 基础服务检查
- [ ] PostgreSQL容器运行正常
- [ ] Redis容器运行正常
- [ ] 数据库连接测试通过
- [ ] Redis连接测试通过

### ✅ 数据库迁移检查
- [ ] 所有数据库表创建成功
- [ ] Novel表包含新字段（description, target_length, style_settings）
- [ ] 外键关系正确建立
- [ ] 迁移脚本执行无错误

### ✅ LLM服务检查
- [ ] LLM_API_KEY配置正确
- [ ] LLM服务提供商配置正确
- [ ] LLM测试调用成功
- [ ] 生成任务能正常创建

### ✅ API服务检查
- [ ] FastAPI服务启动成功
- [ ] 健康检查接口响应正常
- [ ] Swagger文档可访问（http://localhost:8000/docs）
- [ ] 认证接口工作正常

### ✅ 任务队列检查
- [ ] Celery Worker启动成功
- [ ] 任务队列连接正常
- [ ] 可以创建和执行异步任务

---

## 🎯 快速启动脚本

创建一键启动脚本 `start_system.ps1`：

```powershell
# 一键启动脚本
Write-Host "🚀 启动AI小说生成器后端系统" -ForegroundColor Green

# 1. 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& ".\novel_ai_venv\Scripts\Activate.ps1"

# 2. 启动基础服务
Write-Host "启动数据库和Redis..." -ForegroundColor Yellow
docker-compose up -d postgres redis

# 3. 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 4. 运行数据库迁移（如果需要）
Write-Host "运行数据库迁移..." -ForegroundColor Yellow
python scripts/migrate_database.py

# 5. 启动API服务
Write-Host "启动API服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command python scripts/start_api.py"

# 6. 启动Worker
Write-Host "启动Celery Worker..." -ForegroundColor Yellow
Start-Sleep -Seconds 5
Start-Process powershell -ArgumentList "-Command python scripts/start_worker_windows.py"

Write-Host "✅ 系统启动完成！" -ForegroundColor Green
Write-Host "API文档: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "健康检查: http://localhost:8000/health" -ForegroundColor Cyan
```

使用方法：
```powershell
.\start_system.ps1
```

---

## 📞 获取帮助

如果在配置过程中遇到问题：

1. **检查日志文件**：
   - API日志：`logs/app.log`
   - 错误日志：`logs/error.log`

2. **常用调试命令**：
   ```powershell
   # 检查Docker服务
   docker-compose ps
   
   # 查看容器日志
   docker-compose logs postgres
   docker-compose logs redis
   
   # 检查端口占用
   netstat -an | findstr :8000
   netstat -an | findstr :5432
   netstat -an | findstr :6379
   ```

3. **重置环境**：
   ```powershell
   # 重置数据库
   docker-compose down postgres
   docker volume rm ai_novelgenerator_postgres_data
   docker-compose up -d postgres
   
   # 重新初始化
   python scripts/init_database.py
   ```

---

**配置完成时间**：根据网络情况，整个配置过程大约需要10-30分钟  
**文档版本**：v1.0  
**最后更新**：2025年6月19日 