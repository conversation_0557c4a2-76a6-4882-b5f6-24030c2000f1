# ====================================================================
# AI小说生成器后端系统 - 一键启动脚本
# 
# 功能：
# 1. 激活虚拟环境
# 2. 启动Docker服务（PostgreSQL + Redis）
# 3. 运行数据库迁移
# 4. 启动FastAPI服务
# 5. 启动Celery Worker
# ====================================================================

Write-Host "🚀 AI小说生成器后端系统启动脚本" -ForegroundColor Green
Write-Host "作者: AI Assistant | 版本: v1.0" -ForegroundColor Gray
Write-Host "=============================================" -ForegroundColor Gray

# 询问部署模式
Write-Host "`n📋 请选择部署模式：" -ForegroundColor Cyan
Write-Host "1. 混合模式 - Docker基础服务 + 本地应用 (推荐开发)" -ForegroundColor White
Write-Host "2. 完整容器化 - 所有服务在Docker中运行 (推荐生产)" -ForegroundColor White
Write-Host "3. 仅基础服务 - 只启动数据库和Redis" -ForegroundColor White
$deployMode = Read-Host "请输入选择 (1-3，默认1)"

if ([string]::IsNullOrWhiteSpace($deployMode)) {
    $deployMode = "1"
}

# 检查当前目录是否正确
if (-not (Test-Path "app\main.py")) {
    Write-Host "❌ 错误：请在项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "当前目录：$(Get-Location)" -ForegroundColor Yellow
    exit 1
}

# 1. 激活虚拟环境
Write-Host "`n🔧 步骤1：激活Python虚拟环境..." -ForegroundColor Yellow
if (Test-Path "novel_ai_venv\Scripts\Activate.ps1") {
    & ".\novel_ai_venv\Scripts\Activate.ps1"
    Write-Host "✅ 虚拟环境激活成功" -ForegroundColor Green
} else {
    Write-Host "❌ 虚拟环境不存在，请先创建虚拟环境：" -ForegroundColor Red
    Write-Host "python -m venv novel_ai_venv" -ForegroundColor Yellow
    Write-Host "novel_ai_venv\Scripts\Activate.ps1" -ForegroundColor Yellow
    Write-Host "pip install -r app\requirements.txt" -ForegroundColor Yellow
    exit 1
}

# 2. 检查环境配置
Write-Host "`n📄 步骤2：检查环境配置..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "❌ 环境配置文件 .env 不存在" -ForegroundColor Red
    if (Test-Path "env.example") {
        Write-Host "复制示例配置文件..." -ForegroundColor Yellow
        Copy-Item "env.example" ".env"
        Write-Host "✅ 已创建 .env 文件，请修改其中的配置项" -ForegroundColor Green
        Write-Host "重要：请修改以下配置项后重新运行脚本：" -ForegroundColor Cyan
        Write-Host "  - POSTGRES_PASSWORD" -ForegroundColor White
        Write-Host "  - SECRET_KEY" -ForegroundColor White
        Write-Host "  - LLM_API_KEY" -ForegroundColor White
        exit 1
    } else {
        Write-Host "❌ 请先创建 .env 配置文件" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ 环境配置文件存在" -ForegroundColor Green
}

# 3. 检查Docker是否运行
Write-Host "`n🐳 步骤3：检查Docker服务..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker服务运行正常" -ForegroundColor Green
    } else {
        Write-Host "❌ Docker服务未运行，请启动Docker Desktop" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Docker未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 4. 启动基础服务
Write-Host "`n🗄️ 步骤4：启动数据库和Redis服务..." -ForegroundColor Yellow
docker-compose up -d postgres redis

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 数据库和Redis启动成功" -ForegroundColor Green
} else {
    Write-Host "❌ 服务启动失败，请检查docker-compose.yml配置" -ForegroundColor Red
    exit 1
}

# 5. 等待服务启动
Write-Host "`n⏳ 步骤5：等待服务完全启动..." -ForegroundColor Yellow
for ($i = 10; $i -gt 0; $i--) {
    Write-Host "倒计时: $i 秒" -ForegroundColor Gray
    Start-Sleep -Seconds 1
}

# 6. 检查服务状态
Write-Host "`n🔍 步骤6：检查服务状态..." -ForegroundColor Yellow
$services = docker-compose ps --services --filter status=running
if ($services -contains "postgres" -and $services -contains "redis") {
    Write-Host "✅ PostgreSQL和Redis服务运行正常" -ForegroundColor Green
} else {
    Write-Host "❌ 部分服务启动失败" -ForegroundColor Red
    docker-compose ps
    exit 1
}

# 7. 运行数据库迁移
Write-Host "`n🔄 步骤7：运行数据库迁移..." -ForegroundColor Yellow
try {
    if (Test-Path "scripts\migrate_database.py") {
        python scripts\migrate_database.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 数据库迁移完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 数据库迁移失败，尝试初始化..." -ForegroundColor Yellow
            python scripts\init_database.py
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 数据库初始化完成" -ForegroundColor Green
            } else {
                Write-Host "❌ 数据库初始化失败" -ForegroundColor Red
                exit 1
            }
        }
    } else {
        Write-Host "⚠️ 迁移脚本不存在，运行数据库初始化..." -ForegroundColor Yellow
        python scripts\init_database.py
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 数据库初始化完成" -ForegroundColor Green
        } else {
            Write-Host "❌ 数据库初始化失败" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "❌ 数据库操作失败：$_" -ForegroundColor Red
    exit 1
}

# 8. 根据部署模式启动应用服务
if ($deployMode -eq "1") {
    # 混合模式：本地应用
    Write-Host "`n🌐 步骤8：启动调试系统（API + Worker）..." -ForegroundColor Yellow
    Write-Host "选择启动方式：" -ForegroundColor Cyan
    Write-Host "1. 统一启动脚本 (推荐调试)" -ForegroundColor White
    Write-Host "2. 分别启动服务 (传统方式)" -ForegroundColor White
    $startMode = Read-Host "请输入选择 (1-2，默认1)"
    
    if ([string]::IsNullOrWhiteSpace($startMode) -or $startMode -eq "1") {
        # 使用统一启动脚本
        Write-Host "使用统一调试启动脚本..." -ForegroundColor Yellow
        $debugJob = Start-Job -ScriptBlock {
            Set-Location $using:PWD
            & ".\novel_ai_venv\Scripts\Activate.ps1"
            python scripts\start_debug_system.py
        }
        
        # 等待服务启动
        Start-Sleep -Seconds 8
        
        # 检查API服务是否启动成功
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ 统一调试系统启动成功" -ForegroundColor Green
            } else {
                Write-Host "⚠️ 系统正在启动中..." -ForegroundColor Yellow
            }
        } catch {
            Write-Host "⚠️ 系统正在启动中..." -ForegroundColor Yellow
        }
        
    } else {
        # 分别启动服务（传统方式）
        Write-Host "使用分别启动方式..." -ForegroundColor Yellow
        
        # 启动API服务
        $apiJob = Start-Job -ScriptBlock {
            Set-Location $using:PWD
            & ".\novel_ai_venv\Scripts\Activate.ps1"
            python scripts\start_api.py
        }

        # 等待API服务启动
        Start-Sleep -Seconds 5

        # 检查API服务是否启动成功
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 10
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ FastAPI服务启动成功" -ForegroundColor Green
            } else {
                Write-Host "⚠️ API服务响应异常" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "⚠️ API服务正在启动中..." -ForegroundColor Yellow
        }

        # 启动Celery Worker
        Write-Host "`n⚙️ 步骤9：启动本地Celery Worker..." -ForegroundColor Yellow
        $workerJob = Start-Job -ScriptBlock {
            Set-Location $using:PWD
            & ".\novel_ai_venv\Scripts\Activate.ps1"
            python scripts\start_worker_windows.py
        }

        Start-Sleep -Seconds 3
        Write-Host "✅ Celery Worker启动成功" -ForegroundColor Green
    }

} elseif ($deployMode -eq "2") {
    # 完整容器化模式
    Write-Host "`n🐳 步骤8：启动容器化应用服务..." -ForegroundColor Yellow
    docker-compose --profile app up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 容器化服务启动成功" -ForegroundColor Green
        
        # 等待服务启动
        Write-Host "等待容器启动..." -ForegroundColor Gray
        Start-Sleep -Seconds 10
        
        # 检查API服务
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -Method GET -TimeoutSec 15
            if ($response.StatusCode -eq 200) {
                Write-Host "✅ 容器化API服务健康检查通过" -ForegroundColor Green
            }
        } catch {
            Write-Host "⚠️ API服务正在启动中..." -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ 容器化服务启动失败" -ForegroundColor Red
        exit 1
    }

} else {
    # 仅基础服务模式
    Write-Host "`n✅ 仅基础服务模式，应用服务需要手动启动" -ForegroundColor Green
    Write-Host "启动应用服务命令：" -ForegroundColor Cyan
    Write-Host "  python scripts\start_api.py" -ForegroundColor White
    Write-Host "  python scripts\start_worker_windows.py" -ForegroundColor White
}

# 10. 显示系统状态
Write-Host "`n🎉 系统启动完成！" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Gray

Write-Host "`n📊 服务状态：" -ForegroundColor Cyan
Write-Host "  🗄️ PostgreSQL: ✅ 运行中 (localhost:5432)" -ForegroundColor White
Write-Host "  🔄 Redis: ✅ 运行中 (localhost:6379)" -ForegroundColor White

if ($deployMode -eq "1") {
    if ($apiJob) {
        Write-Host "  🌐 FastAPI: ✅ 本地运行 (http://localhost:8000)" -ForegroundColor White
    }
    if ($workerJob) {
        Write-Host "  ⚙️ Celery Worker: ✅ 本地运行" -ForegroundColor White
    }
} elseif ($deployMode -eq "2") {
    Write-Host "  🌐 FastAPI: ✅ 容器运行 (http://localhost:8000)" -ForegroundColor White
    Write-Host "  ⚙️ Celery Worker: ✅ 容器运行" -ForegroundColor White
    Write-Host "  📅 Celery Beat: ✅ 容器运行" -ForegroundColor White
} else {
    Write-Host "  🌐 FastAPI: ⏸️ 需要手动启动" -ForegroundColor Yellow
    Write-Host "  ⚙️ Celery Worker: ⏸️ 需要手动启动" -ForegroundColor Yellow
}

Write-Host "`n🔗 重要链接：" -ForegroundColor Cyan
Write-Host "  📚 API文档: http://localhost:8000/docs" -ForegroundColor White
Write-Host "  💓 健康检查: http://localhost:8000/health" -ForegroundColor White
Write-Host "  📖 接口文档: http://localhost:8000/redoc" -ForegroundColor White

if ($deployMode -eq "2") {
    Write-Host "`n🐳 Docker管理命令：" -ForegroundColor Cyan
    Write-Host "  查看容器状态: docker-compose ps" -ForegroundColor White
    Write-Host "  查看API日志: docker-compose logs api" -ForegroundColor White
    Write-Host "  查看Worker日志: docker-compose logs celery_worker" -ForegroundColor White
    Write-Host "  重启应用: docker-compose restart api celery_worker" -ForegroundColor White
    Write-Host "  停止所有服务: docker-compose down" -ForegroundColor White
} else {
    Write-Host "`n📝 管理命令：" -ForegroundColor Cyan
    Write-Host "  查看API日志: Get-Content logs\app.log -Tail 50 -Wait" -ForegroundColor White
    Write-Host "  查看服务状态: docker-compose ps" -ForegroundColor White
    Write-Host "  停止Docker服务: docker-compose down" -ForegroundColor White
    if ($deployMode -eq "1") {
        Write-Host "  查看后台任务: Get-Job" -ForegroundColor White
        Write-Host "  停止后台任务: Get-Job | Stop-Job" -ForegroundColor White
    }
}

Write-Host "`n⚠️ 重要提醒：" -ForegroundColor Yellow
Write-Host "  - 请确保已正确配置 .env 文件中的 LLM_API_KEY" -ForegroundColor White

if ($deployMode -eq "1") {
    Write-Host "  - 关闭此窗口将停止API和Worker服务" -ForegroundColor White
    Write-Host "  - 如需停止服务，请使用 Ctrl+C" -ForegroundColor White
} elseif ($deployMode -eq "2") {
    Write-Host "  - 容器服务将在后台持续运行" -ForegroundColor White
    Write-Host "  - 使用 docker-compose down 停止所有服务" -ForegroundColor White
} else {
    Write-Host "  - 需要手动启动应用服务" -ForegroundColor White
    Write-Host "  - 使用 docker-compose down 停止基础服务" -ForegroundColor White
}

if ($deployMode -eq "2") {
    Write-Host "`n📚 更多Docker命令请参考: Docker部署指南.md" -ForegroundColor Cyan
}

if ($deployMode -eq "1") {
    Write-Host "`n按Ctrl+C停止服务..." -ForegroundColor Gray
    
    # 等待用户中断
    try {
        while ($true) {
            Start-Sleep -Seconds 1
        }
    } finally {
        Write-Host "`n🛑 正在停止服务..." -ForegroundColor Yellow
        
        # 停止后台任务
        if ($apiJob) { Stop-Job $apiJob; Remove-Job $apiJob }
        if ($workerJob) { Stop-Job $workerJob; Remove-Job $workerJob }
        
        # 停止Docker服务
        docker-compose down
        
        Write-Host "✅ 所有服务已停止" -ForegroundColor Green
    }
} elseif ($deployMode -eq "2") {
    Write-Host "`n✅ 容器化部署完成！服务将在后台持续运行。" -ForegroundColor Green
    Write-Host "使用以下命令管理服务：" -ForegroundColor Cyan
    Write-Host "  • 查看状态: docker-compose ps" -ForegroundColor White
    Write-Host "  • 停止服务: docker-compose down" -ForegroundColor White
    Write-Host "  • 查看日志: docker-compose logs -f api" -ForegroundColor White
    
    Write-Host "`n按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} else {
    Write-Host "`n✅ 基础服务已启动！" -ForegroundColor Green
    Write-Host "请手动启动应用服务：" -ForegroundColor Cyan
    Write-Host "  • API服务: python scripts\start_api.py" -ForegroundColor White
    Write-Host "  • Worker服务: python scripts\start_worker_windows.py" -ForegroundColor White
    
    Write-Host "`n按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} 