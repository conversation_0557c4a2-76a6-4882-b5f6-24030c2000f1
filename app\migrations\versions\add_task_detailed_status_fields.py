"""add task detailed status fields

Revision ID: add_task_detailed_status_fields
Revises: 
Create Date: 2025-06-20 04:35:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_task_detailed_status_fields'
down_revision = None
depends_on = None


def upgrade():
    """添加任务详细状态字段"""
    # 添加新的字段到generation_tasks表
    op.add_column('generation_tasks', sa.Column('current_stage', sa.String(100), nullable=True, comment='当前执行阶段'))
    op.add_column('generation_tasks', sa.Column('detailed_progress', sa.Text(), nullable=True, comment='详细进度信息JSON'))
    op.add_column('generation_tasks', sa.Column('result_data', sa.Text(), nullable=True, comment='结果数据JSON'))


def downgrade():
    """移除任务详细状态字段"""
    op.drop_column('generation_tasks', 'result_data')
    op.drop_column('generation_tasks', 'detailed_progress')
    op.drop_column('generation_tasks', 'current_stage') 