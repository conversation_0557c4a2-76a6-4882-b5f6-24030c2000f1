#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务管理API端点
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    NotFoundException, BusinessException, DatabaseException, ErrorCode
)
from app.models.user import User
from app.models.generation_task import GenerationTask
from app.schemas.task import TaskResponse, TaskListResponse
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.get("/", response_model=TaskListResponse, summary="获取用户任务列表")
async def get_user_tasks(
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取当前用户的任务列表
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 构建查询
        query = select(GenerationTask).where(GenerationTask.user_id == current_user.id)
        
        if status_filter:
            query = query.where(GenerationTask.status == status_filter)
        
        # 获取总数
        count_query = select(GenerationTask).where(GenerationTask.user_id == current_user.id)
        if status_filter:
            count_query = count_query.where(GenerationTask.status == status_filter)
        
        total_result = await session.execute(count_query)
        total = len(total_result.scalars().all())
        
        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(GenerationTask.created_at.desc())
        result = await session.execute(query)
        tasks = result.scalars().all()
        
        # 记录获取任务列表日志
        log_business_operation(
            logger=logger.logger,
            operation="get_user_tasks",
            details={
                "total_tasks": total,
                "status_filter": status_filter,
                "page": skip // limit + 1,
                "per_page": limit
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return TaskListResponse(
            tasks=[TaskResponse.model_validate(task) for task in tasks],
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_user_tasks'
        })
        raise DatabaseException(
            message="获取任务列表失败",
            details={"user_id": user_id}
        )


@router.get("/{task_id}", response_model=TaskResponse, summary="获取任务详情")
async def get_task_detail(
    task_id: str,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取指定任务的详细信息和进度
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(GenerationTask).where(
            and_(
                GenerationTask.id == task_id,
                GenerationTask.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise NotFoundException(
                message="任务不存在或无权限访问",
                error_code=ErrorCode.GENERATION_TASK_NOT_FOUND
            )
        
        # 记录获取任务详情日志
        log_business_operation(
            logger=logger.logger,
            operation="get_task_detail",
            details={
                "task_id": task_id,
                "task_type": task.task_type,
                "status": task.status,
                "progress": task.progress
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return TaskResponse.model_validate(task)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败 - 用户ID: {user_id}, 任务ID: {task_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_task_detail'
        })
        raise DatabaseException(
            message="获取任务详情失败",
            details={"task_id": task_id}
        )


@router.delete("/{task_id}", summary="取消任务")
async def cancel_task(
    task_id: str,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    取消指定的生成任务
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(GenerationTask).where(
            and_(
                GenerationTask.id == task_id,
                GenerationTask.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise NotFoundException(
                message="任务不存在或无权限访问",
                error_code=ErrorCode.GENERATION_TASK_NOT_FOUND
            )
        
        if task.status in ['completed', 'failed', 'cancelled']:
            raise BusinessException(
                message="任务已完成或已取消，无法取消",
                error_code=ErrorCode.GENERATION_TASK_ALREADY_RUNNING
            )
        
        # TODO: 取消Celery任务
        # from app.core.celery_app import celery_app
        # celery_app.control.revoke(task_id, terminate=True)
        
        # 更新任务状态
        task.status = "cancelled"
        await session.commit()
        
        # 记录取消任务日志
        log_business_operation(
            logger=logger.logger,
            operation="cancel_task",
            details={
                "task_id": task_id,
                "task_type": task.task_type,
                "previous_status": task.status
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return {"message": "任务已成功取消"}
        
    except (NotFoundException, BusinessException):
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"取消任务失败 - 用户ID: {user_id}, 任务ID: {task_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'cancel_task'
        })
        raise DatabaseException(
            message="取消任务失败",
            details={"task_id": task_id}
        )


@router.get("/{task_id}/progress", summary="获取任务进度")
async def get_task_progress(
    task_id: str,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    实时获取任务进度信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(GenerationTask).where(
            and_(
                GenerationTask.id == task_id,
                GenerationTask.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise NotFoundException(
                message="任务不存在或无权限访问",
                error_code=ErrorCode.GENERATION_TASK_NOT_FOUND
            )
        
        # TODO: 从Celery获取实时进度
        # from app.core.celery_app import celery_app
        # celery_task = celery_app.AsyncResult(task_id)
        # progress_info = celery_task.info
        
        # 记录获取任务进度日志
        log_business_operation(
            logger=logger.logger,
            operation="get_task_progress",
            details={
                "task_id": task_id,
                "task_type": task.task_type,
                "status": task.status,
                "progress": task.progress
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return {
            "id": str(task.id),
            "status": task.status.value,
            "progress": task.progress,
            "error_message": task.error_message,
            "created_at": task.created_at,
            "updated_at": task.updated_at
        }
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败 - 用户ID: {user_id}, 任务ID: {task_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_task_progress'
        })
        raise DatabaseException(
            message="获取任务进度失败",
            details={"task_id": task_id}
        ) 
