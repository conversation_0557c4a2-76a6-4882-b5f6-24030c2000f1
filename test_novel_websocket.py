#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 支持WebSocket实时监控的完整流程测试脚本
提供实时进度更新和更好的用户体验
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any, Set
import aiohttp
import websockets
from dataclasses import dataclass
from enum import Enum
import signal
import sys


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


@dataclass
class TestConfig:
    """测试配置"""
    base_url: str = "http://localhost:8000"
    ws_url: str = "ws://localhost:8000"
    api_prefix: str = "/api/v1"

    def update_ports(self, port: int):
        """更新端口配置"""
        self.base_url = f"http://localhost:{port}"
        self.ws_url = f"ws://localhost:{port}"
    # 模拟微信登录的测试参数
    test_wx_code: str = "test_code_123456"
    test_nickname: str = "AI小说测试用户"
    test_avatar: str = "https://example.com/avatar.jpg"
    # 测试小说参数
    novel_title: str = "AI奇幻冒险小说"
    novel_theme: str = "一个现代程序员意外穿越到魔法世界，必须用编程思维拯救这个被古老诅咒笼罩的王国"
    novel_genre: str = "fantasy"
    target_length: int = 25000
    chapter_count: int = 3
    # WebSocket配置
    enable_websocket: bool = True
    heartbeat_interval: int = 30


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self, ws_url: str):
        self.ws_url = ws_url
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.active_tasks: Set[str] = set()
        
    async def connect_to_task(self, task_id: str) -> Optional[websockets.WebSocketServerProtocol]:
        """连接到特定任务的WebSocket"""
        try:
            ws_url = f"{self.ws_url}/ws/{task_id}"
            websocket = await websockets.connect(ws_url)
            self.connections[task_id] = websocket
            self.active_tasks.add(task_id)
            return websocket
        except Exception as e:
            print(f"⚠️ WebSocket连接失败: {str(e)}")
            return None
    
    async def disconnect_task(self, task_id: str):
        """断开特定任务的WebSocket连接"""
        if task_id in self.connections:
            try:
                await self.connections[task_id].close()
            except:
                pass
            del self.connections[task_id]
            self.active_tasks.discard(task_id)
    
    async def listen_task_updates(self, task_id: str, callback):
        """监听任务更新"""
        websocket = self.connections.get(task_id)
        if not websocket:
            return
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await callback(task_id, data)
                    
                    # 如果任务完成，停止监听
                    if data.get("type") == "task_update" and data.get("status") in ["SUCCESS", "FAILED", "CANCELLED"]:
                        break
                        
                except json.JSONDecodeError:
                    print(f"⚠️ 无法解析WebSocket消息: {message}")
                    
        except websockets.exceptions.ConnectionClosed:
            print(f"📡 WebSocket连接已关闭: {task_id}")
        except Exception as e:
            print(f"⚠️ WebSocket监听异常: {str(e)}")
        finally:
            await self.disconnect_task(task_id)
    
    async def send_heartbeat(self, task_id: str):
        """发送心跳包"""
        websocket = self.connections.get(task_id)
        if websocket:
            try:
                heartbeat_msg = {
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send(json.dumps(heartbeat_msg))
            except Exception as e:
                print(f"⚠️ 发送心跳失败: {str(e)}")
    
    async def close_all(self):
        """关闭所有连接"""
        for task_id in list(self.connections.keys()):
            await self.disconnect_task(task_id)


class EnhancedNovelGenerationTester:
    """增强版小说生成测试器（支持WebSocket）"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.user_info: Optional[Dict] = None
        self.novel_id: Optional[int] = None
        self.ws_manager = WebSocketManager(config.ws_url)
        self.task_progress: Dict[str, Dict] = {}
        self.shutdown_requested = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n⚠️ 收到退出信号 {signum}，正在清理资源...")
        self.shutdown_requested = True
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=300)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        await self.ws_manager.close_all()
        if self.session:
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.config.base_url}{self.config.api_prefix}{endpoint}"
        headers = self._get_headers()
        
        self._log(f"🌐 {method} {url}")
        
        async with self.session.request(method, url, headers=headers, json=data) as response:
            response_data = await response.json()
            
            if response.status >= 400:
                self._log(f"❌ 请求失败 [{response.status}]: {response_data}")
                raise Exception(f"API请求失败: {response_data}")
            
            return response_data
    
    def _log(self, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    async def _task_update_callback(self, task_id: str, data: Dict):
        """WebSocket任务更新回调"""
        message_type = data.get("type", "unknown")

        # 添加原始消息调试
        self._log(f"🔍 收到WebSocket消息: type={message_type}, data={data}")

        if message_type == "task_update":
            status = data.get("status", "UNKNOWN")
            progress = data.get("progress", 0)
            message = data.get("message", "")

            # 更新任务进度
            self.task_progress[task_id] = {
                "status": status,
                "progress": progress,
                "message": message,
                "updated_at": datetime.now()
            }

            # 实时输出进度
            task_name = self.task_progress[task_id].get("name", "任务")
            self._log(f"📈 {task_name} - {progress}% - {message}")

        elif message_type == "stage_start":
            # 阶段开始
            stage = data.get("stage", "unknown")
            self._log(f"🎬 开始阶段: {stage}")

        elif message_type == "content_chunk":
            # 流式内容片段
            stage = data.get("stage", "unknown")
            chunk = data.get("chunk", "")
            chunk_index = data.get("chunk_index", 0)
            total_length = data.get("total_length", 0)

            # 显示流式内容（截取前50个字符避免刷屏）
            display_chunk = chunk[:50] + "..." if len(chunk) > 50 else chunk
            self._log(f"📝 [{stage}] 片段{chunk_index}: {display_chunk}")

        elif message_type == "content_complete":
            # 内容完成
            stage = data.get("stage", "unknown")
            final_content = data.get("final_content", "")
            content_length = len(final_content)
            self._log(f"✅ 阶段完成: {stage} (生成{content_length}字符)")

        elif message_type == "generation_complete":
            # 整个生成任务完成
            summary = data.get("summary", "生成完成")
            self._log(f"🎉 生成完成: {summary}")

        elif message_type == "error_recovery":
            # 错误恢复
            error_message = data.get("error_message", "")
            recovery_action = data.get("recovery_action", "")
            self._log(f"⚠️ 错误恢复: {error_message} - {recovery_action}")

        elif message_type == "connection_status":
            # 连接状态
            status = data.get("status", "unknown")
            self._log(f"🔗 连接状态: {status}")

        elif message_type == "pong":
            # 心跳响应
            self._log(f"💓 收到心跳响应")
        else:
            # 未知消息类型，记录调试信息
            self._log(f"🔍 未知消息类型: {message_type} - {data}")
    
    async def _wait_for_task_with_websocket(self, task_id: str, task_name: str, max_wait_time: int = 600) -> bool:
        """使用WebSocket等待任务完成"""
        self.task_progress[task_id] = {"name": task_name, "status": "PENDING", "progress": 0}
        
        if self.config.enable_websocket:
            self._log(f"📡 建立WebSocket连接监听{task_name}...")
            
            # 连接WebSocket
            websocket = await self.ws_manager.connect_to_task(task_id)
            if websocket:
                # 启动监听任务
                listen_task = asyncio.create_task(
                    self.ws_manager.listen_task_updates(task_id, self._task_update_callback)
                )
                
                # 启动心跳任务
                heartbeat_task = asyncio.create_task(
                    self._send_periodic_heartbeat(task_id)
                )
                
                try:
                    # 等待任务完成或超时
                    start_time = time.time()
                    while time.time() - start_time < max_wait_time:
                        if self.shutdown_requested:
                            self._log("⚠️ 收到停止信号，取消等待")
                            return False
                        
                        task_info = self.task_progress.get(task_id, {})
                        status = task_info.get("status", "PENDING")
                        
                        if status == "SUCCESS":
                            self._log(f"✅ {task_name}完成!")
                            return True
                        elif status == "FAILED":
                            self._log(f"❌ {task_name}失败: {task_info.get('message', '未知错误')}")
                            return False
                        elif status == "CANCELLED":
                            self._log(f"⚠️ {task_name}已取消")
                            return False
                        
                        await asyncio.sleep(2)
                    
                    self._log(f"⏰ {task_name}等待超时")
                    return False
                    
                finally:
                    # 清理任务
                    listen_task.cancel()
                    heartbeat_task.cancel()
                    await self.ws_manager.disconnect_task(task_id)
            else:
                self._log(f"⚠️ WebSocket连接失败，切换到轮询模式")
                return await self._wait_for_task_polling(task_id, task_name, max_wait_time)
        else:
            return await self._wait_for_task_polling(task_id, task_name, max_wait_time)
    
    async def _wait_for_task_polling(self, task_id: str, task_name: str, max_wait_time: int = 300) -> bool:
        """使用轮询方式等待任务完成"""
        self._log(f"🔄 使用轮询方式等待{task_name}完成...")
        
        start_time = time.time()
        last_progress = -1
        
        while time.time() - start_time < max_wait_time:
            if self.shutdown_requested:
                return False
                
            try:
                response = await self._make_request("GET", f"/tasks/{task_id}")
                
                if response["success"]:
                    task = response["data"]
                    status = task["status"]
                    progress = task.get("progress", 0)
                    
                    if progress != last_progress:
                        self._log(f"📈 {task_name}进度: {progress}% (状态: {status})")
                        last_progress = progress
                    
                    if status == "SUCCESS":
                        self._log(f"✅ {task_name}完成!")
                        return True
                    elif status == "FAILED":
                        error_msg = task.get("error_message", "未知错误")
                        self._log(f"❌ {task_name}失败: {error_msg}")
                        return False
                    elif status == "CANCELLED":
                        self._log(f"⚠️ {task_name}已取消")
                        return False
                    
                    await asyncio.sleep(5)
                else:
                    await asyncio.sleep(5)
                    
            except Exception as e:
                self._log(f"⚠️ 检查任务状态异常: {str(e)}")
                await asyncio.sleep(5)
        
        self._log(f"⏰ {task_name}等待超时")
        return False
    
    async def _send_periodic_heartbeat(self, task_id: str):
        """定期发送心跳包"""
        try:
            while task_id in self.ws_manager.active_tasks:
                await self.ws_manager.send_heartbeat(task_id)
                await asyncio.sleep(self.config.heartbeat_interval)
        except asyncio.CancelledError:
            pass
    
    async def auto_detect_port(self) -> bool:
        """自动检测API服务端口"""
        ports_to_try = [8000, 8001, 8002, 8003, 8004, 8005]

        for port in ports_to_try:
            try:
                test_url = f"http://localhost:{port}/docs"
                async with self.session.get(test_url, timeout=aiohttp.ClientTimeout(total=3)) as response:
                    if response.status == 200:
                        self._log(f"🔍 检测到API服务运行在端口: {port}")
                        self.config.update_ports(port)
                        return True
            except:
                continue

        self._log("❌ 无法检测到API服务端口")
        return False

    async def step1_user_login(self) -> bool:
        """步骤1: 用户登录"""
        self._log("🔐 步骤1: 开始用户登录...")

        # 首先自动检测端口
        if not await self.auto_detect_port():
            return False

        try:
            login_data = {
                "code": self.config.test_wx_code,
                "nickname": self.config.test_nickname,
                "avatar_url": self.config.test_avatar
            }

            # 使用测试登录端点避免微信API验证
            response = await self._make_request("POST", "/auth/test/login", login_data)

            # 测试端点直接返回TokenResponse格式，不包装在success/data中
            if "access_token" in response:
                self.access_token = response["access_token"]
                self.user_info = response["user"]

                self._log(f"✅ 登录成功! 用户ID: {self.user_info['id']}")
                self._log(f"📊 用户配额: {self.user_info['quota_used']}/{self.user_info['quota_limit']}")
                return True
            else:
                self._log(f"❌ 登录失败: {response}")
                return False

        except Exception as e:
            self._log(f"❌ 登录异常: {str(e)}")
            return False
    
    async def step2_create_novel(self) -> bool:
        """步骤2: 创建小说项目"""
        self._log("📚 步骤2: 创建小说项目...")
        
        try:
            novel_data = {
                "title": self.config.novel_title,
                "description": self.config.novel_theme,
                "genre": self.config.novel_genre,
                "target_length": self.config.target_length,
                "style_settings": {
                    "style": "奇幻冒险",
                    "pace": "medium",
                    "focus": "plot_driven"
                }
            }
            
            response = await self._make_request("POST", "/novels/", novel_data)
            
            # 小说创建端点直接返回小说对象，没有success/data包装
            if "id" in response:
                novel = response
                self.novel_id = novel["id"]
                
                self._log(f"✅ 小说创建成功! 小说ID: {self.novel_id}")
                self._log(f"📖 小说标题: {novel['title']}")
                return True
            else:
                self._log(f"❌ 小说创建失败: {response}")
                return False
                
        except Exception as e:
            self._log(f"❌ 小说创建异常: {str(e)}")
            return False
    
    async def step3_generate_architecture(self) -> bool:
        """步骤3: 生成小说架构"""
        self._log("🏗️ 步骤3: 生成小说架构...")
        
        try:
            arch_data = {
                "novel_id": self.novel_id,
                "theme": self.config.novel_theme,
                "genre": self.config.novel_genre,
                "target_length": self.config.target_length,
                "style_preferences": {
                    "style": "奇幻冒险",
                    "pace": "medium",
                    "focus": "character_development"
                }
            }
            
            response = await self._make_request("POST", "/generation/architecture", arch_data)

            # 架构生成端点直接返回任务信息，没有success/data包装
            if "task_id" in response:
                task_id = response["task_id"]

                self._log(f"✅ 架构生成任务启动! 任务ID: {task_id}")
                self._log(f"🔍 调试信息: 完整响应 = {response}")

                # 使用WebSocket等待任务完成
                success = await self._wait_for_task_with_websocket(task_id, "架构生成", 600)
                return success
            else:
                self._log(f"❌ 架构生成失败: {response}")
                return False
                
        except Exception as e:
            self._log(f"❌ 架构生成异常: {str(e)}")
            return False
    
    async def step4_generate_chapters(self) -> bool:
        """步骤4: 生成章节内容"""
        self._log(f"📝 步骤4: 生成章节内容 (共{self.config.chapter_count}章)...")
        
        try:
            for chapter_num in range(1, self.config.chapter_count + 1):
                if self.shutdown_requested:
                    self._log("⚠️ 收到停止信号，停止章节生成")
                    return False
                
                self._log(f"✍️ 开始生成第{chapter_num}章...")
                
                # 获取上下文章节
                context_chapters = list(range(max(1, chapter_num - 2), chapter_num))
                
                chapter_data = {
                    "novel_id": self.novel_id,
                    "chapter_number": chapter_num,
                    "chapter_outline": f"第{chapter_num}章：主角在魔法世界的新发现和挑战",
                    "context_chapters": context_chapters,
                    "style_preferences": {
                        "style": "奇幻冒险",
                        "tone": "轻松而富有想象力"
                    }
                }
                
                response = await self._make_request("POST", "/generation/chapter", chapter_data)
                
                # 章节生成端点直接返回任务信息，没有success/data包装
                if "task_id" in response:
                    task_id = response["task_id"]
                    
                    self._log(f"✅ 第{chapter_num}章生成任务启动! 任务ID: {task_id}")
                    
                    # 使用WebSocket等待当前章节完成
                    success = await self._wait_for_task_with_websocket(task_id, f"第{chapter_num}章生成", 600)
                    if not success:
                        return False
                        
                    # 短暂延迟
                    await asyncio.sleep(2)
                else:
                    self._log(f"❌ 第{chapter_num}章生成失败: {response}")
                    return False
            
            self._log("✅ 所有章节生成完成!")
            return True
            
        except Exception as e:
            self._log(f"❌ 章节生成异常: {str(e)}")
            return False
    
    async def step5_verify_results(self) -> bool:
        """步骤5: 验证生成结果"""
        self._log("🔍 步骤5: 验证生成结果...")
        
        try:
            # 获取小说详情
            response = await self._make_request("GET", f"/novels/{self.novel_id}")
            
            # 小说详情端点直接返回小说对象
            if "status" in response:
                novel = response
                self._log(f"📊 小说状态: {novel['status']}")
                
                # 获取章节列表
                chapters_response = await self._make_request("GET", f"/chapters/novel/{self.novel_id}")
                
                # 章节列表端点直接返回列表对象
                if "items" in chapters_response:
                    chapters = chapters_response["items"]
                    self._log(f"📊 已生成章节数: {len(chapters)}")
                    
                    total_words = 0
                    for chapter in chapters:
                        word_count = chapter.get("word_count", 0)
                        total_words += word_count
                        self._log(f"  - 第{chapter['chapter_number']}章: {chapter['title']} ({word_count}字)")
                    
                    self._log(f"📖 小说总字数: {total_words}")
                    
                    if len(chapters) == self.config.chapter_count and total_words > 0:
                        self._log("✅ 结果验证成功!")
                        return True
                    else:
                        self._log(f"❌ 结果验证失败: 章节数或总字数与预期不符")
                        return False
                else:
                    self._log(f"❌ 获取章节列表失败: {chapters_response}")
                    return False
            else:
                self._log(f"❌ 获取小说详情失败: {response}")
                return False
                
        except Exception as e:
            self._log(f"❌ 结果验证异常: {str(e)}")
            return False
    
    async def run_complete_flow(self) -> bool:
        """运行完整的生成流程"""
        self._log("🚀 开始AI小说生成完整流程测试 (WebSocket版)")
        self._log("=" * 60)
        
        start_time = time.time()
        
        try:
            steps = [
                ("用户登录", self.step1_user_login),
                ("创建小说项目", self.step2_create_novel),
                ("生成小说架构", self.step3_generate_architecture),
                ("生成章节内容", self.step4_generate_chapters),
                ("验证生成结果", self.step5_verify_results)
            ]
            
            for step_name, step_func in steps:
                if self.shutdown_requested:
                    self._log(f"⚠️ 收到停止信号，中止流程")
                    return False
                
                if not await step_func():
                    self._log(f"❌ 步骤 '{step_name}' 执行失败，停止流程")
                    return False
            
            elapsed_time = time.time() - start_time
            self._log("=" * 60)
            self._log(f"🎉 完整流程测试成功! 总耗时: {elapsed_time:.1f}秒")
            self._log(f"📚 生成的小说ID: {self.novel_id}")
            
            return True
            
        except Exception as e:
            self._log(f"❌ 流程执行异常: {str(e)}")
            return False


async def run_enhanced_novel_generation_test():
    """运行增强版小说生成测试"""
    config = TestConfig()
    
    print("AI小说生成器 - 增强版完整流程测试")
    print("=" * 60)
    print(f"API服务地址: {config.base_url}")
    print(f"WebSocket地址: {config.ws_url}")
    print(f"测试小说主题: {config.novel_theme}")
    print(f"目标长度: {config.target_length}字")
    print(f"计划章节数: {config.chapter_count}章")
    print(f"WebSocket监控: {'启用' if config.enable_websocket else '禁用'}")
    print("=" * 60)
    
    async with EnhancedNovelGenerationTester(config) as tester:
        try:
            success = await tester.run_complete_flow()
            
            if success:
                print("\n测试完成 - 所有步骤都成功执行!")
                print("建议: 可以通过API或数据库查看生成的具体内容")
                print("可通过以下接口查看结果:")
                print(f"   - 小说详情: GET {config.base_url}{config.api_prefix}/novels/{tester.novel_id}")
                print(f"   - 章节列表: GET {config.base_url}{config.api_prefix}/chapters/novel/{tester.novel_id}")
                return 0
            else:
                print("\n测试失败 - 请检查服务状态和配置")
                return 1
                
        except KeyboardInterrupt:
            print("\n用户中断测试")
            return 1
        except Exception as e:
            print(f"\n测试异常: {str(e)}")
            return 1


if __name__ == "__main__":
    print("启动AI小说生成流程测试 (增强版)...")
    try:
        exit_code = asyncio.run(run_enhanced_novel_generation_test())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n再见!")
        sys.exit(0) 