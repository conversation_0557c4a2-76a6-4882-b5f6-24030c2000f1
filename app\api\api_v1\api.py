#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API v1 路由聚合
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    auth,
    users,
    novels, 
    chapters,
    generation,
    tasks,
    llm_config,
)

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(novels.router, prefix="/novels", tags=["小说管理"])
api_router.include_router(chapters.router, prefix="/chapters", tags=["章节管理"])
api_router.include_router(generation.router, prefix="/generation", tags=["内容生成"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(llm_config.router, prefix="/llm-configs", tags=["LLM配置管理"]) 