#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 统一调试启动脚本
同时启动API服务和Celery Worker，方便开发调试
"""
import os
import sys
import subprocess
import asyncio
import asyncpg
import redis
import threading
import time
import signal
from pathlib import Path
from datetime import datetime


class DebugSystemManager:
    def __init__(self):
        self.api_process = None
        self.worker_process = None
        self.running = True
        self.log_prefix = {
            'api': '[API] ',
            'worker': '[WORKER] ',
            'system': '[SYSTEM] '
        }
    
    def log(self, service, message):
        """统一日志输出"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        prefix = self.log_prefix.get(service, '[UNKNOWN] ')
        print(f"{timestamp} {prefix}{message}")
    
    def check_environment(self):
        """检查运行环境"""
        self.log('system', "正在检查运行环境...")
        
        # 检查是否在项目根目录
        if not os.path.exists("app"):
            self.log('system', "错误：请在项目根目录运行此脚本")
            return False
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            self.log('system', "错误：需要Python 3.8或更高版本")
            return False
        
        self.log('system', f"✓ Python版本: {sys.version}")
        
        # 检查虚拟环境
        if not os.environ.get('VIRTUAL_ENV'):
            self.log('system', "警告：未检测到虚拟环境，建议先激活虚拟环境")
        else:
            self.log('system', f"✓ 虚拟环境: {os.environ['VIRTUAL_ENV']}")
        
        return True
    
    def setup_environment(self):
        """设置环境变量"""
        self.log('system', "正在设置环境变量...")
        
        # 设置Python路径
        current_dir = os.getcwd()
        python_path = os.environ.get('PYTHONPATH', '')
        if current_dir not in python_path:
            if os.name == 'nt':  # Windows
                separator = ';'
            else:  # Unix/Linux/Mac
                separator = ':'
            os.environ['PYTHONPATH'] = f"{current_dir}{separator}{python_path}" if python_path else current_dir
        
        # 设置默认环境变量
        defaults = {
            'ENVIRONMENT': 'development',
            'DEBUG': 'true',
            'PROJECT_NAME': 'AI Novel Generator',
            'VERSION': '1.0.0',
            'API_V1_STR': '/api/v1',
            'SECRET_KEY': 'your-secret-key-change-in-production',
            'ACCESS_TOKEN_EXPIRE_MINUTES': '60',
            'DATABASE_URL': 'postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator',
            'REDIS_URL': 'redis://localhost:6379/0',
            'CELERY_BROKER_URL': 'redis://localhost:6379/0',
            'CELERY_RESULT_BACKEND': 'redis://localhost:6379/0',
            'BACKEND_CORS_ORIGINS': '["http://localhost:3000", "http://localhost:8080"]',
            'HOST': '0.0.0.0',
            'PORT': '8000'
        }
        
        set_count = 0
        for key, value in defaults.items():
            if not os.environ.get(key):
                os.environ[key] = value
                set_count += 1
        
        self.log('system', f"✓ 设置了 {set_count} 个默认环境变量")
    
    async def check_database_connection(self):
        """检查数据库连接"""
        try:
            database_url = os.environ.get('DATABASE_URL', '')
            if not database_url:
                self.log('system', "警告：DATABASE_URL未设置")
                return False
            
            # 解析数据库URL
            if database_url.startswith('postgresql+asyncpg://'):
                pg_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
            else:
                pg_url = database_url
            
            conn = await asyncpg.connect(pg_url)
            await conn.close()
            self.log('system', "✓ 数据库连接成功")
            return True
            
        except Exception as e:
            self.log('system', f"✗ 数据库连接失败: {e}")
            return False
    
    def check_redis_connection(self):
        """检查Redis连接"""
        try:
            redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
            r = redis.from_url(redis_url)
            r.ping()
            self.log('system', "✓ Redis连接成功")
            return True
            
        except Exception as e:
            self.log('system', f"✗ Redis连接失败: {e}")
            return False
    
    async def check_dependencies(self):
        """检查服务依赖"""
        self.log('system', "正在检查服务依赖...")
        
        db_ok = await self.check_database_connection()
        redis_ok = self.check_redis_connection()
        
        if not db_ok:
            self.log('system', "警告：数据库连接失败，某些功能可能无法正常工作")
        
        if not redis_ok:
            self.log('system', "警告：Redis连接失败，任务队列功能可能无法正常工作")
        
        return db_ok and redis_ok
    
    def get_python_executable(self):
        """获取正确的Python解释器路径"""
        # 首先尝试使用当前运行的Python解释器（最可靠）
        current_python = sys.executable
        self.log('system', f"当前Python解释器: {current_python}")

        # 检查当前Python是否可用
        if os.path.exists(current_python):
            self.log('system', f"✓ 使用当前Python解释器: {current_python}")
            return current_python

        # 检查是否在虚拟环境中
        venv_path = os.environ.get('VIRTUAL_ENV')
        if venv_path:
            # 在虚拟环境中，使用虚拟环境的Python
            if os.name == 'nt':  # Windows
                python_exe = os.path.join(venv_path, 'Scripts', 'python.exe')
            else:  # Unix/Linux/Mac
                python_exe = os.path.join(venv_path, 'bin', 'python')

            if os.path.exists(python_exe):
                self.log('system', f"✓ 使用虚拟环境Python: {python_exe}")
                return python_exe

        # 检查是否存在项目本地的虚拟环境
        current_dir = os.getcwd()
        possible_venv_names = ['novel_ai_venv', 'venv', 'env', '.venv']

        for venv_name in possible_venv_names:
            venv_dir = os.path.join(current_dir, venv_name)
            if os.path.exists(venv_dir):
                if os.name == 'nt':  # Windows
                    python_exe = os.path.join(venv_dir, 'Scripts', 'python.exe')
                else:  # Unix/Linux/Mac
                    python_exe = os.path.join(venv_dir, 'bin', 'python')

                if os.path.exists(python_exe):
                    self.log('system', f"✓ 找到本地虚拟环境: {venv_dir}")
                    return python_exe

        # 最后回退到当前Python解释器
        self.log('system', "警告：未找到其他Python解释器，使用当前解释器")
        return current_python
    
    def start_api_server(self):
        """启动API服务器"""
        self.log('api', "正在启动API服务器...")

        # 获取配置
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', '8000'))

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 验证Python解释器是否存在
        if not os.path.exists(python_exe):
            self.log('api', f"错误：Python解释器不存在: {python_exe}")
            return False

        # 验证uvicorn是否可用
        try:
            result = subprocess.run([python_exe, "-m", "uvicorn", "--help"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                self.log('api', f"错误：uvicorn不可用，返回码: {result.returncode}")
                self.log('api', f"错误输出: {result.stderr}")
                return False
        except Exception as e:
            self.log('api', f"错误：无法验证uvicorn: {e}")
            return False

        # 构建启动命令
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            f"--host={host}",
            f"--port={port}",
            "--reload",
            "--log-level=info"
        ]

        self.log('api', f"执行命令: {' '.join(cmd)}")
        self.log('api', f"工作目录: {os.getcwd()}")

        try:
            # 启动API服务
            self.api_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=os.getcwd()  # 明确设置工作目录
            )

            self.log('api', f"API服务已启动，PID: {self.api_process.pid}")
            self.log('api', f"服务地址: http://{host}:{port}")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.api_process, 'api'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('api', f"启动API服务时出错: {e}")
            import traceback
            self.log('api', f"详细错误: {traceback.format_exc()}")
            return False
    
    def start_celery_worker(self):
        """启动Celery Worker"""
        self.log('worker', "正在启动Celery Worker...")
        
        # 获取正确的Python解释器
        python_exe = self.get_python_executable()
        
        # 构建启动命令
        cmd = [
            python_exe, "-m", "celery",
            "-A", "app.core.celery_app:celery_app",
            "worker",
            "--loglevel=info",
            "--concurrency=2",
            "--pool=solo",
            "--without-mingle",
            "--without-gossip"
        ]
        
        self.log('worker', f"执行命令: {' '.join(cmd)}")
        
        try:
            # 启动Celery Worker
            self.worker_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.log('worker', f"Celery Worker已启动，PID: {self.worker_process.pid}")
            
            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.worker_process, 'worker'),
                daemon=True
            ).start()
            
            return True
            
        except Exception as e:
            self.log('worker', f"启动Celery Worker时出错: {e}")
            return False
    
    def log_output_thread(self, process, service):
        """日志输出线程"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip() and self.running:
                    self.log(service, line.strip())
        except Exception as e:
            if self.running:
                self.log(service, f"日志输出异常: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log('system', f"接收到信号 {signum}，正在关闭服务...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        self.log('system', "正在关闭所有服务...")
        
        # 关闭API服务
        if self.api_process and self.api_process.poll() is None:
            self.log('api', "正在关闭API服务...")
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                self.log('api', "API服务已关闭")
            except subprocess.TimeoutExpired:
                self.log('api', "强制终止API服务...")
                self.api_process.kill()
                self.api_process.wait()
        
        # 关闭Celery Worker
        if self.worker_process and self.worker_process.poll() is None:
            self.log('worker', "正在关闭Celery Worker...")
            try:
                self.worker_process.terminate()
                self.worker_process.wait(timeout=10)
                self.log('worker', "Celery Worker已关闭")
            except subprocess.TimeoutExpired:
                self.log('worker', "强制终止Celery Worker...")
                self.worker_process.kill()
                self.worker_process.wait()
        
        self.log('system', "所有服务已关闭")
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            
            # 检查API进程
            if self.api_process and self.api_process.poll() is not None:
                self.log('api', f"API服务进程已退出，返回码: {self.api_process.returncode}")
                if self.running:
                    self.log('system', "API服务异常退出，关闭系统...")
                    self.shutdown()
                    break
            
            # 检查Worker进程
            if self.worker_process and self.worker_process.poll() is not None:
                self.log('worker', f"Celery Worker进程已退出，返回码: {self.worker_process.returncode}")
                if self.running:
                    self.log('system', "Celery Worker异常退出，关闭系统...")
                    self.shutdown()
                    break
    
    async def start_system(self):
        """启动整个系统"""
        self.log('system', "AI小说生成器 - 统一调试启动脚本")
        self.log('system', "=" * 50)
        
        # 检查环境
        if not self.check_environment():
            return False
        
        # 设置环境
        self.setup_environment()
        
        # 检查依赖
        deps_ok = await self.check_dependencies()
        if not deps_ok:
            self.log('system', "警告：部分依赖服务不可用，服务可能无法正常工作")
            response = input("是否继续启动？(y/N): ").strip().lower()
            if response != 'y':
                self.log('system', "启动已取消")
                return False
        
        # 设置信号处理器
        self.setup_signal_handlers()
        
        # 启动服务
        self.log('system', "正在启动服务...")
        
        # 启动API服务
        if not self.start_api_server():
            self.log('system', "API服务启动失败")
            return False
        
        # 等待API服务启动
        await asyncio.sleep(2)
        
        # 启动Celery Worker
        if not self.start_celery_worker():
            self.log('system', "Celery Worker启动失败")
            self.shutdown()
            return False
        
        # 等待Worker启动
        await asyncio.sleep(2)
        
        self.log('system', "✓ 所有服务启动完成")
        self.log('system', "")
        self.log('system', "服务状态:")
        self.log('system', f"  - API服务: http://0.0.0.0:{os.environ.get('PORT', '8000')}")
        self.log('system', f"  - Swagger文档: http://0.0.0.0:{os.environ.get('PORT', '8000')}/docs")
        self.log('system', f"  - Celery Worker: 运行中")
        self.log('system', "")
        self.log('system', "按 Ctrl+C 停止所有服务")
        self.log('system', "=" * 50)
        
        # 启动进程监控
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        # 等待关闭信号
        try:
            while self.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            self.log('system', "接收到关闭信号...")
            self.shutdown()
        
        return True


async def main():
    """主函数"""
    manager = DebugSystemManager()
    await manager.start_system()


if __name__ == "__main__":
    asyncio.run(main()) 