"""
质量监控服务 - 实时内容质量检查和优化建议
基于认知科学和文学理论的质量评估体系
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
from dataclasses import dataclass
from enum import Enum
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    """质量等级"""
    EXCELLENT = "excellent"    # 优秀 (90-100)
    GOOD = "good"             # 良好 (80-89)
    FAIR = "fair"             # 一般 (70-79)
    POOR = "poor"             # 较差 (60-69)
    CRITICAL = "critical"     # 严重问题 (<60)


@dataclass
class QualityMetrics:
    """质量指标"""
    consistency_score: float = 0.0      # 一致性评分 (0-1)
    readability_score: float = 0.0      # 可读性评分 (0-1)
    emotional_coherence: float = 0.0    # 情感连贯性 (0-1)
    narrative_flow: float = 0.0         # 叙事流畅性 (0-1)
    character_consistency: float = 0.0  # 角色一致性 (0-1)
    plot_coherence: float = 0.0         # 情节连贯性 (0-1)
    overall_quality: float = 0.0        # 综合质量 (0-1)


@dataclass
class QualityIssue:
    """质量问题"""
    issue_type: str              # 问题类型
    severity: str                # 严重程度 (critical/high/medium/low)
    description: str             # 问题描述
    suggestion: str              # 改进建议
    location: Optional[str]      # 问题位置


class QualityMonitor:
    """质量监控器"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        
        # 质量标准配置
        self.quality_thresholds = {
            QualityLevel.EXCELLENT: 90,
            QualityLevel.GOOD: 80,
            QualityLevel.FAIR: 70,
            QualityLevel.POOR: 60,
            QualityLevel.CRITICAL: 0
        }
        
        # 权重配置
        self.metric_weights = {
            "consistency_score": 0.25,
            "readability_score": 0.20,
            "emotional_coherence": 0.20,
            "narrative_flow": 0.20,
            "character_consistency": 0.15
        }
    
    async def evaluate_chapter_quality(self, chapter_content: str, novel_id: UUID, 
                                      chapter_number: int, context: Dict[str, Any]) -> Tuple[QualityMetrics, List[QualityIssue]]:
        """评估章节质量"""
        try:
            if not chapter_content or not chapter_content.strip():
                logger.warning(f"Chapter {chapter_number} has empty content")
                return self._create_default_metrics(), []
            
            metrics = QualityMetrics()
            issues = []
            
            # 1. 一致性检查
            try:
                consistency_score, consistency_issues = await self._check_consistency(
                    chapter_content, novel_id, chapter_number, context
                )
                metrics.consistency_score = consistency_score
                issues.extend(consistency_issues)
            except Exception as e:
                logger.error(f"Consistency check failed: {e}")
                metrics.consistency_score = 0.5  # 默认中等分数
            
            # 2. 可读性分析
            try:
                readability_score, readability_issues = await self._analyze_readability(chapter_content)
                metrics.readability_score = readability_score
                issues.extend(readability_issues)
            except Exception as e:
                logger.error(f"Readability analysis failed: {e}")
                metrics.readability_score = 0.7  # 默认较好分数
            
            # 3. 情节连贯性
            try:
                coherence_score, coherence_issues = await self._check_plot_coherence(
                    chapter_content, novel_id, chapter_number
                )
                metrics.plot_coherence = coherence_score
                issues.extend(coherence_issues)
            except Exception as e:
                logger.error(f"Plot coherence check failed: {e}")
                metrics.plot_coherence = 0.6  # 默认中等分数
            
            # 计算总体质量分数
            metrics.overall_quality = self._calculate_overall_quality(metrics)
            
            logger.info(f"Chapter {chapter_number} quality evaluation completed. Overall score: {metrics.overall_quality:.2f}")
            return metrics, issues
            
        except Exception as e:
            logger.error(f"Chapter quality evaluation failed: {e}")
            # 返回默认指标，避免系统崩溃
            return self._create_default_metrics(), []
    
    def _create_default_metrics(self) -> QualityMetrics:
        """创建默认质量指标"""
        metrics = QualityMetrics()
        metrics.consistency_score = 0.5
        metrics.readability_score = 0.7
        metrics.plot_coherence = 0.6
        metrics.overall_quality = 0.6
        return metrics
    
    async def _check_consistency(self, content: str, novel_id: UUID, 
                               chapter_number: int, context: Dict[str, Any]) -> Tuple[float, List[QualityIssue]]:
        """检查内容一致性"""
        issues = []
        score = 100.0
        
        try:
            from app.services.llm_service import llm_service
            
            # 获取历史内容进行对比
            historical_context = await self._get_historical_context(novel_id, chapter_number)
            
            if historical_context:
                consistency_prompt = f"""请检查以下新章节内容与历史内容的一致性：

新章节内容：
{content[:1000]}

历史上下文：
{historical_context[:1000]}

请检查以下方面的一致性：
1. 角色性格和行为是否一致
2. 世界设定是否保持一致
3. 时间线是否合理
4. 已建立的规则是否被违反

请按以下格式输出问题：
问题类型: 具体描述 | 严重程度: high/medium/low | 建议: 改进建议

如果没有发现问题，请回答"无一致性问题"。"""
                
                response = await llm_service.stream_invoke_with_retry(consistency_prompt)
                
                if "无一致性问题" not in response:
                    # 解析一致性问题
                    consistency_issues = self._parse_quality_issues(response, "consistency")
                    issues.extend(consistency_issues)
                    
                    # 根据问题数量和严重程度调整分数
                    score -= len(consistency_issues) * 15
                    for issue in consistency_issues:
                        if issue.severity == "high":
                            score -= 10
                        elif issue.severity == "medium":
                            score -= 5
            
            # 检查内部一致性
            internal_issues = self._check_internal_consistency(content)
            issues.extend(internal_issues)
            score -= len(internal_issues) * 10
            
            return max(0, score / 100), issues
            
        except Exception as e:
            logger.error(f"Failed to check consistency: {e}")
            return 0.7, []
    
    def _check_internal_consistency(self, content: str) -> List[QualityIssue]:
        """检查内部一致性"""
        issues = []
        
        # 检查角色名称拼写一致性
        character_names = re.findall(r'[A-Za-z\u4e00-\u9fff]{2,}(?=说|道|想|看|听|感到|觉得)', content)
        name_variants = {}
        
        for name in character_names:
            if len(name) >= 2:
                similar_names = [n for n in character_names if self._is_similar_name(name, n)]
                if len(similar_names) > 1:
                    name_variants[name] = similar_names
        
        if name_variants:
            issues.append(QualityIssue(
                issue_type="character_naming",
                severity="medium",
                description=f"发现可能的角色名称不一致：{list(name_variants.keys())[:3]}",
                suggestion="检查角色名称拼写，确保前后一致",
                location="全文"
            ))
        
        # 检查时间逻辑
        time_indicators = re.findall(r'(昨天|今天|明天|上午|下午|晚上|夜里|早上)', content)
        if len(set(time_indicators)) > 3:
            issues.append(QualityIssue(
                issue_type="time_logic",
                severity="low",
                description="时间指示词较多，请确认时间逻辑清晰",
                suggestion="梳理时间线，确保时间逻辑合理",
                location="时间描述部分"
            ))
        
        return issues
    
    def _is_similar_name(self, name1: str, name2: str) -> bool:
        """判断两个名称是否相似"""
        if name1 == name2:
            return True
        
        # 简单的相似度检查
        if len(name1) == len(name2) and len(name1) >= 2:
            diff_count = sum(c1 != c2 for c1, c2 in zip(name1, name2))
            return diff_count <= 1
        
        return False
    
    async def _analyze_readability(self, content: str) -> Tuple[float, List[QualityIssue]]:
        """分析可读性"""
        issues = []
        score = 100.0
        
        try:
            # 1. 句子长度分析
            sentences = re.split(r'[。！？]', content)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if sentences:
                avg_sentence_length = sum(len(s) for s in sentences) / len(sentences)
                
                if avg_sentence_length > 50:
                    issues.append(QualityIssue(
                        issue_type="sentence_length",
                        severity="medium",
                        description=f"平均句子长度过长（{avg_sentence_length:.1f}字）",
                        suggestion="适当缩短句子长度，提高可读性",
                        location="全文"
                    ))
                    score -= 15
                elif avg_sentence_length < 10:
                    issues.append(QualityIssue(
                        issue_type="sentence_length",
                        severity="low",
                        description=f"平均句子长度过短（{avg_sentence_length:.1f}字）",
                        suggestion="适当增加句子长度，丰富表达",
                        location="全文"
                    ))
                    score -= 10
            
            # 2. 段落结构分析
            paragraphs = content.split('\n\n')
            paragraphs = [p.strip() for p in paragraphs if p.strip()]
            
            if paragraphs:
                avg_paragraph_length = sum(len(p) for p in paragraphs) / len(paragraphs)
                
                if avg_paragraph_length > 300:
                    issues.append(QualityIssue(
                        issue_type="paragraph_length",
                        severity="medium",
                        description="段落过长，影响阅读体验",
                        suggestion="适当分段，提高文本层次感",
                        location="长段落"
                    ))
                    score -= 10
            
            # 3. 重复词汇检查
            words = re.findall(r'[\u4e00-\u9fff]+', content)
            if words:
                word_freq = {}
                for word in words:
                    if len(word) >= 2:
                        word_freq[word] = word_freq.get(word, 0) + 1
                
                # 检查高频词汇
                high_freq_words = [word for word, freq in word_freq.items() 
                                 if freq > len(words) * 0.02 and word not in ['他们', '我们', '这个', '那个', '可以', '已经', '现在', '时候']]
                
                if high_freq_words:
                    issues.append(QualityIssue(
                        issue_type="word_repetition",
                        severity="low",
                        description=f"词汇重复较多：{', '.join(high_freq_words[:3])}",
                        suggestion="增加词汇多样性，避免过度重复",
                        location="全文"
                    ))
                    score -= 5
            
            return max(0, score / 100), issues
            
        except Exception as e:
            logger.error(f"Failed to analyze readability: {e}")
            return 0.75, []
    
    async def _check_plot_coherence(self, content: str, novel_id: UUID, chapter_number: int) -> Tuple[float, List[QualityIssue]]:
        """检查情节连贯性"""
        issues = []
        score = 100.0
        
        try:
            from app.services.llm_service import llm_service
            
            # 获取前几章内容
            previous_context = await self._get_historical_context(novel_id, chapter_number)
            
            if previous_context:
                coherence_prompt = f"""请分析以下章节的情节连贯性：

前文内容：
{previous_context[:800]}

当前章节：
{content[:1000]}

请评估：
1. 情节发展是否自然合理
2. 角色行为是否符合逻辑
3. 冲突推进是否有序
4. 转折点是否合理

请按以下格式输出问题：
问题类型: 具体描述 | 严重程度: high/medium/low | 建议: 改进建议

如果情节连贯性良好，请回答"情节连贯性良好"。"""
                
                response = await llm_service.stream_invoke_with_retry(coherence_prompt)
                
                if "情节连贯性良好" not in response:
                    coherence_issues = self._parse_quality_issues(response, "plot_coherence")
                    issues.extend(coherence_issues)
                    
                    # 根据问题调整分数
                    for issue in coherence_issues:
                        if issue.severity == "high":
                            score -= 20
                        elif issue.severity == "medium":
                            score -= 10
                        else:
                            score -= 5
            
            return max(0, score / 100), issues
            
        except Exception as e:
            logger.error(f"Failed to check plot coherence: {e}")
            return 0.8, []
    
    def _parse_quality_issues(self, response: str, issue_category: str) -> List[QualityIssue]:
        """解析质量问题响应"""
        issues = []
        
        try:
            lines = response.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if '|' in line and ':' in line:
                    parts = line.split('|')
                    if len(parts) >= 3:
                        # 解析问题类型和描述
                        type_desc = parts[0].strip()
                        if ':' in type_desc:
                            issue_type, description = type_desc.split(':', 1)
                            issue_type = issue_type.strip()
                            description = description.strip()
                        else:
                            issue_type = issue_category
                            description = type_desc
                        
                        # 解析严重程度
                        severity_part = parts[1].strip()
                        severity = "medium"  # 默认值
                        if ':' in severity_part:
                            severity = severity_part.split(':', 1)[1].strip()
                        
                        # 解析建议
                        suggestion_part = parts[2].strip()
                        suggestion = suggestion_part
                        if ':' in suggestion_part:
                            suggestion = suggestion_part.split(':', 1)[1].strip()
                        
                        issues.append(QualityIssue(
                            issue_type=issue_type,
                            severity=severity,
                            description=description,
                            suggestion=suggestion,
                            location="待定位"
                        ))
            
        except Exception as e:
            logger.error(f"Failed to parse quality issues: {e}")
        
        return issues
    
    def _calculate_overall_quality(self, metrics: QualityMetrics) -> float:
        """计算综合质量分数"""
        weighted_score = (
            metrics.consistency_score * self.metric_weights["consistency_score"] +
            metrics.readability_score * self.metric_weights["readability_score"] +
            metrics.emotional_coherence * self.metric_weights["emotional_coherence"] +
            metrics.narrative_flow * self.metric_weights["narrative_flow"] +
            metrics.character_consistency * self.metric_weights["character_consistency"]
        )
        
        return round(weighted_score, 2)
    
    def get_quality_level(self, score: float) -> QualityLevel:
        """根据分数获取质量等级"""
        score_percentage = score * 100
        for level, threshold in self.quality_thresholds.items():
            if score_percentage >= threshold:
                return level
        return QualityLevel.CRITICAL
    
    async def _get_historical_context(self, novel_id: UUID, chapter_number: int) -> str:
        """获取历史上下文"""
        try:
            from app.models.chapter import Chapter
            from app.models.document import Document, DocumentType
            
            # 获取前几章内容
            recent_chapters_query = select(Chapter).where(
                and_(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number < chapter_number,
                    Chapter.chapter_number >= max(1, chapter_number - 3)
                )
            ).order_by(Chapter.chapter_number.desc())
            
            result = await self.session.execute(recent_chapters_query)
            recent_chapters = result.scalars().all()
            
            # 获取架构信息
            arch_query = select(Document).where(
                and_(
                    Document.novel_id == novel_id,
                    Document.doc_type == DocumentType.ARCHITECTURE
                )
            )
            result = await self.session.execute(arch_query)
            arch_doc = result.scalar_one_or_none()
            
            context_parts = []
            
            if arch_doc:
                context_parts.append(f"小说架构：{arch_doc.content[:300]}")
            
            for chapter in recent_chapters[:2]:  # 只取最近2章
                if chapter.content:
                    context_parts.append(f"第{chapter.chapter_number}章：{chapter.content[:200]}")
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Failed to get historical context: {e}")
            return ""
    
    def generate_quality_report(self, metrics: QualityMetrics, issues: List[QualityIssue]) -> Dict[str, Any]:
        """生成质量报告"""
        quality_level = self.get_quality_level(metrics.overall_quality)
        
        # 按严重程度分组问题
        issues_by_severity = {
            "critical": [],
            "high": [],
            "medium": [],
            "low": []
        }
        
        for issue in issues:
            severity = issue.severity if issue.severity in issues_by_severity else "medium"
            issues_by_severity[severity].append({
                "type": issue.issue_type,
                "description": issue.description,
                "suggestion": issue.suggestion,
                "location": issue.location
            })
        
        # 生成改进建议
        improvement_suggestions = []
        
        if metrics.consistency_score < 0.8:
            improvement_suggestions.append("加强内容一致性检查，确保角色行为和世界设定前后统一")
        
        if metrics.readability_score < 0.8:
            improvement_suggestions.append("优化句子长度和段落结构，提高文本可读性")
        
        if metrics.emotional_coherence < 0.8:
            improvement_suggestions.append("注意情感转换的自然性，确保角色情感状态合理发展")
        
        if metrics.narrative_flow < 0.8:
            improvement_suggestions.append("加强叙事连贯性，改善段落间的逻辑连接")
        
        if metrics.character_consistency < 0.8:
            improvement_suggestions.append("确保角色行为符合既定性格，保持角色一致性")
        
        return {
            "overall_quality": metrics.overall_quality,
            "quality_level": quality_level.value,
            "metrics": {
                "consistency": metrics.consistency_score,
                "readability": metrics.readability_score,
                "emotional_coherence": metrics.emotional_coherence,
                "narrative_flow": metrics.narrative_flow,
                "character_consistency": metrics.character_consistency
            },
            "issues": issues_by_severity,
            "improvement_suggestions": improvement_suggestions,
            "total_issues": len(issues),
            "critical_issues": len(issues_by_severity["critical"]),
            "high_priority_issues": len(issues_by_severity["high"])
        } 
