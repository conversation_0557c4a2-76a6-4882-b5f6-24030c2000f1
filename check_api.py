#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查API服务是否正常启动
"""
import requests
import time
import sys


def check_api_health():
    """检查API健康状态"""
    print("🔍 检查API服务状态...")
    
    # 等待几秒让服务完全启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    try:
        # 检查健康端点
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ API健康检查通过")
            print(f"📊 响应: {response.json()}")
            return True
        else:
            print(f"❌ API健康检查失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务 (localhost:8000)")
        return False
    except requests.exceptions.Timeout:
        print("❌ API服务响应超时")
        return False
    except Exception as e:
        print(f"❌ 检查API时发生错误: {e}")
        return False


def check_api_docs():
    """检查API文档是否可访问"""
    print("\n📚 检查API文档...")
    
    try:
        response = requests.get("http://localhost:8000/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API文档可访问")
            return True
        else:
            print(f"❌ API文档访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查API文档时发生错误: {e}")
        return False


def check_api_root():
    """检查API根路径"""
    print("\n🏠 检查API根路径...")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ API根路径可访问")
            print(f"📊 响应: {response.json()}")
            return True
        else:
            print(f"❌ API根路径访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查API根路径时发生错误: {e}")
        return False


def main():
    """主函数"""
    print("🔧 AI小说生成器 - API服务检查工具")
    print("=" * 40)
    
    # 检查各个端点
    health_ok = check_api_health()
    docs_ok = check_api_docs()
    root_ok = check_api_root()
    
    print("\n" + "=" * 40)
    print("📋 检查结果汇总:")
    print(f"  - 健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"  - API文档: {'✅ 可访问' if docs_ok else '❌ 失败'}")
    print(f"  - 根路径: {'✅ 可访问' if root_ok else '❌ 失败'}")
    
    if health_ok and docs_ok and root_ok:
        print("\n🎉 API服务运行正常！")
        print("🌐 你可以访问以下地址:")
        print("  - API服务: http://localhost:8000")
        print("  - API文档: http://localhost:8000/docs")
        print("  - 健康检查: http://localhost:8000/health")
        return True
    else:
        print("\n⚠️ API服务存在问题，请检查启动日志")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
