#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端口清理工具 - 专门用于清理被占用的端口
"""
import os
import subprocess
import time
import socket


def check_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            return result != 0  # 0表示连接成功，即端口被占用
    except Exception:
        return True  # 出现异常时假设端口可用


def find_process_using_port(port):
    """查找占用端口的进程PID"""
    try:
        if os.name == 'nt':  # Windows
            cmd = f'netstat -ano | findstr :{port}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            return int(parts[-1])  # PID是最后一列
        else:  # Unix/Linux/Mac
            cmd = f'lsof -ti:{port}'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                if pids and pids[0]:
                    return int(pids[0])
    except Exception as e:
        print(f"查找端口占用进程时出错: {e}")
    return None


def process_exists(pid):
    """检查进程是否存在"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(f'tasklist /FI "PID eq {pid}"', shell=True, capture_output=True, text=True)
            return str(pid) in result.stdout
        else:  # Unix/Linux/Mac
            result = subprocess.run(f'ps -p {pid}', shell=True, capture_output=True, text=True)
            return result.returncode == 0
    except Exception:
        return False


def kill_process_by_pid(pid):
    """根据PID终止进程"""
    try:
        if not process_exists(pid):
            print(f"进程 {pid} 不存在")
            return False
            
        if os.name == 'nt':  # Windows
            subprocess.run(f'taskkill /F /PID {pid}', shell=True, check=True)
        else:  # Unix/Linux/Mac
            subprocess.run(f'kill -9 {pid}', shell=True, check=True)
        return True
    except Exception as e:
        print(f"终止进程 {pid} 时出错: {e}")
        return False


def force_release_port_windows(port):
    """Windows特有的强制释放端口方法"""
    try:
        print(f"尝试强制释放端口 {port}...")
        
        # 重置TCP/IP栈
        print("重置TCP/IP栈...")
        subprocess.run('netsh int ip reset', shell=True, capture_output=True, timeout=10)
        time.sleep(2)
        
        # 检查端口是否释放
        if check_port_available(port):
            print(f"✅ 端口 {port} 已强制释放")
            return True
        else:
            print(f"❌ 无法强制释放端口 {port}")
            return False
            
    except Exception as e:
        print(f"强制释放端口时出错: {e}")
        return False


def cleanup_port(port):
    """清理占用指定端口的进程"""
    print(f"检查端口 {port} 是否被占用...")
    
    if check_port_available(port):
        print(f"✅ 端口 {port} 可用")
        return True
    
    print(f"⚠️ 端口 {port} 被占用，正在查找占用进程...")
    
    # 尝试多次查找进程
    pid = None
    for attempt in range(3):
        pid = find_process_using_port(port)
        if pid:
            break
        time.sleep(0.5)
    
    if pid:
        print(f"发现占用进程 PID: {pid}")
        
        if kill_process_by_pid(pid):
            print(f"✅ 已终止进程 {pid}")
            
            # 等待端口释放
            for wait_attempt in range(10):
                time.sleep(0.5)
                if check_port_available(port):
                    print(f"✅ 端口 {port} 现在可用")
                    return True
            
            print(f"❌ 端口 {port} 仍被占用")
            return False
        else:
            print(f"❌ 无法终止进程 {pid}")
            return False
    else:
        # 没有找到进程，但端口仍被占用
        print(f"⚠️ 无法找到占用端口 {port} 的进程，可能是系统缓存问题")
        
        if os.name == 'nt':
            # 在Windows上尝试强制释放端口
            if force_release_port_windows(port):
                return True
        
        print(f"等待端口自动释放...")
        
        # 等待一段时间看端口是否自动释放
        for wait_attempt in range(6):
            time.sleep(0.5)
            if check_port_available(port):
                print(f"✅ 端口 {port} 已自动释放")
                return True
        
        print(f"❌ 端口 {port} 仍被占用")
        return False


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python port_cleaner.py <端口号>")
        print("示例: python port_cleaner.py 8000")
        return
    
    try:
        port = int(sys.argv[1])
    except ValueError:
        print("错误: 端口号必须是数字")
        return
    
    print(f"端口清理工具 - 清理端口 {port}")
    print("=" * 40)
    
    success = cleanup_port(port)
    
    if success:
        print(f"\n🎉 端口 {port} 清理成功!")
    else:
        print(f"\n💥 端口 {port} 清理失败!")
        print("建议:")
        print("1. 以管理员身份运行此脚本")
        print("2. 重启计算机")
        print("3. 使用其他端口")


if __name__ == "__main__":
    main()
