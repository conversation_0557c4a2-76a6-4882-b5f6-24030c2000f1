#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI小说生成器 - FastAPI主应用入口
"""
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings
from app.core.database import init_db
from app.api.api_v1.api import api_router
from app.core.websocket import websocket_router

# 导入统一的异常处理和日志配置
from app.core.exceptions import (
    BaseAPIException, base_api_exception_handler,
    validation_exception_handler, sqlalchemy_exception_handler,
    http_exception_handler, general_exception_handler
)
from app.core.middleware import (
    RequestContextMiddleware, ResponseFormatterMiddleware,
    ErrorHandlingMiddleware, SecurityMiddleware, RateLimitMiddleware
)
from app.core.logging_config import setup_logging


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    import logging
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 AI Novel Generator backend starting up...")
    
    # 服务初始化状态
    services_status = {"database": False, "llm": False, "prompts": False}
    
    # 1. 初始化数据库
    try:
        await init_db()
        services_status["database"] = True
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        raise  # 数据库初始化失败应该阻止应用启动
    
    # 2. 初始化LLM服务
    try:
        from app.services.llm_service import initialize_default_llm_service
        llm_success = await initialize_default_llm_service()
        services_status["llm"] = llm_success
        
        if llm_success:
            logger.info("✅ LLM service initialized successfully")
        else:
            logger.warning("⚠️ LLM service initialization failed - some features may not work")
    except Exception as e:
        logger.error(f"❌ LLM service initialization error: {e}")
    
    # 3. 初始化默认提示词模板
    try:
        from app.services.prompt_service import initialize_default_prompts
        from app.core.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as session:
            prompt_success = await initialize_default_prompts(session)
            services_status["prompts"] = prompt_success
            
            if prompt_success:
                logger.info("✅ Default prompt templates initialized")
            else:
                logger.warning("⚠️ Some prompt templates failed to initialize")
    except Exception as e:
        logger.error(f"❌ Failed to initialize prompt templates: {e}")
    
    # 4. 检查依赖服务状态
    if not services_status["llm"]:
        logger.warning("🔴 Critical: LLM service unavailable - generation tasks will fail")
    
    if not services_status["prompts"]:
        logger.warning("🔴 Critical: Prompt service incomplete - may affect generation quality")
    
    # 5. 应用健康状态报告
    if all(services_status.values()):
        logger.info("🎉 All services initialized successfully - Application ready")
    else:
        failed_services = [name for name, status in services_status.items() if not status]
        logger.warning(f"⚠️ Application started with degraded functionality. Failed services: {failed_services}")
    
    logger.info("🎉 Application startup completed")
    
    yield
    
    # 关闭时清理资源
    logger.info("🔄 Application shutting down...")
    logger.info("👋 Application shutdown completed")


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    # 初始化日志配置
    setup_logging()
    
    app = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="AI小说生成器后端API",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        lifespan=lifespan,
    )

    # 注册全局异常处理器
    app.add_exception_handler(BaseAPIException, base_api_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # 添加中间件（注意顺序很重要）
    # 1. 错误处理中间件（最外层）
    app.add_middleware(ErrorHandlingMiddleware)
    
    # 2. 安全中间件
    app.add_middleware(SecurityMiddleware)
    
    # 3. 限流中间件
    app.add_middleware(RateLimitMiddleware, max_requests=100, window_seconds=60)
    
    # 4. 响应格式化中间件
    app.add_middleware(ResponseFormatterMiddleware)
    
    # 5. 请求上下文中间件
    app.add_middleware(RequestContextMiddleware)

    # 6. CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 7. 可信主机中间件（最内层）
    if settings.TRUSTED_HOSTS:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.TRUSTED_HOSTS,
        )

    # 包含API路由
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    # 包含WebSocket路由
    app.include_router(websocket_router)

    return app


app = create_application()


@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "AI Novel Generator API",
        "version": settings.VERSION,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if os.getenv("ENVIRONMENT") == "development" else False,
    ) 