#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试流式传输和状态显示问题
"""
import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def debug_analysis():
    """分析问题的根本原因"""
    print("🔍 开始分析流式传输和状态显示问题")
    print("=" * 60)
    
    # 1. 检查数据库模型
    print("📊 1. 检查数据库模型...")
    try:
        from app.models.generation_task import GenerationTask
        import inspect
        
        # 获取模型的所有字段
        fields = []
        for name, column in inspect.getmembers(GenerationTask):
            if hasattr(column, 'type'):
                fields.append(name)
        
        print(f"✅ GenerationTask模型字段: {', '.join(fields)}")
        
        # 检查是否有新字段
        new_fields = ['current_stage', 'detailed_progress', 'result_data']
        missing_fields = [field for field in new_fields if not hasattr(GenerationTask, field)]
        
        if missing_fields:
            print(f"❌ 缺少字段: {', '.join(missing_fields)}")
        else:
            print("✅ 所有新字段都存在于模型中")
            
    except Exception as e:
        print(f"❌ 模型检查失败: {e}")
    
    # 2. 检查Schema
    print("\n📋 2. 检查TaskResponse Schema...")
    try:
        from app.schemas.task import TaskResponse
        import inspect
        
        schema_fields = TaskResponse.model_fields.keys()
        print(f"✅ TaskResponse Schema字段: {', '.join(schema_fields)}")
        
        # 检查新字段
        new_schema_fields = ['current_stage', 'detailed_progress', 'result_data']
        missing_schema_fields = [field for field in new_schema_fields if field not in schema_fields]
        
        if missing_schema_fields:
            print(f"❌ Schema缺少字段: {', '.join(missing_schema_fields)}")
        else:
            print("✅ Schema包含所有新字段")
            
    except Exception as e:
        print(f"❌ Schema检查失败: {e}")
    
    # 3. 检查LLM服务流式支持
    print("\n🤖 3. 检查LLM服务流式支持...")
    try:
        from app.services.llm_service import LLMService
        
        # 检查是否有流式方法
        has_stream_method = hasattr(LLMService, 'stream_invoke_with_retry')
        print(f"✅ LLMService有流式方法: {has_stream_method}")
        
        if has_stream_method:
            method = getattr(LLMService, 'stream_invoke_with_retry')
            sig = inspect.signature(method)
            print(f"✅ 流式方法签名: {sig}")
        
    except Exception as e:
        print(f"❌ LLM服务检查失败: {e}")
    
    # 4. 检查WebSocket支持
    print("\n🔌 4. 检查WebSocket支持...")
    try:
        from app.core.websocket import send_task_update
        import inspect
        
        sig = inspect.signature(send_task_update)
        print(f"✅ WebSocket send_task_update签名: {sig}")
        
        # 检查参数
        params = list(sig.parameters.keys())
        new_params = ['current_stage', 'detailed_progress']
        missing_params = [param for param in new_params if param not in params]
        
        if missing_params:
            print(f"❌ WebSocket缺少参数: {', '.join(missing_params)}")
        else:
            print("✅ WebSocket支持所有新参数")
            
    except Exception as e:
        print(f"❌ WebSocket检查失败: {e}")
    
    # 5. 分析具体问题
    print("\n🔍 5. 问题分析...")
    
    # 分析用户提供的日志
    print("📝 基于用户日志的分析:")
    print("   - 登录成功，配额0/10 ✅")
    print("   - 小说创建成功 ✅") 
    print("   - 任务启动成功 ✅")
    print("   - 进度更新：0% → 20% → 40% ✅")
    print("   - 但缺少详细状态信息 ❌")
    print("   - 使用轮询模式而非WebSocket ❌")
    
    print("\n💡 问题原因:")
    print("1. 测试脚本使用的是基础版本（轮询模式），而不是WebSocket模式")
    print("2. 数据库可能还未包含新字段（需要迁移）")
    print("3. Celery Worker可能使用旧代码（需要重启）")
    print("4. API响应中不包含详细状态字段")
    
    print("\n🔧 解决方案:")
    print("1. 运行数据库迁移：alembic upgrade head")
    print("2. 重启Celery Worker")
    print("3. 使用WebSocket测试脚本验证流式功能") 
    print("4. 检查任务状态API是否返回新字段")
    
    # 6. 生成测试建议
    print("\n📋 6. 测试建议...")
    test_steps = [
        "停止当前的API和Worker进程",
        "运行数据库迁移",
        "重启API服务",
        "重启Worker进程", 
        "使用WebSocket测试脚本测试",
        "检查API响应是否包含详细字段"
    ]
    
    for i, step in enumerate(test_steps, 1):
        print(f"{i}. {step}")
    
    print("\n" + "=" * 60)
    print("🎯 总结：问题主要是数据库字段缺失和使用了轮询模式而非WebSocket")

if __name__ == "__main__":
    asyncio.run(debug_analysis()) 