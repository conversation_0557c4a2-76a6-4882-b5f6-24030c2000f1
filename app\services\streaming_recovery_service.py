#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
流式传输恢复服务 - 处理中断恢复和断点续传
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from app.services.streaming_content_service import StreamingContentService
from app.models.streaming_content import ContentRecoveryPoint, StreamingMetrics
from app.core.websocket import manager, send_error_recovery

logger = logging.getLogger(__name__)


class StreamingRecoveryService:
    """流式传输恢复服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.streaming_service = StreamingContentService(session)
    
    async def detect_interrupted_sessions(self) -> List[Dict[str, Any]]:
        """检测中断的流式传输会话"""
        try:
            # 查找活跃但可能中断的恢复点
            query = select(ContentRecoveryPoint).where(
                ContentRecoveryPoint.is_active == True
            ).order_by(desc(ContentRecoveryPoint.updated_at))
            
            result = await self.session.execute(query)
            recovery_points = result.scalars().all()
            
            interrupted_sessions = []
            for point in recovery_points:
                # 检查是否有对应的活跃WebSocket连接
                connection_count = manager.get_task_connections_count(point.task_id)
                
                if connection_count == 0:
                    # 没有活跃连接，可能是中断的会话
                    interrupted_sessions.append({
                        "task_id": point.task_id,
                        "stage": point.stage,
                        "last_chunk_index": point.last_chunk_index,
                        "accumulated_content": point.accumulated_content,
                        "total_length": point.total_length,
                        "last_update": point.updated_at
                    })
            
            return interrupted_sessions
            
        except Exception as e:
            logger.error(f"Failed to detect interrupted sessions: {e}")
            return []
    
    async def recover_session(self, task_id: str, stage: str) -> Optional[Dict[str, Any]]:
        """恢复中断的流式传输会话"""
        try:
            # 获取恢复信息
            recovery_info = await self.streaming_service.recover_streaming_session(task_id, stage)
            
            if not recovery_info:
                logger.warning(f"No recovery information found for task {task_id}, stage {stage}")
                return None
            
            # 发送恢复通知
            await send_error_recovery(
                task_id, 
                f"会话已恢复，从第{recovery_info['last_chunk_index']}个片段继续",
                "session_recovered"
            )
            
            logger.info(f"Successfully recovered session for task {task_id}, stage {stage}")
            return recovery_info
            
        except Exception as e:
            logger.error(f"Failed to recover session for task {task_id}, stage {stage}: {e}")
            return None
    
    async def validate_content_integrity(self, task_id: str, stage: str) -> Dict[str, Any]:
        """验证内容完整性"""
        try:
            # 获取累积内容
            accumulated_content = await self.streaming_service.get_accumulated_content(task_id, stage)
            
            if not accumulated_content:
                return {
                    "is_valid": False,
                    "error": "No accumulated content found"
                }
            
            # 获取恢复点信息
            recovery_info = await self.streaming_service.recover_streaming_session(task_id, stage)
            
            if not recovery_info:
                return {
                    "is_valid": False,
                    "error": "No recovery point found"
                }
            
            # 验证长度一致性
            expected_length = recovery_info["total_length"]
            actual_length = len(accumulated_content)
            
            length_match = expected_length == actual_length
            
            # 计算完整性评分
            integrity_score = 1.0 if length_match else actual_length / expected_length if expected_length > 0 else 0.0
            
            return {
                "is_valid": length_match,
                "integrity_score": integrity_score,
                "expected_length": expected_length,
                "actual_length": actual_length,
                "content_preview": accumulated_content[:200] + "..." if len(accumulated_content) > 200 else accumulated_content
            }
            
        except Exception as e:
            logger.error(f"Failed to validate content integrity: {e}")
            return {
                "is_valid": False,
                "error": str(e)
            }
    
    async def cleanup_completed_sessions(self, max_age_hours: int = 24) -> int:
        """清理已完成的会话数据"""
        try:
            from datetime import datetime, timezone, timedelta
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
            
            # 查找需要清理的恢复点
            query = select(ContentRecoveryPoint).where(
                and_(
                    ContentRecoveryPoint.is_active == False,
                    ContentRecoveryPoint.updated_at < cutoff_time
                )
            )
            
            result = await self.session.execute(query)
            old_recovery_points = result.scalars().all()
            
            cleanup_count = 0
            for point in old_recovery_points:
                # 删除相关的内容片段
                from app.models.streaming_content import StreamingContent
                content_query = select(StreamingContent).where(
                    and_(
                        StreamingContent.task_id == point.task_id,
                        StreamingContent.stage == point.stage
                    )
                )
                content_result = await self.session.execute(content_query)
                content_chunks = content_result.scalars().all()
                
                for chunk in content_chunks:
                    await self.session.delete(chunk)
                
                # 删除恢复点
                await self.session.delete(point)
                cleanup_count += 1
            
            await self.session.commit()
            logger.info(f"Cleaned up {cleanup_count} completed streaming sessions")
            return cleanup_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup completed sessions: {e}")
            await self.session.rollback()
            return 0
    
    async def get_streaming_metrics(self, task_id: str, stage: str = None) -> Optional[Dict[str, Any]]:
        """获取流式传输指标"""
        try:
            query = select(StreamingMetrics).where(StreamingMetrics.task_id == task_id)
            
            if stage:
                query = query.where(StreamingMetrics.stage == stage)
            
            result = await self.session.execute(query)
            metrics_list = result.scalars().all()
            
            if not metrics_list:
                return None
            
            # 聚合指标
            total_chunks = sum(m.total_chunks for m in metrics_list)
            total_bytes = sum(m.total_bytes for m in metrics_list)
            total_errors = sum(m.error_count for m in metrics_list)
            total_retries = sum(m.retry_count for m in metrics_list)
            total_recoveries = sum(m.recovery_count for m in metrics_list)
            
            avg_completion_rate = sum(m.completion_rate for m in metrics_list) / len(metrics_list)
            avg_integrity_score = sum(m.data_integrity_score for m in metrics_list) / len(metrics_list)
            
            return {
                "task_id": task_id,
                "stage": stage,
                "total_chunks": total_chunks,
                "total_bytes": total_bytes,
                "error_count": total_errors,
                "retry_count": total_retries,
                "recovery_count": total_recoveries,
                "completion_rate": avg_completion_rate,
                "data_integrity_score": avg_integrity_score,
                "stages": len(metrics_list)
            }
            
        except Exception as e:
            logger.error(f"Failed to get streaming metrics: {e}")
            return None
