# Redis配置文件 - AI小说生成器后端系统

# 基础配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile ""

# 数据库数量
databases 16

# 安全配置
# requirepass your_redis_password  # 生产环境请启用密码

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 客户端连接
maxclients 10000

# AOF持久化
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# 性能优化
tcp-backlog 511
hz 10 