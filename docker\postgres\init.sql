-- PostgreSQL数据库初始化脚本
-- AI小说生成器后端系统

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建数据库（如果不存在）
-- 注意：在docker-entrypoint-initdb.d中，数据库已经由POSTGRES_DB创建

-- 设置默认权限
GRANT ALL PRIVILEGES ON DATABASE ai_novel_generator TO novel_user;

-- 创建序列和函数用于更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 输出初始化完成信息
\echo 'PostgreSQL数据库初始化完成'
\echo '数据库: ai_novel_generator'
\echo '用户: novel_user'
\echo '时区: Asia/Shanghai' 