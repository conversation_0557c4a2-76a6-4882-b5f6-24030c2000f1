# ====================================================================
# AI小说生成器后端系统 - Dockerfile
# 
# 用途：构建FastAPI应用和Celery Worker的Docker镜像
# ====================================================================

# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    TZ=Asia/Shanghai

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY app/requirements.txt /app/requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . /app/

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads /app/chroma_db

# 设置权限
RUN chmod +x /app/scripts/*.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 默认启动命令（可被docker-compose覆盖）
CMD ["python", "scripts/start_api.py"] 