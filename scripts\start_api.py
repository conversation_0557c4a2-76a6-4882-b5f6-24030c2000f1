#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - API服务启动脚本
支持开发和生产环境配置
"""
import os
import sys
import subprocess
import asyncio
import asyncpg
import redis
from pathlib import Path


def check_environment():
    """检查运行环境"""
    print("正在检查运行环境...")
    
    # 检查是否在项目根目录
    if not os.path.exists("app"):
        print("错误：请在项目根目录运行此脚本")
        return False
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误：需要Python 3.8或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查虚拟环境
    if not os.environ.get('VIRTUAL_ENV'):
        print("警告：未检测到虚拟环境，建议先激活虚拟环境")
    else:
        print(f"✓ 虚拟环境: {os.environ['VIRTUAL_ENV']}")
    
    return True


def setup_environment():
    """设置环境变量"""
    # 设置Python路径
    current_dir = os.getcwd()
    python_path = os.environ.get('PYTHONPATH', '')
    if current_dir not in python_path:
        os.environ['PYTHONPATH'] = f"{current_dir}:{python_path}" if python_path else current_dir
    
    # 设置默认环境变量（如果不存在）
    defaults = {
        'ENVIRONMENT': 'development',
        'DEBUG': 'true',
        'PROJECT_NAME': 'AI Novel Generator',
        'VERSION': '1.0.0',
        'API_V1_STR': '/api/v1',
        'SECRET_KEY': 'your-secret-key-change-in-production',
        'ACCESS_TOKEN_EXPIRE_MINUTES': '60',
        'DATABASE_URL': 'postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator',
        'REDIS_URL': 'redis://localhost:6379/0',
        'BACKEND_CORS_ORIGINS': '["http://localhost:3000", "http://localhost:8080"]'
    }
    
    for key, value in defaults.items():
        if not os.environ.get(key):
            os.environ[key] = value
            print(f"设置默认环境变量: {key}")


async def check_database_connection():
    """检查数据库连接"""
    try:
        database_url = os.environ.get('DATABASE_URL', '')
        if not database_url:
            print("警告：DATABASE_URL未设置")
            return False
        
        # 解析数据库URL
        if database_url.startswith('postgresql+asyncpg://'):
            pg_url = database_url.replace('postgresql+asyncpg://', 'postgresql://')
        else:
            pg_url = database_url
        
        conn = await asyncpg.connect(pg_url)
        await conn.close()
        print("✓ 数据库连接成功")
        return True
        
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False


def check_redis_connection():
    """检查Redis连接"""
    try:
        redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
        r = redis.from_url(redis_url)
        r.ping()
        print("✓ Redis连接成功")
        return True
        
    except Exception as e:
        print(f"✗ Redis连接失败: {e}")
        return False


async def check_dependencies():
    """检查服务依赖"""
    print("正在检查服务依赖...")
    
    db_ok = await check_database_connection()
    redis_ok = check_redis_connection()
    
    if not db_ok:
        print("警告：数据库连接失败，某些功能可能无法正常工作")
    
    if not redis_ok:
        print("警告：Redis连接失败，任务队列功能可能无法正常工作")
    
    return db_ok and redis_ok


def get_python_executable():
    """获取正确的Python解释器路径"""
    # 检查是否在虚拟环境中
    venv_path = os.environ.get('VIRTUAL_ENV')
    if venv_path:
        # 在虚拟环境中，使用虚拟环境的Python
        if os.name == 'nt':  # Windows
            return os.path.join(venv_path, 'Scripts', 'python.exe')
        else:  # Unix/Linux/Mac
            return os.path.join(venv_path, 'bin', 'python')
    else:
        # 检查是否存在项目本地的虚拟环境
        current_dir = os.getcwd()
        possible_venv_names = ['novel_ai_env', 'venv', 'env', '.venv']
        
        for venv_name in possible_venv_names:
            venv_dir = os.path.join(current_dir, venv_name)
            if os.path.exists(venv_dir):
                if os.name == 'nt':  # Windows
                    python_exe = os.path.join(venv_dir, 'Scripts', 'python.exe')
                else:  # Unix/Linux/Mac
                    python_exe = os.path.join(venv_dir, 'bin', 'python')
                
                if os.path.exists(python_exe):
                    print(f"✓ 找到本地虚拟环境: {venv_dir}")
                    return python_exe
        
        # 没有找到虚拟环境，使用当前Python解释器
        print("警告：未找到虚拟环境，使用当前Python解释器")
        return sys.executable


def start_api_server():
    """启动API服务器"""
    print("正在启动API服务器...")
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', '8000'))
    environment = os.environ.get('ENVIRONMENT', 'development')
    
    # 获取正确的Python解释器
    python_exe = get_python_executable()
    
    # 构建启动命令
    if environment == 'development':
        # 开发环境使用reload
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            f"--host={host}",
            f"--port={port}",
            "--reload",
            "--log-level=info"
        ]
    else:
        # 生产环境配置
        workers = int(os.environ.get('WORKERS', '4'))
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            f"--host={host}",
            f"--port={port}",
            f"--workers={workers}",
            "--log-level=info"
        ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print(f"服务将在 http://{host}:{port} 启动")
    print("按 Ctrl+C 停止服务")
    
    try:
        # 启动服务
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n正在停止API服务...")
        print("API服务已停止")
        
    except subprocess.CalledProcessError as e:
        print(f"启动API服务时出错: {e}")
        return False
    
    return True


async def main():
    """主函数"""
    print("AI小说生成器 - API服务启动脚本")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 检查依赖
    deps_ok = await check_dependencies()
    if not deps_ok:
        print("警告：部分依赖服务不可用，服务可能无法正常工作")
        response = input("是否继续启动？(y/N): ").strip().lower()
        if response != 'y':
            print("启动已取消")
            sys.exit(1)
    
    # 启动API服务
    if not start_api_server():
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 