#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
认证相关的数据传输对象
"""
from typing import Optional
from uuid import UUID
from pydantic import BaseModel, Field


class WeChatLoginRequest(BaseModel):
    """微信登录请求"""
    code: str = Field(..., description="微信登录凭证")
    nickname: Optional[str] = Field(None, description="用户昵称")
    avatar_url: Optional[str] = Field(None, description="用户头像URL")


class UserResponse(BaseModel):
    """用户响应对象"""
    id: UUID
    wx_openid: str
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    quota_used: int
    quota_limit: int
    is_vip: bool
    
    model_config = {"from_attributes": True}


class UserUpdateRequest(BaseModel):
    """用户更新请求"""
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    settings: Optional[dict] = None


class TokenResponse(BaseModel):
    """Token响应对象"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    user: UserResponse = Field(..., description="用户信息")


class TokenData(BaseModel):
    """Token数据"""
    user_id: Optional[UUID] = None