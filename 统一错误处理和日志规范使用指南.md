# AI小说生成器 - 统一错误处理和日志规范使用指南

## 📋 概述

本文档介绍了新的统一错误处理和日志规范系统，目的是解决以下问题：
- 不统一的异常类型和错误信息
- 日志格式不规范，缺少追踪信息
- 缺少全局异常处理机制
- 敏感信息泄露风险

## 🔧 系统架构

### 核心组件
1. **统一异常处理模块** (`app/core/exceptions.py`)
2. **日志配置模块** (`app/core/logging_config.py`)  
3. **中间件模块** (`app/core/middleware.py`)
4. **全局异常处理器** (在 `main.py` 中注册)

---

## 🚨 异常处理规范

### 1. 统一异常类型

#### 基础异常类
```python
from app.core.exceptions import (
    BaseAPIException,           # 所有自定义异常的基类
    ValidationException,        # 数据验证异常
    NotFoundException,          # 资源不存在异常
    PermissionDeniedException,  # 权限拒绝异常
    QuotaExceededException,     # 配额超限异常
    AuthenticationException,    # 认证异常
    DatabaseException,          # 数据库异常
    LLMServiceException,        # LLM服务异常
    BusinessException          # 业务逻辑异常
)
```

#### 错误代码常量
```python
from app.core.exceptions import ErrorCode

# 使用预定义的错误代码
raise NotFoundException(
    message="小说不存在或无权限访问",
    error_code=ErrorCode.NOVEL_NOT_FOUND
)
```

### 2. API端点异常处理最佳实践

#### ✅ 推荐写法
```python
from fastapi import APIRouter, Depends, Request
from app.core.exceptions import NotFoundException, QuotaExceededException, ErrorCode
from app.core.logging_config import get_logger, log_business_operation

logger = get_logger(__name__)

@router.get("/{novel_id}")
async def get_novel(
    novel_id: int,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 业务逻辑
        novel = await get_novel_by_id(novel_id, user_id, session)
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 记录成功操作
        log_business_operation(
            logger=logger.logger,
            operation="get_novel",
            details={"novel_id": novel_id, "title": novel.title},
            user_id=user_id,
            request_id=request_id
        )
        
        return NovelResponse.from_orm(novel)
        
    except NotFoundException:
        # 让全局异常处理器处理
        raise
    except Exception as e:
        # 记录意外错误
        logger.error(f"获取小说失败 - 用户ID: {user_id}, 小说ID: {novel_id}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_novel',
            'error': str(e)
        })
        raise DatabaseException(
            message="获取小说失败",
            details={"novel_id": novel_id}
        )
```

#### ❌ 不推荐写法 (旧方式)
```python
# 不要再使用这种方式
try:
    # 业务逻辑
    pass
except Exception as e:
    logger.error(f"Error: {e}")  # 日志格式不规范
    raise HTTPException(          # 直接使用HTTPException
        status_code=400,
        detail="操作失败"         # 错误信息不明确
    )
```

---

## 📝 日志规范

### 1. 日志初始化

#### 获取日志记录器
```python
from app.core.logging_config import get_logger

# 获取带上下文的日志记录器
logger = get_logger(__name__)

# 或者带额外上下文
logger = get_logger(__name__, service="novel_service", version="1.0")
```

### 2. 日志记录最佳实践

#### 结构化日志记录
```python
from app.core.logging_config import log_business_operation, log_error

# 记录业务操作
log_business_operation(
    logger=logger.logger,
    operation="create_novel",
    details={
        "novel_id": novel.id,
        "title": novel.title,
        "genre": novel.genre
    },
    user_id=user_id,
    request_id=request_id
)

# 记录错误
log_error(
    logger=logger.logger,
    error=exception,
    context={
        "operation": "generate_chapter",
        "novel_id": novel_id,
        "chapter_number": 1
    },
    user_id=user_id,
    request_id=request_id
)
```

#### 标准日志格式
```python
# ✅ 推荐 - 使用extra参数传递结构化数据
logger.info("用户创建小说成功", extra={
    'request_id': request_id,
    'user_id': user_id,
    'operation': 'create_novel',
    'novel_id': novel.id,
    'title': novel.title
})

# ❌ 不推荐 - 简单字符串拼接
logger.info(f"User {user_id} created novel {novel.id}")
```

### 3. 日志级别使用规范

#### DEBUG
```python
logger.debug("详细的调试信息", extra={
    'request_id': request_id,
    'operation': 'debug_info'
})
```

#### INFO
```python
# 记录重要的业务操作
logger.info("用户登录成功", extra={
    'request_id': request_id,
    'user_id': user_id,
    'operation': 'user_login'
})
```

#### WARNING
```python
# 记录潜在问题
logger.warning("用户配额即将用完", extra={
    'request_id': request_id,
    'user_id': user_id,
    'operation': 'quota_warning',
    'quota_used': user.quota_used,
    'quota_limit': user.quota_limit
})
```

#### ERROR
```python
# 记录错误，使用log_error函数
log_error(
    logger=logger.logger,
    error=exception,
    context={"additional": "context"},
    user_id=user_id,
    request_id=request_id
)
```

---

## 🌐 中间件系统

### 自动添加的功能
1. **请求追踪**: 自动生成 `request_id`
2. **响应格式化**: 统一响应格式
3. **错误处理**: 捕获未处理异常
4. **安全头**: 添加安全相关的HTTP头
5. **限流**: 基于IP的简单限流
6. **日志记录**: 自动记录所有API调用

### 中间件配置
```python
# 在 main.py 中已配置，按以下顺序执行：
app.add_middleware(ErrorHandlingMiddleware)      # 最外层
app.add_middleware(SecurityMiddleware)
app.add_middleware(RateLimitMiddleware, max_requests=100, window_seconds=60)
app.add_middleware(ResponseFormatterMiddleware)
app.add_middleware(RequestContextMiddleware)
app.add_middleware(CORSMiddleware, ...)         # 最内层
```

---

## 📊 响应格式规范

### 成功响应
```json
{
  "success": true,
  "data": {
    // 实际数据
  },
  "message": "操作成功",
  "timestamp": "2025-01-01T00:00:00Z",
  "request_id": "abc12345"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "NOVEL_001",
    "message": "小说不存在或无权限访问",
    "details": {
      "novel_id": 123
    }
  },
  "timestamp": "2025-01-01T00:00:00Z",
  "request_id": "abc12345",
  "path": "/api/v1/novels/123"
}
```

---

## 🔒 安全最佳实践

### 1. 敏感信息过滤
日志系统会自动过滤以下敏感信息：
- password, token, api_key, secret
- authorization, access_token, refresh_token
- jwt, openid, private_key

### 2. 自定义敏感数据过滤
```python
# 在logging_config.py中修改SensitiveDataFilter类
class SensitiveDataFilter(logging.Filter):
    SENSITIVE_KEYS = {
        'password', 'token', 'api_key', 'secret',
        'your_custom_sensitive_field'  # 添加自定义敏感字段
    }
```

---

## 🔧 服务层异常处理

### LLM服务异常处理
```python
from app.services.llm_service import llm_service
from app.core.exceptions import LLMServiceException

try:
    result = await llm_service.invoke_with_retry(
        prompt=prompt,
        adapter_name="openai",
        request_id=request_id
    )
except LLMServiceException as e:
    # LLM服务异常会被自动处理
    raise
```

### 数据库服务异常处理
```python
from app.core.exceptions import DatabaseException

try:
    await session.commit()
except Exception as e:
    await session.rollback()
    raise DatabaseException(
        message="数据保存失败",
        details={"operation": "commit"}
    )
```

---

## 📈 监控和调试

### 1. 日志文件位置
```
logs/
├── app.log           # 应用日志
├── error.log         # 错误日志（JSON格式）
└── access.log        # API访问日志（JSON格式）
```

### 2. 日志分析
```bash
# 查看特定请求的日志
grep "abc12345" logs/app.log

# 查看特定用户的操作
grep "user_123" logs/app.log

# 查看特定操作的日志
jq '.operation' logs/error.log | grep "create_novel"
```

### 3. 调试模式
```python
# 在开发环境中，设置DEBUG=True会输出更详细的日志
# 在.env文件中：
DEBUG=true
LOG_LEVEL=DEBUG
```

---

## 🧪 测试建议

### 1. 异常处理测试
```python
import pytest
from app.core.exceptions import NotFoundException, ErrorCode

def test_novel_not_found():
    with pytest.raises(NotFoundException) as exc_info:
        raise NotFoundException(
            message="小说不存在",
            error_code=ErrorCode.NOVEL_NOT_FOUND
        )
    
    assert exc_info.value.error_code == ErrorCode.NOVEL_NOT_FOUND
    assert exc_info.value.status_code == 404
```

### 2. 日志测试
```python
def test_logging(caplog):
    logger = get_logger(__name__)
    
    logger.info("测试日志", extra={
        'request_id': 'test123',
        'operation': 'test'
    })
    
    assert "测试日志" in caplog.text
    assert "test123" in caplog.text
```

---

## 📚 迁移指南

### 从旧系统迁移

#### 1. 更新导入
```python
# 旧方式
import logging
from fastapi import HTTPException

# 新方式  
from app.core.logging_config import get_logger
from app.core.exceptions import NotFoundException, ErrorCode
```

#### 2. 更新异常处理
```python
# 旧方式
raise HTTPException(status_code=404, detail="Not found")

# 新方式
raise NotFoundException(
    message="资源不存在",
    error_code=ErrorCode.RESOURCE_NOT_FOUND
)
```

#### 3. 更新日志记录
```python
# 旧方式
logger = logging.getLogger(__name__)
logger.error(f"Error: {e}")

# 新方式
logger = get_logger(__name__)
logger.error("操作失败", extra={
    'request_id': request_id,
    'user_id': user_id,
    'operation': 'operation_name',
    'error': str(e)
})
```

---

## ⚠️ 注意事项

1. **中间件顺序**: 中间件的添加顺序很重要，不要随意调整
2. **异常链**: 让自定义异常向上传播，由全局处理器统一处理
3. **日志性能**: 在高频操作中避免记录过多详细日志
4. **敏感数据**: 确保不在日志中记录密码、令牌等敏感信息
5. **请求ID**: 始终传递request_id以便追踪完整的请求链路

---

## 🔗 相关文件

- `app/core/exceptions.py` - 统一异常定义
- `app/core/logging_config.py` - 日志配置
- `app/core/middleware.py` - 中间件实现  
- `app/main.py` - 应用配置
- `统一错误处理和日志规范使用指南.md` - 本文档

---

**最后更新**: 2025年6月19日  
**文档版本**: v1.0 