#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
章节相关数据传输对象
"""
from typing import Optional, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel


class ChapterCreate(BaseModel):
    """创建章节请求"""
    novel_id: UUID
    title: str
    content: Optional[str] = None
    summary: Optional[str] = None


class ChapterUpdate(BaseModel):
    """更新章节请求"""
    title: Optional[str] = None
    content: Optional[str] = None
    summary: Optional[str] = None
    status: Optional[str] = None


class ChapterResponse(BaseModel):
    """章节响应对象"""
    id: UUID
    novel_id: UUID
    chapter_number: int
    title: str
    content: Optional[str] = None
    summary: Optional[str] = None
    status: str
    version: int = 1
    parent_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class ChapterListResponse(BaseModel):
    """章节列表响应"""
    chapters: List[ChapterResponse]
    total: int
    page: int
    per_page: int 
