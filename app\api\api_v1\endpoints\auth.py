#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
认证相关API端点
"""
from fastapi import APIRouter, Depends, Request, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_async_db
from app.core.config import settings
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    AuthenticationException, DatabaseException, ErrorCode
)
from app.models.user import User
from app.schemas.auth import WeChatLoginRequest, TokenResponse, UserResponse
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.post("/wechat/login", response_model=TokenResponse, summary="微信小程序登录")
async def wechat_login(
    login_data: WeChatLoginRequest,
    request: Request,
    session: AsyncSession = Depends(get_async_db)
):
    """
    微信小程序登录
    
    通过微信小程序的code获取用户信息并返回JWT token
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    try:
        auth_service = AuthService(session)
        result = await auth_service.wechat_login(
            code=login_data.code,
            nickname=login_data.nickname,
            avatar_url=login_data.avatar_url
        )
        
        # 记录登录成功日志
        log_business_operation(
            logger=logger.logger,
            operation="wechat_login",
            details={
                "code": login_data.code[:10] + "..." if login_data.code else None,
                "nickname": login_data.nickname,
                "success": True
            },
            user_id=str(result.user.id) if hasattr(result, 'user') and result.user else 'unknown',
            request_id=request_id
        )
        
        return result
        
    except AuthenticationException:
        raise
    except Exception as e:
        logger.error(f"微信登录失败 - 错误: {str(e)}", extra={
            'request_id': request_id,
            'operation': 'wechat_login',
            'code': login_data.code[:10] + "..." if login_data.code else None
        })
        raise AuthenticationException(
            message="登录失败，请重试",
            error_code=ErrorCode.AUTH_WECHAT_LOGIN_FAILED
        )


@router.post("/refresh", response_model=TokenResponse, summary="刷新Token")
async def refresh_token(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    刷新访问令牌
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        auth_service = AuthService(session)
        new_token = await auth_service.create_access_token(data={"sub": str(current_user.id)})
        
        # 记录令牌刷新日志
        log_business_operation(
            logger=logger.logger,
            operation="refresh_token",
            details={"user_id": user_id},
            user_id=user_id,
            request_id=request_id
        )
        
        return TokenResponse(
            access_token=new_token,
            token_type="bearer",
            user=UserResponse.model_validate(current_user)
        )
        
    except Exception as e:
        logger.error(f"令牌刷新失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'refresh_token'
        })
        raise AuthenticationException(
            message="无法刷新令牌",
            error_code=ErrorCode.AUTH_TOKEN_EXPIRED
        )


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    获取当前登录用户的信息
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    # 记录获取用户信息日志
    log_business_operation(
        logger=logger.logger,
        operation="get_current_user_info",
        details={"user_id": user_id},
        user_id=user_id,
        request_id=request_id
    )
    
    return UserResponse.model_validate(current_user)


@router.post("/logout", summary="登出")
async def logout(
    request: Request,
    current_user: User = Depends(AuthService.get_current_user)
):
    """
    用户登出
    
    注意：由于JWT是无状态的，实际的token失效需要客户端主动删除
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    # 记录登出日志
    log_business_operation(
        logger=logger.logger,
        operation="logout",
        details={"user_id": user_id},
        user_id=user_id,
        request_id=request_id
    )
    
    return {"message": "登出成功"}


@router.post("/test/login", response_model=TokenResponse, summary="测试登录（仅开发环境）")
async def test_login(
    login_data: WeChatLoginRequest,
    request: Request,
    session: AsyncSession = Depends(get_async_db)
):
    """
    测试登录端点 - 仅用于开发环境测试
    不进行微信API验证，直接创建测试用户
    """
    if not settings.DEBUG:
        raise HTTPException(
            status_code=403,
            detail="测试端点仅在开发模式下可用"
        )
    
    request_id = getattr(request.state, 'request_id', 'unknown')
    
    try:
        # 生成测试openid
        test_openid = f"test_openid_{login_data.code[10:] if login_data.code.startswith('test_code_') else 'default'}"
        
        # 直接创建或获取测试用户
        auth_service = AuthService(session)
        user = await auth_service._get_or_create_user(
            openid=test_openid,
            nickname=login_data.nickname or "AI小说测试用户",
            avatar_url=login_data.avatar_url
        )
        
        # 生成JWT token
        access_token = await auth_service.create_access_token(data={"sub": str(user.id)})
        
        result = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse.model_validate(user)
        )
        
        # 记录测试登录成功日志
        log_business_operation(
            logger=logger.logger,
            operation="test_login",
            details={
                "code": login_data.code[:10] + "..." if login_data.code else None,
                "nickname": login_data.nickname,
                "test_openid": test_openid,
                "success": True
            },
            user_id=str(user.id),
            request_id=request_id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"测试登录失败 - 错误: {str(e)}", extra={
            'request_id': request_id,
            'operation': 'test_login',
            'code': login_data.code[:10] + "..." if login_data.code else None
        })
        raise AuthenticationException(
            message="测试登录失败",
            error_code=ErrorCode.AUTH_WECHAT_LOGIN_FAILED
        ) 