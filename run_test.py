#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 快速测试运行脚本
自动选择合适的测试版本并运行
"""

import asyncio
import sys
import subprocess
import os
from typing import Optional


def check_dependencies() -> bool:
    """检查必要的依赖是否安装"""
    required_packages = ['aiohttp', 'websockets']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要依赖: {', '.join(missing_packages)}")
        print(f"💡 请运行: pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_service_availability() -> bool:
    """检查后端服务是否可用"""
    try:
        import requests
        # 尝试多个可能的端口
        ports = [8000, 8001]
        for port in ports:
            try:
                response = requests.get(f"http://localhost:{port}/health", timeout=5)
                if response.status_code == 200:
                    print(f"✅ 发现后端服务运行在端口 {port}")
                    return True
            except Exception:
                continue
        return False
    except Exception:
        return False


async def run_basic_test():
    """运行基础版本测试"""
    print("🔄 运行基础版本测试（轮询模式）...")
    try:
        from test_novel_generation_flow import run_novel_generation_test
        return await run_novel_generation_test()
    except ImportError:
        print("❌ 无法导入基础测试脚本")
        return 1


async def run_enhanced_test():
    """运行增强版本测试"""
    print("🚀 运行增强版本测试（WebSocket模式）...")
    try:
        from test_novel_websocket import run_enhanced_novel_generation_test
        return await run_enhanced_novel_generation_test()
    except ImportError:
        print("❌ 无法导入增强测试脚本")
        return 1


def print_menu():
    """打印菜单选项"""
    print("\n🤖 AI小说生成器 - 测试脚本快速启动器")
    print("=" * 50)
    print("请选择测试版本:")
    print("1. 🔄 基础版本 (轮询模式)")
    print("   - 稳定可靠，兼容性好")
    print("   - 适合网络不稳定环境")
    print()
    print("2. 🚀 增强版本 (WebSocket模式) [推荐]")
    print("   - 实时进度更新")
    print("   - 更好的用户体验")
    print()
    print("3. 🔧 环境检查")
    print("4. 📖 查看使用说明")
    print("0. 退出")
    print("=" * 50)


def show_usage():
    """显示使用说明"""
    print("\n📖 使用说明")
    print("=" * 50)
    print("1. 确保后端服务正在运行:")
    print("   - 启动基础服务: docker-compose up -d postgres redis")
    print("   - 启动API服务: python scripts/start_api.py")
    print("   - 启动Worker: python scripts/start_worker_windows.py")
    print()
    print("2. 安装测试依赖:")
    print("   pip install aiohttp websockets")
    print()
    print("3. 运行测试:")
    print("   - 基础版本: python test_novel_generation_flow.py")
    print("   - 增强版本: python test_novel_websocket.py")
    print("   - 快速启动: python run_test.py")
    print()
    print("4. 查看详细文档:")
    print("   README_测试脚本使用指南.md")
    print("=" * 50)


def check_environment():
    """环境检查"""
    print("\n🔧 环境检查")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info >= (3, 8):
        print(f"✅ Python版本: {sys.version}")
    else:
        print(f"❌ Python版本过低: {sys.version}")
        print("   需要Python 3.8或更高版本")
    
    # 检查依赖包
    print("\n📦 依赖检查:")
    if check_dependencies():
        print("✅ 所有依赖已安装")
    
    # 检查服务状态
    print("\n🌐 服务检查:")
    if check_service_availability():
        print("✅ 后端服务正常运行 (http://localhost:8000)")
    else:
        print("❌ 后端服务不可用")
        print("   请确保已启动所有必要服务")
    
    # 检查测试脚本文件
    print("\n📄 脚本文件检查:")
    scripts = [
        "test_novel_generation_flow.py",
        "test_novel_websocket.py"
    ]
    
    for script in scripts:
        if os.path.exists(script):
            print(f"✅ {script}")
        else:
            print(f"❌ {script} (文件不存在)")
    
    print("=" * 50)


async def main():
    """主函数"""
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 (0-4): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1":
                # 运行基础版本测试
                if not check_dependencies():
                    continue
                if not check_service_availability():
                    print("⚠️ 后端服务不可用，测试可能失败")
                    continue_anyway = input("是否继续? (y/N): ").strip().lower()
                    if continue_anyway != 'y':
                        continue
                
                exit_code = await run_basic_test()
                if exit_code == 0:
                    print("\n🎉 测试成功完成!")
                else:
                    print("\n💥 测试失败")
                
            elif choice == "2":
                # 运行增强版本测试
                if not check_dependencies():
                    continue
                if not check_service_availability():
                    print("⚠️ 后端服务不可用，测试可能失败")
                    continue_anyway = input("是否继续? (y/N): ").strip().lower()
                    if continue_anyway != 'y':
                        continue
                
                exit_code = await run_enhanced_test()
                if exit_code == 0:
                    print("\n🎉 测试成功完成!")
                else:
                    print("\n💥 测试失败")
                
            elif choice == "3":
                # 环境检查
                check_environment()
                
            elif choice == "4":
                # 查看使用说明
                show_usage()
                
            else:
                print("❌ 无效选项，请重新选择")
            
            input("\n按回车键继续...")
            
        except KeyboardInterrupt:
            print("\n\n👋 再见!")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")
            input("按回车键继续...")


if __name__ == "__main__":
    print("🚀 启动AI小说生成器测试快速启动器...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见!")
        sys.exit(0) 