"""Convert Integer IDs to UUID

Revision ID: convert_integer_ids_to_uuid
Revises: add_llm_config_tables
Create Date: 2025-01-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = 'convert_integer_ids_to_uuid'
down_revision = None
branch_labels = None
depends_on = ('add_novel_fields', 'add_llm_config_tables')


def upgrade() -> None:
    """
    Convert all Integer IDs to UUID. 
    This is a destructive migration that will recreate all tables with UUID primary keys.
    All existing data will be lost.
    """
    
    # 1. Drop all tables in correct order (to handle foreign key constraints)
    op.drop_table('llm_usage_logs')
    op.drop_table('llm_configs')
    op.drop_table('generation_tasks')  
    op.drop_table('generation_states')
    op.drop_table('documents')
    op.drop_table('chapters')
    op.drop_table('novels')
    op.drop_table('prompt_templates')
    op.drop_table('users')
    
    # 2. Recreate users table with UUID
    op.create_table('users',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('wx_openid', sa.String(length=128), nullable=False),
        sa.Column('nickname', sa.String(length=255), nullable=True),
        sa.Column('avatar_url', sa.String(length=500), nullable=True),
        sa.Column('quota_used', sa.Integer(), nullable=True, default=0),
        sa.Column('quota_limit', sa.Integer(), nullable=True, default=10),
        sa.Column('is_vip', sa.Boolean(), nullable=True, default=False),
        sa.Column('settings', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_wx_openid'), 'users', ['wx_openid'], unique=True)
    
    # 3. Recreate novels table with UUID
    op.create_table('novels',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('genre', sa.String(length=50), nullable=True),
        sa.Column('target_length', sa.Integer(), nullable=True),
        sa.Column('style_settings', sa.JSON(), nullable=True),
        sa.Column('status', sa.Enum('draft', 'generating', 'completed', 'paused', name='novelstatus'), nullable=True),
        sa.Column('config', sa.JSON(), nullable=True),
        sa.Column('chapter_count', sa.Integer(), nullable=True, default=0),
        sa.Column('total_words', sa.Integer(), nullable=True, default=0),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_novels_id'), 'novels', ['id'], unique=False)
    op.create_index(op.f('ix_novels_user_id'), 'novels', ['user_id'], unique=False)
    
    # 4. Recreate chapters table with UUID
    op.create_table('chapters',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('novel_id', UUID(as_uuid=True), nullable=False),
        sa.Column('chapter_number', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=255), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('status', sa.Enum('draft', 'published', 'archived', name='chapterstatus'), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True, default=0),
        sa.Column('version', sa.Integer(), nullable=True, default=1),
        sa.Column('parent_id', UUID(as_uuid=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['novel_id'], ['novels.id']),
        sa.ForeignKeyConstraint(['parent_id'], ['chapters.id']),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('novel_id', 'chapter_number', 'is_active', name='uq_novel_chapter_active')
    )
    op.create_index(op.f('ix_chapters_id'), 'chapters', ['id'], unique=False)
    op.create_index(op.f('ix_chapters_novel_id'), 'chapters', ['novel_id'], unique=False)
    
    # 5. Recreate documents table with UUID
    op.create_table('documents',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('novel_id', UUID(as_uuid=True), nullable=False),
        sa.Column('doc_type', sa.Enum('architecture', 'blueprint', 'summary', 'character_state', 'world_building', 'plot_outline', 'immediate_summary', 'medium_summary', 'global_summary', 'character_summary', name='documenttype'), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=True, default=1),
        sa.Column('parent_id', UUID(as_uuid=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('summary_level', sa.Enum('immediate', 'medium', 'global', 'character', name='summarylevel'), nullable=True),
        sa.Column('chapter_range_start', sa.Integer(), nullable=True),
        sa.Column('chapter_range_end', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['novel_id'], ['novels.id']),
        sa.ForeignKeyConstraint(['parent_id'], ['documents.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_documents_id'), 'documents', ['id'], unique=False)
    op.create_index(op.f('ix_documents_novel_id'), 'documents', ['novel_id'], unique=False)
    
    # 6. Recreate generation_states table with UUID
    op.create_table('generation_states',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('novel_id', UUID(as_uuid=True), nullable=False),
        sa.Column('task_id', sa.String(), nullable=False),
        sa.Column('generation_type', sa.String(), nullable=False),
        sa.Column('current_step', sa.String(), nullable=False),
        sa.Column('completed_steps', sa.JSON(), nullable=True),
        sa.Column('step_progress', sa.Integer(), nullable=True, default=0),
        sa.Column('total_progress', sa.Integer(), nullable=True, default=0),
        sa.Column('is_completed', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_failed', sa.Boolean(), nullable=True, default=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_states_id'), 'generation_states', ['id'], unique=False)
    op.create_index(op.f('ix_generation_states_novel_id'), 'generation_states', ['novel_id'], unique=False)
    op.create_index(op.f('ix_generation_states_task_id'), 'generation_states', ['task_id'], unique=False)
    
    # 7. Recreate generation_tasks table with UUID (update foreign keys)
    op.create_table('generation_tasks',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('task_id', sa.String(length=255), nullable=False),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('novel_id', UUID(as_uuid=True), nullable=True),
        sa.Column('task_type', sa.Enum('generate_architecture', 'generate_blueprint', 'generate_chapter', 'regenerate_paragraph', 'consistency_check', 'finalize_chapter', name='tasktype'), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'CANCELLED', name='taskstatus'), nullable=True),
        sa.Column('progress', sa.Integer(), nullable=True, default=0),
        sa.Column('parameters', sa.Text(), nullable=True),
        sa.Column('result_doc_id', UUID(as_uuid=True), nullable=True),
        sa.Column('result_chapter_id', UUID(as_uuid=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['novel_id'], ['novels.id']),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generation_tasks_id'), 'generation_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_generation_tasks_task_id'), 'generation_tasks', ['task_id'], unique=True)
    op.create_index(op.f('ix_generation_tasks_user_id'), 'generation_tasks', ['user_id'], unique=False)
    op.create_index(op.f('ix_generation_tasks_novel_id'), 'generation_tasks', ['novel_id'], unique=False)
    
    # 8. Recreate prompt_templates table with UUID
    op.create_table('prompt_templates',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('template', sa.Text(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=True, default=1),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_prompt_templates_id'), 'prompt_templates', ['id'], unique=False)
    op.create_index(op.f('ix_prompt_templates_name'), 'prompt_templates', ['name'], unique=True)
    
    # 9. Recreate llm_configs table with UUID
    op.create_table('llm_configs',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=True, default=False),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('api_key', sa.String(length=500), nullable=True),
        sa.Column('base_url', sa.String(length=500), nullable=True),
        sa.Column('api_version', sa.String(length=50), nullable=True),
        sa.Column('model', sa.String(length=100), nullable=False),
        sa.Column('temperature', sa.Float(), nullable=True, default=0.7),
        sa.Column('max_tokens', sa.Integer(), nullable=True, default=2048),
        sa.Column('top_p', sa.Float(), nullable=True, default=1.0),
        sa.Column('frequency_penalty', sa.Float(), nullable=True, default=0.0),
        sa.Column('presence_penalty', sa.Float(), nullable=True, default=0.0),
        sa.Column('timeout', sa.Integer(), nullable=True, default=60),
        sa.Column('max_retries', sa.Integer(), nullable=True, default=3),
        sa.Column('retry_delay', sa.Float(), nullable=True, default=1.0),
        sa.Column('custom_headers', sa.JSON(), nullable=True),
        sa.Column('custom_params', sa.JSON(), nullable=True),
        sa.Column('total_requests', sa.Integer(), nullable=True, default=0),
        sa.Column('total_tokens', sa.Integer(), nullable=True, default=0),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_llm_configs_id'), 'llm_configs', ['id'], unique=False)
    op.create_index(op.f('ix_llm_configs_user_id'), 'llm_configs', ['user_id'], unique=False)
    
    # 10. Recreate llm_usage_logs table with UUID
    op.create_table('llm_usage_logs',
        sa.Column('id', UUID(as_uuid=True), nullable=False, primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), nullable=False),
        sa.Column('config_id', UUID(as_uuid=True), nullable=False),
        sa.Column('request_id', sa.String(length=100), nullable=True),
        sa.Column('endpoint', sa.String(length=200), nullable=True),
        sa.Column('method', sa.String(length=10), nullable=True),
        sa.Column('prompt_tokens', sa.Integer(), nullable=True, default=0),
        sa.Column('completion_tokens', sa.Integer(), nullable=True, default=0),
        sa.Column('total_tokens', sa.Integer(), nullable=True, default=0),
        sa.Column('response_time', sa.Float(), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=True, default=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('estimated_cost', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['config_id'], ['llm_configs.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_llm_usage_logs_id'), 'llm_usage_logs', ['id'], unique=False)
    op.create_index(op.f('ix_llm_usage_logs_user_id'), 'llm_usage_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_llm_usage_logs_config_id'), 'llm_usage_logs', ['config_id'], unique=False)


def downgrade() -> None:
    """
    Downgrade is not supported for this migration as it would require
    converting UUIDs back to integers, which is not feasible.
    """
    raise NotImplementedError("Downgrade from UUID to Integer is not supported. This would require manual data migration.")