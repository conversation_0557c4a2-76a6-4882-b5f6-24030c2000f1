#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文档模型 - 用于存储小说架构、蓝图、角色设定等核心文档
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, <PERSON><PERSON>an, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid

from app.core.database import Base


class DocumentType(enum.Enum):
    ARCHITECTURE = "architecture"  # 小说架构
    BLUEPRINT = "blueprint"  # 章节蓝图
    SUMMARY = "summary"
    CHARACTER_STATE = "character_state"  # 角色状态（人物关系状态）
    WORLD_BUILDING = "world_building"
    PLOT_OUTLINE = "plot_outline"  # 情节大纲
    # 新增多级摘要类型
    IMMEDIATE_SUMMARY = "immediate_summary"  # 最近3章摘要
    MEDIUM_SUMMARY = "medium_summary"        # 前10章摘要
    GLOBAL_SUMMARY = "global_summary"        # 全书摘要
    CHARACTER_SUMMARY = "character_summary"  # 角色发展摘要


class SummaryLevel(enum.Enum):
    IMMEDIATE = "immediate"  # 详细，最近3章
    MEDIUM = "medium"        # 中等，前10章
    GLOBAL = "global"        # 高度概括，全书
    CHARACTER = "character"  # 角色专项


class Document(Base):
    """核心文档表 - 统一管理架构、蓝图、角色设定等"""
    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    novel_id = Column(UUID(as_uuid=True), ForeignKey("novels.id"), nullable=False, index=True, comment="小说ID")
    
    # 文档基本信息
    doc_type = Column(SQLEnum(DocumentType, values_callable=lambda obj: [e.value for e in obj]), nullable=False, comment="文档类型")
    title = Column(String(200), nullable=False, comment="文档标题")
    content = Column(Text, nullable=True, comment="文档内容")
    
    # 版本控制
    version = Column(Integer, default=1, comment="版本号")
    parent_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True, comment="父版本ID")
    is_active = Column(Boolean, default=True, comment="是否为当前活跃版本")
    
    # 新增摘要管理字段
    summary_level = Column(SQLEnum(SummaryLevel, values_callable=lambda obj: [e.value for e in obj]), nullable=True, comment="摘要级别")
    chapter_range_start = Column(Integer, nullable=True, comment="摘要覆盖的起始章节")
    chapter_range_end = Column(Integer, nullable=True, comment="摘要覆盖的结束章节")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    novel = relationship("Novel", back_populates="documents")
    parent = relationship("Document", remote_side=[id], backref="children")

    def __repr__(self):
        return f"<Document(id={self.id}, type={self.doc_type}, version={self.version})>"