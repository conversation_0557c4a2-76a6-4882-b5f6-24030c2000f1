import os
import logging
from huggingface_hub import snapshot_download
from sentence_transformers import SentenceTransformer
import torch

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置镜像
# os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
# 设置模型缓存路径
model_cache_dir = os.path.join(os.path.dirname(__file__), "model_cache")
os.makedirs(model_cache_dir, exist_ok=True)

# 预先下载NLTK资源
import nltk
from nltk.book import *
nltk.download('punkt', quiet=True)
nltk.download('punkt_tab', quiet=True)

from nltk.tokenize import word_tokenize
from nltk.text import Text
input_str = "Today's weather is good, very windy and sunny, we have no classes in the afternoon,We have to play basketball tomorrow."
tokens = word_tokenize(input_str)
print(tokens)

# 模型名称
model_name = 'sentence-transformers/paraphrase-MiniLM-L6-v2'

# try:
#     # 先使用snapshot_download手动下载模型
#     logger.info(f"正在下载模型 {model_name}...")
#     model_path = snapshot_download(
#         repo_id=model_name,
#         cache_dir=model_cache_dir,
#         local_files_only=False,  # 如果设为True则只使用本地文件
#         resume_download=True,    # 断点续传
#         tqdm_class=None
#     )
#     logger.info(f"模型已下载到 {model_path}")
    
#     # 使用本地路径加载模型
#     model = SentenceTransformer(model_path)
    
#     # 测试模型
#     sentences = ["This is an example sentence", "Each sentence is converted"]
#     embeddings = model.encode(sentences)
#     print(embeddings)
#     logger.info("模型加载和测试成功")
    
# except Exception as e:
#     logger.error(f"下载或加载模型失败: {str(e)}")
    
#     # 备选方案：直接从模型名称加载，指定缓存目录
#     try:
#         logger.info("尝试直接加载模型...")
#         model = SentenceTransformer('paraphrase-MiniLM-L6-v2', cache_folder=model_cache_dir)
#         sentences = ["This is an example sentence", "Each sentence is converted"]
#         embeddings = model.encode(sentences)
#         print(embeddings)
#         logger.info("模型加载和测试成功")
#     except Exception as e2:
#         logger.error(f"备选方案也失败: {str(e2)}")
        
#         # 检查是否可以使用离线模式
#         if os.path.exists(os.path.join(model_cache_dir, "paraphrase-MiniLM-L6-v2")):
#             logger.info("尝试使用离线模式加载...")
#             try:
#                 model = SentenceTransformer('paraphrase-MiniLM-L6-v2', cache_folder=model_cache_dir, 
#                                            local_files_only=True)
#                 sentences = ["This is an example sentence", "Each sentence is converted"]
#                 embeddings = model.encode(sentences)
#                 print(embeddings)
#                 logger.info("离线模式加载成功")
#             except Exception as e3:
#                 logger.error(f"离线模式也失败: {str(e3)}")
