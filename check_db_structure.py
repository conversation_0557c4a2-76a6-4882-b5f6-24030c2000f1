#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库结构检查脚本
验证数据库中的实际表结构是否与SQLAlchemy模型定义一致
"""
import asyncio
import sys
import os
from typing import Dict, List, Any, Set
from sqlalchemy import inspect, MetaData, Table
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.engine import Inspector
from sqlalchemy.sql import text
import json
from datetime import datetime

# 添加app模块到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.models import *  # 导入所有模型


class DatabaseStructureChecker:
    """数据库结构检查器"""
    
    def __init__(self):
        self.engine = create_async_engine(settings.DATABASE_URL, echo=False)
        self.results = {
            "tables": {},
            "summary": {
                "total_tables": 0,
                "passed_tables": 0,
                "failed_tables": 0,
                "missing_tables": 0,
                "total_columns": 0,
                "passed_columns": 0,
                "failed_columns": 0,
                "missing_columns": 0,
                "total_constraints": 0,
                "passed_constraints": 0,
                "failed_constraints": 0,
                "total_indexes": 0,
                "passed_indexes": 0,
                "failed_indexes": 0
            },
            "check_time": datetime.now().isoformat()
        }
        
        # 预期的表结构定义
        self.expected_tables = {
            "users": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "wx_openid": {"type": "VARCHAR", "nullable": False, "unique": True, "length": 128},
                    "nickname": {"type": "VARCHAR", "nullable": True, "length": 255},
                    "avatar_url": {"type": "VARCHAR", "nullable": True, "length": 500},
                    "quota_used": {"type": "INTEGER", "nullable": True, "default": 0},
                    "quota_limit": {"type": "INTEGER", "nullable": True, "default": 10},
                    "is_vip": {"type": "BOOLEAN", "nullable": True, "default": False},
                    "settings": {"type": "JSON", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_users_id", "ix_users_wx_openid"],
                "constraints": ["users_pkey", "users_wx_openid_key"]
            },
            "novels": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "user_id": {"type": "INTEGER", "nullable": False, "foreign_key": "users.id"},
                    "title": {"type": "VARCHAR", "nullable": False, "length": 255},
                    "description": {"type": "TEXT", "nullable": True},
                    "genre": {"type": "VARCHAR", "nullable": True, "length": 50},
                    "target_length": {"type": "INTEGER", "nullable": True},
                    "style_settings": {"type": "JSON", "nullable": True},
                    "status": {"type": "ENUM", "nullable": True},
                    "config": {"type": "JSON", "nullable": True},
                    "chapter_count": {"type": "INTEGER", "nullable": True, "default": 0},
                    "total_words": {"type": "INTEGER", "nullable": True, "default": 0},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_novels_id", "ix_novels_user_id"],
                "foreign_keys": ["novels_user_id_fkey"]
            },
            "chapters": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "novel_id": {"type": "INTEGER", "nullable": False, "foreign_key": "novels.id"},
                    "chapter_number": {"type": "INTEGER", "nullable": False},
                    "title": {"type": "VARCHAR", "nullable": True, "length": 255},
                    "content": {"type": "TEXT", "nullable": True},
                    "summary": {"type": "TEXT", "nullable": True},
                    "status": {"type": "ENUM", "nullable": True},
                    "word_count": {"type": "INTEGER", "nullable": True, "default": 0},
                    "version": {"type": "INTEGER", "nullable": True, "default": 1},
                    "parent_id": {"type": "INTEGER", "nullable": True, "foreign_key": "chapters.id"},
                    "is_active": {"type": "BOOLEAN", "nullable": True, "default": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_chapters_id", "ix_chapters_novel_id"],
                "foreign_keys": ["chapters_novel_id_fkey", "chapters_parent_id_fkey"],
                "constraints": ["uq_novel_chapter_active"]
            },
            "documents": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "novel_id": {"type": "INTEGER", "nullable": False, "foreign_key": "novels.id"},
                    "doc_type": {"type": "ENUM", "nullable": False},
                    "title": {"type": "VARCHAR", "nullable": False, "length": 200},
                    "content": {"type": "TEXT", "nullable": True},
                    "version": {"type": "INTEGER", "nullable": True, "default": 1},
                    "parent_id": {"type": "INTEGER", "nullable": True, "foreign_key": "documents.id"},
                    "is_active": {"type": "BOOLEAN", "nullable": True, "default": True},
                    "summary_level": {"type": "ENUM", "nullable": True},
                    "chapter_range_start": {"type": "INTEGER", "nullable": True},
                    "chapter_range_end": {"type": "INTEGER", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_documents_id", "ix_documents_novel_id"],
                "foreign_keys": ["documents_novel_id_fkey", "documents_parent_id_fkey"]
            },
            "generation_tasks": {
                "columns": {
                    "id": {"type": "UUID", "nullable": False, "primary_key": True},
                    "task_id": {"type": "VARCHAR", "nullable": False, "unique": True, "length": 255},
                    "user_id": {"type": "INTEGER", "nullable": False, "foreign_key": "users.id"},
                    "novel_id": {"type": "INTEGER", "nullable": True, "foreign_key": "novels.id"},
                    "task_type": {"type": "ENUM", "nullable": False},
                    "status": {"type": "ENUM", "nullable": True},
                    "progress": {"type": "INTEGER", "nullable": True, "default": 0},
                    "parameters": {"type": "TEXT", "nullable": True},
                    "result_doc_id": {"type": "INTEGER", "nullable": True},
                    "result_chapter_id": {"type": "INTEGER", "nullable": True},
                    "error_message": {"type": "TEXT", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True},
                    "started_at": {"type": "TIMESTAMP", "nullable": True},
                    "completed_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_generation_tasks_id", "ix_generation_tasks_task_id", "ix_generation_tasks_user_id", "ix_generation_tasks_novel_id"],
                "foreign_keys": ["generation_tasks_user_id_fkey", "generation_tasks_novel_id_fkey"]
            },
            "generation_states": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "novel_id": {"type": "INTEGER", "nullable": False},
                    "task_id": {"type": "VARCHAR", "nullable": False},
                    "generation_type": {"type": "VARCHAR", "nullable": False},
                    "current_step": {"type": "VARCHAR", "nullable": False},
                    "completed_steps": {"type": "JSON", "nullable": True},
                    "step_progress": {"type": "INTEGER", "nullable": True, "default": 0},
                    "total_progress": {"type": "INTEGER", "nullable": True, "default": 0},
                    "is_completed": {"type": "BOOLEAN", "nullable": True, "default": False},
                    "is_failed": {"type": "BOOLEAN", "nullable": True, "default": False},
                    "error_message": {"type": "TEXT", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True},
                    "completed_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_generation_states_id", "ix_generation_states_novel_id", "ix_generation_states_task_id"]
            },
            "llm_configs": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "user_id": {"type": "INTEGER", "nullable": False, "foreign_key": "users.id"},
                    "name": {"type": "VARCHAR", "nullable": False, "length": 255},
                    "provider": {"type": "VARCHAR", "nullable": False, "length": 50},
                    "is_default": {"type": "BOOLEAN", "nullable": True, "default": False},
                    "is_active": {"type": "BOOLEAN", "nullable": True, "default": True},
                    "api_key": {"type": "VARCHAR", "nullable": True, "length": 500},
                    "base_url": {"type": "VARCHAR", "nullable": True, "length": 500},
                    "api_version": {"type": "VARCHAR", "nullable": True, "length": 50},
                    "model": {"type": "VARCHAR", "nullable": False, "length": 100},
                    "temperature": {"type": "FLOAT", "nullable": True, "default": 0.7},
                    "max_tokens": {"type": "INTEGER", "nullable": True, "default": 2048},
                    "top_p": {"type": "FLOAT", "nullable": True, "default": 1.0},
                    "frequency_penalty": {"type": "FLOAT", "nullable": True, "default": 0.0},
                    "presence_penalty": {"type": "FLOAT", "nullable": True, "default": 0.0},
                    "timeout": {"type": "INTEGER", "nullable": True, "default": 60},
                    "max_retries": {"type": "INTEGER", "nullable": True, "default": 3},
                    "retry_delay": {"type": "FLOAT", "nullable": True, "default": 1.0},
                    "custom_headers": {"type": "JSON", "nullable": True},
                    "custom_params": {"type": "JSON", "nullable": True},
                    "total_requests": {"type": "INTEGER", "nullable": True, "default": 0},
                    "total_tokens": {"type": "INTEGER", "nullable": True, "default": 0},
                    "last_used_at": {"type": "TIMESTAMP", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_llm_configs_id"],
                "foreign_keys": ["llm_configs_user_id_fkey"]
            },
            "llm_usage_logs": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "user_id": {"type": "INTEGER", "nullable": False, "foreign_key": "users.id"},
                    "config_id": {"type": "INTEGER", "nullable": False, "foreign_key": "llm_configs.id"},
                    "request_id": {"type": "VARCHAR", "nullable": True, "length": 100},
                    "endpoint": {"type": "VARCHAR", "nullable": True, "length": 200},
                    "method": {"type": "VARCHAR", "nullable": True, "length": 10},
                    "prompt_tokens": {"type": "INTEGER", "nullable": True, "default": 0},
                    "completion_tokens": {"type": "INTEGER", "nullable": True, "default": 0},
                    "total_tokens": {"type": "INTEGER", "nullable": True, "default": 0},
                    "response_time": {"type": "FLOAT", "nullable": True},
                    "success": {"type": "BOOLEAN", "nullable": True, "default": True},
                    "error_message": {"type": "TEXT", "nullable": True},
                    "estimated_cost": {"type": "FLOAT", "nullable": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_llm_usage_logs_id"],
                "foreign_keys": ["llm_usage_logs_user_id_fkey", "llm_usage_logs_config_id_fkey"]
            },
            "prompt_templates": {
                "columns": {
                    "id": {"type": "INTEGER", "nullable": False, "primary_key": True},
                    "name": {"type": "VARCHAR", "nullable": False, "unique": True, "length": 100},
                    "template": {"type": "TEXT", "nullable": False},
                    "description": {"type": "TEXT", "nullable": True},
                    "version": {"type": "INTEGER", "nullable": True, "default": 1},
                    "is_active": {"type": "BOOLEAN", "nullable": True, "default": True},
                    "created_at": {"type": "TIMESTAMP", "nullable": True},
                    "updated_at": {"type": "TIMESTAMP", "nullable": True}
                },
                "indexes": ["ix_prompt_templates_id", "ix_prompt_templates_name"],
                "constraints": ["prompt_templates_name_key"]
            }
        }

    async def check_database_structure(self) -> Dict[str, Any]:
        """检查数据库结构"""
        print("🔍 开始检查数据库结构...")
        print(f"📊 数据库连接: {settings.DATABASE_URL}")
        print("-" * 80)
        
        try:
            # 获取数据库表信息
            async with self.engine.connect() as conn:
                # 使用同步的方式获取表信息
                def get_inspector_info(sync_conn):
                    inspector = inspect(sync_conn)
                    return {
                        'tables': inspector.get_table_names(),
                        'table_info': {
                            table_name: {
                                'columns': inspector.get_columns(table_name),
                                'indexes': inspector.get_indexes(table_name),
                                'foreign_keys': inspector.get_foreign_keys(table_name),
                                'pk_constraint': inspector.get_pk_constraint(table_name),
                                'unique_constraints': inspector.get_unique_constraints(table_name)
                            } for table_name in inspector.get_table_names()
                        }
                    }
                
                db_info = await conn.run_sync(get_inspector_info)
                
                # 检查每个表
                for table_name, expected_structure in self.expected_tables.items():
                    self.results["summary"]["total_tables"] += 1
                    table_result = await self._check_table(table_name, expected_structure, db_info)
                    self.results["tables"][table_name] = table_result
                    
                    if table_result["status"] == "PASS":
                        self.results["summary"]["passed_tables"] += 1
                    elif table_result["status"] == "MISSING":
                        self.results["summary"]["missing_tables"] += 1
                    else:
                        self.results["summary"]["failed_tables"] += 1
                
                # 检查是否有额外的表
                extra_tables = set(db_info['tables']) - set(self.expected_tables.keys())
                if extra_tables:
                    for table_name in extra_tables:
                        self.results["tables"][table_name] = {
                            "status": "EXTRA",
                            "message": "数据库中存在但模型中未定义的表",
                            "columns": {},
                            "indexes": {},
                            "constraints": {}
                        }
                
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self.results["error"] = str(e)
            return self.results
        finally:
            await self.engine.dispose()
        
        # 生成总结
        self._generate_summary()
        return self.results

    async def _check_table(self, table_name: str, expected_structure: Dict, db_info: Dict) -> Dict[str, Any]:
        """检查单个表结构"""
        result = {
            "status": "PASS",
            "message": "",
            "columns": {},
            "indexes": {},
            "constraints": {},
            "foreign_keys": {}
        }
        
        # 检查表是否存在
        if table_name not in db_info['tables']:
            result["status"] = "MISSING"
            result["message"] = f"表 {table_name} 不存在"
            print(f"❌ 表 {table_name}: 不存在")
            return result
        
        print(f"📋 检查表: {table_name}")
        
        table_info = db_info['table_info'][table_name]
        
        # 检查列
        db_columns = {col['name']: col for col in table_info['columns']}
        column_issues = []
        
        for col_name, expected_col in expected_structure['columns'].items():
            self.results["summary"]["total_columns"] += 1
            
            if col_name not in db_columns:
                column_issues.append(f"缺少列: {col_name}")
                result["columns"][col_name] = {"status": "MISSING", "expected": expected_col}
                self.results["summary"]["missing_columns"] += 1
                print(f"  ❌ 列 {col_name}: 缺失")
            else:
                db_col = db_columns[col_name]
                col_result = self._check_column(col_name, expected_col, db_col)
                result["columns"][col_name] = col_result
                
                if col_result["status"] == "PASS":
                    self.results["summary"]["passed_columns"] += 1
                    print(f"  ✅ 列 {col_name}: 正确")
                else:
                    self.results["summary"]["failed_columns"] += 1
                    column_issues.extend(col_result["issues"])
                    print(f"  ⚠️ 列 {col_name}: {', '.join(col_result['issues'])}")
        
        # 检查是否有额外的列
        extra_columns = set(db_columns.keys()) - set(expected_structure['columns'].keys())
        for col_name in extra_columns:
            result["columns"][col_name] = {
                "status": "EXTRA", 
                "message": "数据库中存在但模型中未定义的列",
                "actual": db_columns[col_name]
            }
            print(f"  ℹ️ 列 {col_name}: 额外列 (数据库中存在但模型中未定义)")
        
        # 检查索引
        if 'indexes' in expected_structure:
            db_indexes = {idx['name'] for idx in table_info['indexes']}
            for expected_idx in expected_structure['indexes']:
                self.results["summary"]["total_indexes"] += 1
                if expected_idx in db_indexes:
                    result["indexes"][expected_idx] = {"status": "PASS"}
                    self.results["summary"]["passed_indexes"] += 1
                    print(f"  ✅ 索引 {expected_idx}: 存在")
                else:
                    result["indexes"][expected_idx] = {"status": "MISSING"}
                    self.results["summary"]["failed_indexes"] += 1
                    print(f"  ❌ 索引 {expected_idx}: 缺失")
        
        # 检查外键
        if 'foreign_keys' in expected_structure:
            db_fks = {fk['name'] for fk in table_info['foreign_keys'] if fk['name']}
            for expected_fk in expected_structure['foreign_keys']:
                if expected_fk in db_fks:
                    result["foreign_keys"][expected_fk] = {"status": "PASS"}
                    print(f"  ✅ 外键 {expected_fk}: 存在")
                else:
                    result["foreign_keys"][expected_fk] = {"status": "MISSING"}
                    print(f"  ❌ 外键 {expected_fk}: 缺失")
        
        # 更新表状态
        if column_issues:
            result["status"] = "FAIL"
            result["message"] = "; ".join(column_issues)
        
        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"{status_icon} 表 {table_name}: {result['status']}")
        print()
        
        return result

    def _check_column(self, col_name: str, expected: Dict, actual: Dict) -> Dict[str, Any]:
        """检查单个列"""
        result = {"status": "PASS", "issues": [], "expected": expected, "actual": actual}
        
        # 检查可空性
        if expected.get("nullable") is not None:
            if expected["nullable"] != actual["nullable"]:
                result["issues"].append(f"可空性不匹配 (期望: {expected['nullable']}, 实际: {actual['nullable']})")
        
        # 检查类型 (简化检查，因为SQLAlchemy类型和PostgreSQL类型可能不完全匹配)
        actual_type = str(actual["type"]).upper()
        expected_type = expected["type"].upper()
        
        # 类型映射
        type_mappings = {
            "INTEGER": ["INTEGER", "SERIAL", "BIGINT"],
            "VARCHAR": ["VARCHAR", "CHARACTER VARYING", "TEXT"],
            "TEXT": ["TEXT", "VARCHAR"],
            "BOOLEAN": ["BOOLEAN", "BOOL"],
            "TIMESTAMP": ["TIMESTAMP", "DATETIME"],
            "JSON": ["JSON", "JSONB"],
            "FLOAT": ["REAL", "DOUBLE PRECISION", "FLOAT", "NUMERIC"],
            "UUID": ["UUID"],
            "ENUM": ["ENUM", "VARCHAR"]  # ENUM可能被实现为VARCHAR
        }
        
        if expected_type in type_mappings:
            if not any(mapping in actual_type for mapping in type_mappings[expected_type]):
                result["issues"].append(f"类型不匹配 (期望: {expected_type}, 实际: {actual_type})")
        
        if result["issues"]:
            result["status"] = "FAIL"
        
        return result

    def _generate_summary(self):
        """生成检查总结"""
        print("=" * 80)
        print("📊 数据库结构检查总结")
        print("=" * 80)
        
        summary = self.results["summary"]
        
        # 表统计
        print(f"📋 表统计:")
        print(f"  总计: {summary['total_tables']}")
        print(f"  ✅ 通过: {summary['passed_tables']}")
        print(f"  ❌ 失败: {summary['failed_tables']}")
        print(f"  ❓ 缺失: {summary['missing_tables']}")
        print()
        
        # 列统计
        print(f"📋 列统计:")
        print(f"  总计: {summary['total_columns']}")
        print(f"  ✅ 通过: {summary['passed_columns']}")
        print(f"  ❌ 失败: {summary['failed_columns']}")
        print(f"  ❓ 缺失: {summary['missing_columns']}")
        print()
        
        # 索引统计
        if summary['total_indexes'] > 0:
            print(f"📋 索引统计:")
            print(f"  总计: {summary['total_indexes']}")
            print(f"  ✅ 通过: {summary['passed_indexes']}")
            print(f"  ❌ 失败: {summary['failed_indexes']}")
            print()
        
        # 整体状态
        if (summary['failed_tables'] == 0 and summary['missing_tables'] == 0 and 
            summary['failed_columns'] == 0 and summary['missing_columns'] == 0):
            print("🎉 数据库结构检查通过！所有表和字段都正确。")
        else:
            print("⚠️ 数据库结构检查发现问题，请查看详细报告。")
        
        print("=" * 80)

    def save_report(self, filename: str = "db_structure_check_report.json"):
        """保存检查报告到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"📄 详细报告已保存到: {filename}")


async def main():
    """主函数"""
    print("🔍 AI小说生成器 - 数据库结构检查工具")
    print("=" * 80)
    
    checker = DatabaseStructureChecker()
    
    try:
        # 执行检查
        results = await checker.check_database_structure()
        
        # 保存报告
        checker.save_report()
        
        # 返回结果
        return results
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 运行检查
    results = asyncio.run(main())
    
    # 退出代码
    if results and results.get("summary", {}).get("failed_tables", 0) == 0:
        sys.exit(0)  # 成功
    else:
        sys.exit(1)  # 失败 