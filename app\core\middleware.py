#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一中间件模块
"""
import time
import uuid
import json
import traceback
from datetime import datetime
from typing import Callable, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import logging

from app.core.logging_config import log_api_call, log_error


logger = logging.getLogger(__name__)


class RequestContextMiddleware(BaseHTTPMiddleware):
    """请求上下文中间件 - 添加请求ID和用户追踪"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]
        request.state.request_id = request_id
        
        # 从认证信息中提取用户ID（如果存在）
        user_id = "anonymous"
        if hasattr(request.state, "current_user") and request.state.current_user:
            user_id = str(request.state.current_user.id)
        request.state.user_id = user_id
        
        # 记录请求开始时间
        start_time = time.time()
        request.state.start_time = start_time
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.3f}"
        
        # 记录API调用日志
        log_api_call(
            logger=logger,
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration=process_time,
            user_id=user_id,
            request_id=request_id
        )
        
        return response


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """响应格式化中间件 - 统一响应格式"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 检查是否需要格式化响应
        # 跳过静态文件、文档页面等
        if (
            request.url.path.startswith("/docs") or
            request.url.path.startswith("/redoc") or
            request.url.path.startswith("/openapi.json") or
            request.url.path.startswith("/static") or
            request.url.path.startswith("/ws")
        ):
            return response
        
        # 只处理成功的JSON响应
        if response.status_code < 400:
            try:
                # 检查是否是JSONResponse或包含JSON内容的响应
                if hasattr(response, "body") and response.body:
                    # 直接读取body内容
                    body_content = response.body.decode() if isinstance(response.body, bytes) else str(response.body)
                    
                    try:
                        content = json.loads(body_content)
                    except json.JSONDecodeError:
                        # 如果不是JSON内容，不处理
                        return response
                    
                    # 如果还没有统一格式，则添加
                    if not isinstance(content, dict) or "success" not in content:
                        formatted_content = {
                            "success": True,
                            "data": content,
                            "message": "操作成功",
                            "timestamp": datetime.utcnow().isoformat() + "Z"
                        }
                        
                        # 添加请求ID（如果存在）
                        if hasattr(request.state, "request_id"):
                            formatted_content["request_id"] = request.state.request_id
                        
                        # 创建新的响应
                        return JSONResponse(
                            content=formatted_content,
                            status_code=response.status_code,
                            headers=dict(response.headers)
                        )
                    else:
                        # 添加时间戳（如果没有）
                        if "timestamp" not in content or content["timestamp"] is None:
                            content["timestamp"] = datetime.utcnow().isoformat() + "Z"
                        
                        # 添加请求ID（如果存在）
                        if hasattr(request.state, "request_id") and "request_id" not in content:
                            content["request_id"] = request.state.request_id
                        
                        # 更新响应
                        return JSONResponse(
                            content=content,
                            status_code=response.status_code,
                            headers=dict(response.headers)
                        )
                        
                elif isinstance(response, JSONResponse):
                    # 处理JSONResponse类型的响应
                    content = response.body
                    if isinstance(content, bytes):
                        content = json.loads(content.decode())
                    
                    # 如果还没有统一格式，则添加
                    if not isinstance(content, dict) or "success" not in content:
                        formatted_content = {
                            "success": True,
                            "data": content,
                            "message": "操作成功",
                            "timestamp": datetime.utcnow().isoformat() + "Z"
                        }
                        
                        # 添加请求ID（如果存在）
                        if hasattr(request.state, "request_id"):
                            formatted_content["request_id"] = request.state.request_id
                        
                        return JSONResponse(
                            content=formatted_content,
                            status_code=response.status_code,
                            headers=dict(response.headers)
                        )
                        
            except Exception as e:
                # 如果无法解析响应内容，则记录错误但不修改响应
                logger.warning(f"无法格式化响应内容: {e}", extra={
                    'request_id': getattr(request.state, 'request_id', 'unknown'),
                    'path': request.url.path,
                    'error': str(e)
                })
        
        return response


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """错误处理中间件 - 捕获未处理的异常"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except Exception as exc:
            # 记录未捕获的异常
            request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])
            user_id = getattr(request.state, "user_id", "anonymous")
            
            log_error(
                logger=logger,
                error=exc,
                context={
                    "method": request.method,
                    "path": request.url.path,
                    "query_params": str(request.query_params),
                    "client": request.client.host if request.client else "unknown"
                },
                user_id=user_id,
                request_id=request_id
            )
            
            # 返回统一的错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "success": False,
                    "error": {
                        "code": "INTERNAL_SERVER_ERROR",
                        "message": "服务器内部错误",
                        "details": {}
                    },
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "request_id": request_id
                }
            )


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件 - 添加安全头"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # 移除服务器信息
        if "server" in response.headers:
            del response.headers["server"]
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件 - 简单的内存限流实现"""
    
    def __init__(self, app: ASGIApp, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # {client_ip: [(timestamp, count), ...]}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()
        
        # 清理过期记录
        if client_ip in self.requests:
            self.requests[client_ip] = [
                (timestamp, count) for timestamp, count in self.requests[client_ip]
                if current_time - timestamp < self.window_seconds
            ]
        
        # 检查限流
        if client_ip in self.requests:
            total_requests = sum(count for _, count in self.requests[client_ip])
            if total_requests >= self.max_requests:
                logger.warning(
                    f"Rate limit exceeded for IP: {client_ip}",
                    extra={
                        'request_id': getattr(request.state, "request_id", str(uuid.uuid4())[:8]),
                        'user_id': getattr(request.state, "user_id", "anonymous"),
                        'operation': 'rate_limit'
                    }
                )
                return JSONResponse(
                    status_code=429,
                    content={
                        "success": False,
                        "error": {
                            "code": "RATE_LIMIT_EXCEEDED",
                            "message": "请求频率超限，请稍后再试",
                            "details": {
                                "retry_after": self.window_seconds
                            }
                        },
                        "timestamp": datetime.utcnow().isoformat() + "Z"
                    },
                    headers={"Retry-After": str(self.window_seconds)}
                )
        
        # 记录请求
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        self.requests[client_ip].append((current_time, 1))
        
        return await call_next(request)


class CORSMiddleware(BaseHTTPMiddleware):
    """CORS中间件 - 处理跨域请求"""
    
    def __init__(
        self,
        app: ASGIApp,
        allow_origins: list = None,
        allow_methods: list = None,
        allow_headers: list = None,
        allow_credentials: bool = True
    ):
        super().__init__(app)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.allow_headers = allow_headers or [
            "Accept", "Accept-Language", "Content-Language", "Content-Type",
            "Authorization", "X-Request-ID", "X-Requested-With"
        ]
        self.allow_credentials = allow_credentials
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        origin = request.headers.get("origin")
        
        # 处理预检请求
        if request.method == "OPTIONS":
            response = Response()
            response.status_code = 200
        else:
            response = await call_next(request)
        
        # 添加CORS头
        if origin and (self.allow_origins == ["*"] or origin in self.allow_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
        elif self.allow_origins == ["*"]:
            response.headers["Access-Control-Allow-Origin"] = "*"
        
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        
        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response 