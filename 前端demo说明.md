# AI小说生成系统 - 前端功能需求规格说明

## 核心设计哲学

1.  **作家为中心 (Writer-Centric)**: 工具应服务于创作者，而非取代。所有功能旨在增强、加速和激发作家的创造力。
2.  **人机协同 (Human-AI Collaboration)**: 提供从高级规划到微观遣词造句的全流程AI辅助，但始终保持用户拥有最终决定权。
3.  **沉浸式环境 (Immersive Environment)**: 通过无干扰的写作界面和触手可及的上下文信息，让作家能专注于故事世界。
4.  **灵感催化剂 (Inspiration Catalyst)**: 不仅执行指令，更要能主动提供创意火花、故事走向和"What-if"情景。
5.  **项目化管理 (Project-Based Management)**: 将每一本小说视为一个独立项目，系统地管理其所有资产，包括手稿、角色、设定和研究资料。

---

## 1. 用户认证与平台引导

### 1.1 平台门户页 (Landing Page)

*   **目标用户**: 未注册的访客。
*   **页面内容**:
    *   **核心价值主张**: 清晰的标题，例如"AI协同，释放你的创作潜能"。
    *   **功能亮点展示**: 通过动态图或短视频，展示核心功能，如"一键生成世界观"、"智能情节大纲"、"角色动态追踪"、"上下文一致性检查"。
    *   **用户评价**: 展示虚拟的作家好评或合作案例。
    *   **服务方案**: 清晰列出不同订阅套餐（如：免费版、专业版、团队版），并对比其功能差异（如：每月生成字数、可用AI模型、项目数量限制、导出格式等）。
    *   **行为召唤(CTA)**: 显眼的"免费注册"或"开始我的创作之旅"按钮。

### 1.2 用户注册

*   **页面目标**: 引导新用户快速、安全地创建账户。
*   **功能组件**:
    *   **注册方式**:
        *   邮箱/密码注册（密码需满足强度要求，并有实时强度提示）。
        *   集成第三方社交媒体平台一键注册 (如微信、Google、Apple ID)。
    *   **表单字段**: 用户昵称（作为作家名）、邮箱、密码、密码确认。
    *   **必需条款**: 需用户主动勾选同意"用户服务协议"和"隐私政策"的链接。
    *   **界面设计**: 界面专业、简洁，背景可采用与创作相关的抽象艺术设计。

### 1.3 用户登录

*   **页面目标**: 已注册用户访问其账户。
*   **功能组件**:客观
    *   邮箱/密码登录。
    *   第三方社交媒体登录选项。
    *   **"保持登录状态"** 复选框。
    *   **"忘记密码"** 链接，用于启动安全的密码重置流程。

---

## 2. 创作仪表盘

用户登录后进入的首页，作为所有创作活动的指挥中心。

### 2.1 整体布局

*   **左侧主导航栏**:
    *   仪表盘 (默认首页)
    *   我的项目
    *   灵感库
    *   账户中心
    *   帮助与支持
*   **主内容区**: 展示各类信息卡片与快捷操作。

### 2.2 功能组件说明

*   **欢迎横幅**: "欢迎回来，[作家名]！今天想创作怎样的故事？"
*   **"创建新项目"按钮**: 界面中最醒目的按钮，引导用户启动"新小说创建向导"。
*   **项目列表**:
    *   以封面卡片形式展示用户的所有小说项目。
    *   **卡片信息**:
        *   小说封面 (允许用户上传自定义图片或由AI根据主题生成)。
        *   小说标题。
        *   类型/主题标签。
        *   **进度概览**: "当前字数 / 目标字数" 和 "已完成章节 / 总章节数" 的可视化进度条。
        *   最后编辑时间。
    *   **卡片操作**: 鼠标悬停时显示操作菜单，包含"继续写作"、"项目设置"、"归档"（将项目移出主列表，但保留数据）、"删除"（需二次确认）。
*   **最近活动流**: 以列表形式显示最近5-10条操作记录（如"编辑了章节：黎明之战"、"创建了新角色：艾拉"），并提供快速跳转链接。
*   **AI灵感模块 (AI Inspiration Module)**:
    *   一个动态卡片区域，每次刷新或每日更新，由AI生成：
        *   **写作练习**: "请用不超过300字描写：一个第一次看到雪的沙漠居民。"
        *   **"What-if"情景分析**: "分析：如果您的项目《赛博之城》中的反派突然失忆，故事将如何发展？"
*   **创作数据统计**: 可视化图表，展示本周/本月的写作总字数、日均创作时长、AI辅助内容占比等数据分析。

---

## 3. 新小说创建向导

通过结构化的步骤引导用户完成项目的初始配置。

### 3.1 核心概念设定

*   **用户输入**: 小说标题、一句话核心梗概 (Logline)、选择多个预设的类型/主题标签 (如: `科幻`, `悬疑`, `赛博朋克`)。
*   **AI交互**: 用户输入后，AI会基于梗概和标签，立即生成3-5个不同角度的"故事简介"备选项。用户可选择其中一个作为基础，或将其融合编辑，形成最终版本。

### 3.2 AI模型与生成风格配置

*   **AI模型选择**: 提供具备不同特性和成本的AI语言模型选项，并附有清晰的功能说明。
    *   **创意大师模型 (例如 GPT-4 Turbo)**: 适用于复杂情节构思和深度文学性描写，生成质量高，响应时间较长。
    *   **闪电草稿模型 (例如 Fine-tuned Llama-3)**: 适用于快速生成大量章节草稿，响应速度快，成本较低。
    *   **精细描述模型 (例如 Specialized Model)**: 专注于环境、氛围、感官细节的渲染。
*   **写作风格定制**:
    *   提供多个维度的风格滑块供用户调节，如：`叙事节奏：紧凑 vs 舒缓`，`文风：简洁 vs 华丽`，`视角：客观 vs 主观`，`基调：幽默 vs 严肃`。
    *   **"风格参考"输入框**: 用户可粘贴一段喜欢的文字，AI将分析其风格作为后续生成的参考。
    *   **"约束与规避"输入框**: 用户可以明确输入不希望AI使用的词语、句式或情节套路 (例如："禁止使用'微微一笑'"、"避免都合主义情节")。

### 3.3 角色与世界观构建

*   **AI批量生成**: 基于已有信息，AI自动生成：
    *   **核心角色建议**: 3-5名主角和关键配角，以卡片形式展示，包含姓名、身份、核心动机和人物小传。
    *   **世界观框架**: 关于故事世界的物理法则、社会结构、技术水平或魔法体系的结构化摘要。
*   **用户交互与调整**: 用户可以点击任何卡片进行深度编辑，也可请求AI"再生成一批"，或手动"添加新角色/新设定"。

### 3.4 情节框架规划

*   **AI结构推荐**: AI根据小说类型推荐一个或多个经典的叙事结构（如：三幕剧、英雄之旅、多线叙事），并自动填充关键情节节点。
*   **可视化编辑**:
    *   以可拖拽的看板（Trello-like）或思维导图形式展示情节框架。
    *   用户可以自由编辑、增加、删除每个情节节点的内容，或在不同叙事结构模板间切换。
    *   AI提供"节点扩展"功能，可将一个高级情节节点（如"主角遭遇重大挫折"）自动扩展为包含多个步骤的详细章节大纲。

### 3.5 项目确认与启动

*   **总览页面**: 集中展示用户在前序步骤中完成的所有核心设定，供最终确认。
*   **启动项目**: 点击"开启创作之旅"按钮，系统在后端完成项目初始化，并导航至该项目的"创作工作台"。

---

## 4. 创作工作台 (Creation Workspace)

集写作、编辑、管理于一体的核心界面，采用专业软件常见的三栏式布局。

### 4.1 资源导航面板 (左栏)

*   **项目文件树**:
    *   `手稿`: 树状结构，清晰展示卷、章、节的层级关系。可拖拽调整章节顺序。
    *   `角色`: 所有角色资料库的入口，以列表形式展示。
    *   `世界观`: 设定集，用户可创建和管理词条（如地点、组织、物品、种族等）。
    *   `笔记板`: 自由剪贴板，用于存放灵感片段、研究资料、图片、外部链接等。
    *   `回收站`: 存放已删除的章节或条目，支持恢复。

### 4.2 主编辑区 (中栏)

*   **编辑器核心功能**:
    *   提供专业的**所见即所得 (WYSIWYG)** 富文本编辑器。
    *   **无干扰写作模式**: 一键隐藏左右两栏，让用户专注于文本创作。
    *   **实时保存**: 所有修改均自动保存，并提供清晰的状态指示（如"已保存"、"正在保存..."）。
    *   **版本历史**: 可随时查看并恢复当前章节的任何历史版本。
*   **AI协同写作功能**:
    *   **指令式生成 (Slash Command)**: 在新的一行输入"/"唤出AI指令菜单，提供如 `/写一段环境描写`，`/生成一段A和B的对话` 等快捷指令。
    *   **上下文感知编辑 (Floating Toolbar)**: 选中一段文本后，浮现AI工具栏，提供"润色"、"扩写"、"缩写"、"改变语气（例如：更紧张）"、"翻译"、"检查一致性"等智能操作。

### 4.3 智能辅助面板 (右栏)

此面板动态展示与当前写作内容相关的上下文信息与AI工具。

*   **动态摘要模块**: 实时更新的全书或当前卷的摘要，帮助作者把握宏观叙事。
*   **章节目标模块**: 清晰显示当前章节在情节大纲中的核心任务与目标。
*   **场景元素关联**:
    *   **出场角色**: 自动识别并列出本章已出现的角色，点击可快速弹窗查看其核心设定，避免OOC（Out of Character）。
    *   **关键设定引用**: 自动高亮并链接本章提及的关键世界观词条。
*   **一致性检查器**:
    *   在后台实时运行，当检测到与前文的潜在逻辑矛盾时（如"角色A的武器与设定不符"、"时间线冲突"），在此处弹出非干扰性的警告卡片。点击卡片，主编辑区中的相关文本会高亮显示，并提供修正建议。
*   **全局生成指令**:
    *   一个固定的文本框，用户可以输入对AI后续生成的全局性要求（如"注意保持悬念"、"多使用比喻"），该指令将在本次写作会话中持续生效。

---

## 5. 账户中心

管理用户个人信息、订阅和应用设置的区域。

### 5.1 个人资料

*   支持修改用户昵称（作家名）、头像、联系邮箱。

### 5.2 订阅与计费

*   清晰展示用户当前的订阅套餐、本月用量（如AI生成字数）及剩余额度。
*   提供详细的账单历史记录。
*   支持套餐的升级、降级或取消。

### 5.3 API密钥管理

*   为高级用户或开发者提供配置自己的LLM (大语言模型) API Key的接口，允许其使用自己的模型额度或私有模型。

### 5.4 应用偏好设置

*   **界面主题**: 提供明亮、黑暗、护眼等多种界面主题选项。
*   **通知设置**: 管理邮件和应用内通知的接收偏好。
*   **默认AI配置**: 设置默认使用的AI模型和写作风格，避免每次创建新项目时重复配置。