#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
认证服务
"""
import httpx
import logging
from jose import jwt
from datetime import datetime, timedelta
from typing import Optional
from uuid import UUID
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.database import get_async_db
from app.models.user import User
from app.schemas.auth import TokenResponse, UserResponse

logger = logging.getLogger(__name__)

security = HTTPBearer()


class AuthService:
    """认证服务类"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def wechat_login(self, code: str, nickname: Optional[str] = None, avatar_url: Optional[str] = None) -> TokenResponse:
        """微信小程序登录"""
        
        # 1. 通过code获取openid
        openid = await self._get_openid_from_wechat(code)
        
        # 2. 查找或创建用户
        user = await self._get_or_create_user(openid, nickname, avatar_url)
        
        # 3. 生成JWT token
        access_token = await self.create_access_token(data={"sub": str(user.id)})
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            user=UserResponse.model_validate(user)
        )
    
    async def _get_openid_from_wechat(self, code: str) -> str:
        """从微信服务器获取openid"""
        url = "https://api.weixin.qq.com/sns/jscode2session"
        params = {
            "appid": settings.WECHAT_APP_ID,
            "secret": settings.WECHAT_APP_SECRET,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if "errcode" in data and data["errcode"] != 0:
                    logger.error(f"WeChat API error: {data}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"微信登录失败: {data.get('errmsg', '未知错误')}"
                    )
                
                return data["openid"]
                
            except httpx.RequestError as e:
                logger.error(f"Request to WeChat API failed: {e}")
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="微信服务暂时不可用"
                )
    
    async def _get_or_create_user(self, openid: str, nickname: Optional[str], avatar_url: Optional[str]) -> User:
        """获取或创建用户"""
        # 查找现有用户
        stmt = select(User).where(User.wx_openid == openid)
        result = await self.session.execute(stmt)
        user = result.scalar_one_or_none()
        
        if user:
            # 更新用户信息（如果有新信息）
            updated = False
            if nickname and user.nickname != nickname:
                user.nickname = nickname
                updated = True
            if avatar_url and user.avatar_url != avatar_url:
                user.avatar_url = avatar_url
                updated = True
            
            if updated:
                await self.session.commit()
                await self.session.refresh(user)
        else:
            # 创建新用户
            user = User(
                wx_openid=openid,
                nickname=nickname,
                avatar_url=avatar_url
            )
            self.session.add(user)
            await self.session.commit()
            await self.session.refresh(user)
        
        return user
    
    async def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")
        return encoded_jwt
    
    @staticmethod
    async def verify_token(token: str) -> Optional[dict]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            return payload
        except jwt.JWTError:
            return None
    
    @staticmethod
    async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        session: AsyncSession = Depends(get_async_db)
    ) -> User:
        """获取当前用户依赖注入函数"""
        
        # 验证token
        payload = await AuthService.verify_token(credentials.credentials)
        if payload is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 从数据库获取用户
        try:
            user_uuid = UUID(user_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的用户ID格式",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        stmt = select(User).where(User.id == user_uuid)
        result = await session.execute(stmt)
        user = result.scalar_one_or_none()
        
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return user