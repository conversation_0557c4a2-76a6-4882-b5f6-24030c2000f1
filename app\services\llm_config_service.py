#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LLM配置管理服务
"""
import time
import logging
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload
from cryptography.fernet import Fernet
import base64
import os

from app.models.llm_config import LLMConfig, LLMUsageLog
from app.models.user import User
from app.schemas.llm_config import (
    LLMConfigCreate, LLMConfigUpdate, LLMConfigResponse,
    LLMUsageStats, LLMTestRequest, LLMTestResponse
)
from app.core.config import settings

logger = logging.getLogger(__name__)


class LLMConfigService:
    """LLM配置服务"""
    
    def __init__(self):
        # 初始化加密密钥
        self._encryption_key = self._get_or_create_encryption_key()
        self._cipher_suite = Fernet(self._encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = "llm_encryption.key"
        
        if os.path.exists(key_file):
            with open(key_file, "rb") as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, "wb") as f:
                f.write(key)
            return key
    
    def _encrypt_api_key(self, api_key: str) -> str:
        """加密API密钥"""
        if not api_key:
            return ""
        return self._cipher_suite.encrypt(api_key.encode()).decode()
    
    def _decrypt_api_key(self, encrypted_key: str) -> str:
        """解密API密钥"""
        if not encrypted_key:
            return ""
        try:
            return self._cipher_suite.decrypt(encrypted_key.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to decrypt API key: {e}")
            return ""
    
    async def create_config(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_data: LLMConfigCreate
    ) -> LLMConfig:
        """创建LLM配置"""
        
        # 如果设置为默认配置，先取消其他默认配置
        if config_data.is_default:
            await self._unset_other_defaults(db, user_id)
        
        # 加密API密钥
        encrypted_api_key = None
        if config_data.api_key:
            encrypted_api_key = self._encrypt_api_key(config_data.api_key)
        
        # 创建配置记录
        config = LLMConfig(
            user_id=user_id,
            name=config_data.name,
            provider=config_data.provider,
            is_default=config_data.is_default,
            is_active=config_data.is_active,
            api_key=encrypted_api_key,
            base_url=config_data.base_url,
            api_version=config_data.api_version,
            model=config_data.model,
            temperature=config_data.temperature,
            max_tokens=config_data.max_tokens,
            top_p=config_data.top_p,
            frequency_penalty=config_data.frequency_penalty,
            presence_penalty=config_data.presence_penalty,
            timeout=config_data.timeout,
            max_retries=config_data.max_retries,
            retry_delay=config_data.retry_delay,
            custom_headers=config_data.custom_headers,
            custom_params=config_data.custom_params
        )
        
        db.add(config)
        await db.commit()
        await db.refresh(config)
        
        logger.info(f"Created LLM config {config.id} for user {user_id}")
        return config
    
    async def get_config(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: UUID
    ) -> Optional[LLMConfig]:
        """获取LLM配置"""
        stmt = select(LLMConfig).where(
            and_(
                LLMConfig.id == config_id,
                LLMConfig.user_id == user_id
            )
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_configs(
        self,
        db: AsyncSession,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = False
    ) -> tuple[List[LLMConfig], int]:
        """获取用户的LLM配置列表"""
        
        # 构建查询条件
        conditions = [LLMConfig.user_id == user_id]
        if active_only:
            conditions.append(LLMConfig.is_active == True)
        
        # 查询总数
        count_stmt = select(func.count(LLMConfig.id)).where(and_(*conditions))
        total_result = await db.execute(count_stmt)
        total = total_result.scalar_one()
        
        # 查询配置列表
        stmt = (
            select(LLMConfig)
            .where(and_(*conditions))
            .order_by(desc(LLMConfig.is_default), desc(LLMConfig.created_at))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        return list(configs), total
    
    async def update_config(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: UUID,
        config_data: LLMConfigUpdate
    ) -> Optional[LLMConfig]:
        """更新LLM配置"""
        
        # 获取现有配置
        config = await self.get_config(db, user_id, config_id)
        if not config:
            return None
        
        # 如果设置为默认配置，先取消其他默认配置
        if config_data.is_default and not config.is_default:
            await self._unset_other_defaults(db, user_id)
        
        # 更新字段
        update_data = config_data.model_dump(exclude_unset=True)
        
        # 处理API密钥加密
        if "api_key" in update_data and update_data["api_key"]:
            update_data["api_key"] = self._encrypt_api_key(update_data["api_key"])
        
        for field, value in update_data.items():
            setattr(config, field, value)
        
        await db.commit()
        await db.refresh(config)
        
        logger.info(f"Updated LLM config {config_id} for user {user_id}")
        return config
    
    async def delete_config(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: UUID
    ) -> bool:
        """删除LLM配置"""
        
        config = await self.get_config(db, user_id, config_id)
        if not config:
            return False
        
        await db.delete(config)
        await db.commit()
        
        logger.info(f"Deleted LLM config {config_id} for user {user_id}")
        return True
    
    async def get_default_config(
        self,
        db: AsyncSession,
        user_id: UUID
    ) -> Optional[LLMConfig]:
        """获取用户的默认LLM配置"""
        
        stmt = select(LLMConfig).where(
            and_(
                LLMConfig.user_id == user_id,
                LLMConfig.is_default == True,
                LLMConfig.is_active == True
            )
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_config_for_generation(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: Optional[UUID] = None
    ) -> Optional[Dict[str, Any]]:
        """获取用于生成的配置（包含解密的API密钥）"""
        
        if config_id:
            config = await self.get_config(db, user_id, config_id)
        else:
            config = await self.get_default_config(db, user_id)
        
        if not config or not config.is_active:
            return None
        
        # 解密API密钥
        decrypted_api_key = None
        if config.api_key:
            decrypted_api_key = self._decrypt_api_key(config.api_key)
        
        return {
            "id": config.id,
            "provider": config.provider,
            "api_key": decrypted_api_key,
            "base_url": config.base_url,
            "api_version": config.api_version,
            "model": config.model,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "top_p": config.top_p,
            "frequency_penalty": config.frequency_penalty,
            "presence_penalty": config.presence_penalty,
            "timeout": config.timeout,
            "max_retries": config.max_retries,
            "retry_delay": config.retry_delay,
            "custom_headers": config.custom_headers or {},
            "custom_params": config.custom_params or {}
        }
    
    async def test_config(
        self,
        db: AsyncSession,
        user_id: UUID,
        test_request: LLMTestRequest
    ) -> LLMTestResponse:
        """测试LLM配置"""
        
        start_time = time.time()
        
        try:
            # 获取配置
            config_dict = await self.get_config_for_generation(
                db, user_id, test_request.config_id
            )
            
            if not config_dict:
                return LLMTestResponse(
                    success=False,
                    response_time=time.time() - start_time,
                    tokens_used=0,
                    error_message="Configuration not found or inactive"
                )
            
            # 这里调用实际的LLM服务进行测试
            # 暂时返回模拟结果
            response_time = time.time() - start_time
            
            return LLMTestResponse(
                success=True,
                response_text="Test response from AI model",
                response_time=response_time,
                tokens_used=50,
                error_message=None
            )
            
        except Exception as e:
            logger.error(f"LLM config test failed: {e}")
            return LLMTestResponse(
                success=False,
                response_time=time.time() - start_time,
                tokens_used=0,
                error_message=str(e)
            )
    
    async def log_usage(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: UUID,
        request_id: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: str = "POST",
        prompt_tokens: int = 0,
        completion_tokens: int = 0,
        response_time: Optional[float] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        estimated_cost: Optional[float] = None
    ) -> LLMUsageLog:
        """记录LLM使用日志"""
        
        total_tokens = prompt_tokens + completion_tokens
        
        # 创建使用日志
        usage_log = LLMUsageLog(
            user_id=user_id,
            config_id=config_id,
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=total_tokens,
            response_time=response_time,
            success=success,
            error_message=error_message,
            estimated_cost=estimated_cost
        )
        
        db.add(usage_log)
        
        # 更新配置的使用统计
        config = await self.get_config(db, user_id, config_id)
        if config:
            config.total_requests += 1
            config.total_tokens += total_tokens
            config.last_used_at = func.now()
        
        await db.commit()
        await db.refresh(usage_log)
        
        return usage_log
    
    async def get_usage_stats(
        self,
        db: AsyncSession,
        user_id: UUID,
        config_id: Optional[UUID] = None,
        days: int = 30
    ) -> LLMUsageStats:
        """获取LLM使用统计"""
        
        # 构建查询条件
        conditions = [LLMUsageLog.user_id == user_id]
        if config_id:
            conditions.append(LLMUsageLog.config_id == config_id)
        
        # 时间范围过滤
        from datetime import datetime, timedelta
        start_date = datetime.utcnow() - timedelta(days=days)
        conditions.append(LLMUsageLog.created_at >= start_date)
        
        # 基础统计
        base_stmt = select(
            func.count(LLMUsageLog.id).label("total_requests"),
            func.sum(LLMUsageLog.total_tokens).label("total_tokens"),
            func.sum(LLMUsageLog.estimated_cost).label("total_cost"),
            func.avg(LLMUsageLog.response_time).label("avg_response_time"),
            func.avg(func.cast(LLMUsageLog.success, func.INTEGER)).label("success_rate")
        ).where(and_(*conditions))
        
        result = await db.execute(base_stmt)
        stats = result.first()
        
        return LLMUsageStats(
            total_requests=stats.total_requests or 0,
            total_tokens=stats.total_tokens or 0,
            total_cost=float(stats.total_cost or 0.0),
            avg_response_time=float(stats.avg_response_time or 0.0),
            success_rate=float(stats.success_rate or 0.0),
            most_used_model=None,  # TODO: 实现最常用模型统计
            daily_usage=[],  # TODO: 实现每日统计
            model_usage=[]   # TODO: 实现模型使用统计
        )
    
    async def _unset_other_defaults(self, db: AsyncSession, user_id: UUID):
        """取消用户的其他默认配置"""
        stmt = select(LLMConfig).where(
            and_(
                LLMConfig.user_id == user_id,
                LLMConfig.is_default == True
            )
        )
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        for config in configs:
            config.is_default = False
        
        await db.commit()


# 全局实例
llm_config_service = LLMConfigService() 