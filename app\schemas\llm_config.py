#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LLM配置数据验证模式
"""
from typing import Optional, Dict, Any, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, field_validator

from enum import Enum


class LLMProvider(str, Enum):
    """LLM服务提供商"""
    OPENAI = "openai"
    AZURE = "azure"
    DASHSCOPE = "dashscope"
    CUSTOM = "custom"


class LLMConfigBase(BaseModel):
    """LLM配置基础模式"""
    name: str = Field(..., description="配置名称", min_length=1, max_length=255)
    provider: LLMProvider = Field(..., description="服务提供商")
    is_default: bool = Field(False, description="是否为默认配置")
    is_active: bool = Field(True, description="是否启用")
    
    # API配置
    api_key: Optional[str] = Field(None, description="API密钥", max_length=500)
    base_url: Optional[str] = Field(None, description="API基础URL", max_length=500)
    api_version: Optional[str] = Field(None, description="API版本", max_length=50)
    
    # 模型参数
    model: str = Field(..., description="模型名称", min_length=1, max_length=100)
    temperature: float = Field(0.7, description="温度参数", ge=0.0, le=2.0)
    max_tokens: int = Field(2048, description="最大token数", ge=1, le=32000)
    top_p: float = Field(1.0, description="top_p参数", ge=0.0, le=1.0)
    frequency_penalty: float = Field(0.0, description="频率惩罚", ge=-2.0, le=2.0)
    presence_penalty: float = Field(0.0, description="存在惩罚", ge=-2.0, le=2.0)
    
    # 请求配置
    timeout: int = Field(60, description="请求超时时间（秒）", ge=1, le=600)
    max_retries: int = Field(3, description="最大重试次数", ge=0, le=10)
    retry_delay: float = Field(1.0, description="重试延迟（秒）", ge=0.1, le=60.0)
    
    # 扩展配置
    custom_headers: Optional[Dict[str, str]] = Field(None, description="自定义请求头")
    custom_params: Optional[Dict[str, Any]] = Field(None, description="自定义参数")

    @field_validator('base_url')
    @classmethod
    def validate_base_url(cls, v: Optional[str]) -> Optional[str]:
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Base URL must start with http:// or https://')
        return v


class LLMConfigCreate(LLMConfigBase):
    """创建LLM配置"""
    pass


class LLMConfigUpdate(BaseModel):
    """更新LLM配置"""
    name: Optional[str] = Field(None, description="配置名称", min_length=1, max_length=255)
    is_default: Optional[bool] = Field(None, description="是否为默认配置")
    is_active: Optional[bool] = Field(None, description="是否启用")
    
    # API配置
    api_key: Optional[str] = Field(None, description="API密钥", max_length=500)
    base_url: Optional[str] = Field(None, description="API基础URL", max_length=500)
    api_version: Optional[str] = Field(None, description="API版本", max_length=50)
    
    # 模型参数
    model: Optional[str] = Field(None, description="模型名称", min_length=1, max_length=100)
    temperature: Optional[float] = Field(None, description="温度参数", ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(None, description="最大token数", ge=1, le=32000)
    top_p: Optional[float] = Field(None, description="top_p参数", ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(None, description="频率惩罚", ge=-2.0, le=2.0)
    presence_penalty: Optional[float] = Field(None, description="存在惩罚", ge=-2.0, le=2.0)
    
    # 请求配置
    timeout: Optional[int] = Field(None, description="请求超时时间（秒）", ge=1, le=600)
    max_retries: Optional[int] = Field(None, description="最大重试次数", ge=0, le=10)
    retry_delay: Optional[float] = Field(None, description="重试延迟（秒）", ge=0.1, le=60.0)
    
    # 扩展配置
    custom_headers: Optional[Dict[str, str]] = Field(None, description="自定义请求头")
    custom_params: Optional[Dict[str, Any]] = Field(None, description="自定义参数")

    @field_validator('base_url')
    @classmethod
    def validate_base_url(cls, v: Optional[str]) -> Optional[str]:
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Base URL must start with http:// or https://')
        return v


class LLMConfigResponse(LLMConfigBase):
    """LLM配置响应"""
    id: UUID
    user_id: UUID
    
    # 使用统计
    total_requests: int = Field(0, description="总请求次数")
    total_tokens: int = Field(0, description="总token使用量")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    
    # 时间戳
    created_at: datetime
    updated_at: datetime
    
    # 隐藏敏感信息
    api_key: Optional[str] = Field(None, description="API密钥（隐藏）")
    
    @field_validator('api_key')
    @classmethod
    def mask_api_key(cls, v: Optional[str]) -> Optional[str]:
        if not v:
            return None
        if len(v) <= 8:
            return "*" * len(v)
        return v[:4] + "*" * (len(v) - 8) + v[-4:]

    model_config = {"from_attributes": True}


class LLMConfigList(BaseModel):
    """LLM配置列表响应"""
    items: List[LLMConfigResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


class LLMUsageLogResponse(BaseModel):
    """LLM使用日志响应"""
    id: UUID
    user_id: UUID
    config_id: UUID
    
    # 请求信息
    request_id: Optional[str] = Field(None, description="请求ID")
    endpoint: Optional[str] = Field(None, description="请求端点")
    method: Optional[str] = Field(None, description="请求方法")
    
    # 使用统计
    prompt_tokens: int = Field(0, description="输入token数")
    completion_tokens: int = Field(0, description="输出token数")
    total_tokens: int = Field(0, description="总token数")
    
    # 性能指标
    response_time: Optional[float] = Field(None, description="响应时间（秒）")
    success: bool = Field(True, description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    
    # 成本信息
    estimated_cost: Optional[float] = Field(None, description="预估成本（美元）")
    
    # 时间戳
    created_at: datetime
    
    model_config = {"from_attributes": True}


class LLMTestRequest(BaseModel):
    """LLM配置测试请求"""
    config_id: UUID = Field(..., description="配置ID")
    test_prompt: str = Field("Hello, how are you?", description="测试提示词")


class LLMTestResponse(BaseModel):
    """LLM配置测试响应"""
    success: bool = Field(..., description="测试是否成功")
    response_text: Optional[str] = Field(None, description="AI响应内容")
    response_time: float = Field(..., description="响应时间（秒）")
    tokens_used: int = Field(..., description="使用的token数")
    error_message: Optional[str] = Field(None, description="错误信息")


class LLMUsageStats(BaseModel):
    """LLM使用统计"""
    total_requests: int = Field(0, description="总请求次数")
    total_tokens: int = Field(0, description="总token使用量")
    total_cost: float = Field(0.0, description="总成本（美元）")
    avg_response_time: float = Field(0.0, description="平均响应时间（秒）")
    success_rate: float = Field(0.0, description="成功率")
    most_used_model: Optional[str] = Field(None, description="最常用模型")
    
    # 按时间分组的统计
    daily_usage: List[Dict[str, Any]] = Field([], description="每日使用统计")
    model_usage: List[Dict[str, Any]] = Field([], description="各模型使用统计") 