#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
内容生成相关数据传输对象
"""
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel


class ArchitectureGenerationRequest(BaseModel):
    """架构生成请求"""
    novel_id: Optional[UUID] = None
    theme: str
    genre: str
    target_length: Optional[int] = None
    style_preferences: Optional[dict] = None


class ArchitectureGenerationResponse(BaseModel):
    """架构生成响应"""
    task_id: str
    status: str
    message: str


class ChapterGenerationRequest(BaseModel):
    """章节生成请求"""
    novel_id: UUID
    chapter_number: int
    chapter_outline: Optional[str] = None
    context_chapters: Optional[List[UUID]] = None
    style_preferences: Optional[dict] = None


class ChapterGenerationResponse(BaseModel):
    """章节生成响应"""
    task_id: str
    status: str
    message: str


class CharacterGenerationRequest(BaseModel):
    """角色生成请求"""
    novel_id: UUID
    character_role: str
    character_traits: Optional[List[str]] = None
    story_context: Optional[str] = None


class CharacterGenerationResponse(BaseModel):
    """角色生成响应"""
    task_id: str
    status: str
    message: str 
