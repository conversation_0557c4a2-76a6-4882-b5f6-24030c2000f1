# =================================================================
# AI小说生成器后端系统 - 环境配置示例文件
# 
# 使用说明：
# 1. 复制此文件并重命名为 .env
# 2. 根据你的实际环境修改对应配置
# 3. 确保修改所有包含 "CHANGE_ME" 的配置项
# =================================================================

# =================================================================
# 基础应用配置
# =================================================================
PROJECT_NAME=AI Novel Generator
VERSION=1.0.0
ENVIRONMENT=production
DEBUG=true

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# =================================================================
# 数据库配置 (PostgreSQL) - 与docker-compose.yml保持一致
# =================================================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=novel_user
POSTGRES_PASSWORD=novel_password
POSTGRES_DB=ai_novel_generator

# 可选：直接指定完整的数据库URL（优先级高于上面的单独配置）
# DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator

# Docker内部网络配置（如果API也在容器中运行）
# POSTGRES_HOST=novel_postgres
# DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@novel_postgres:5432/ai_novel_generator

# =================================================================
# Redis配置 (缓存和消息队列) - 与docker-compose.yml保持一致
# =================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 可选：直接指定完整的Redis URL
# REDIS_URL=redis://localhost:6379/0

# Docker内部网络配置（如果API也在容器中运行）
# REDIS_HOST=novel_redis
# REDIS_URL=redis://novel_redis:6379/0

# =================================================================
# 安全配置
# =================================================================
# JWT密钥 - 必须是32字符以上的强密码
SECRET_KEY=ai_novel_generator_super_secret_key_for_jwt_tokens_2024

# 访问令牌过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# 跨域配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
TRUSTED_HOSTS=localhost,127.0.0.1,your-domain.com

# =================================================================
# 微信小程序配置 (可选)
# =================================================================
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# =================================================================
# LLM服务配置 (必须配置其中一种)
# =================================================================

# 主LLM提供商选择: openai, azure, dashscope
LLM_PROVIDER=openai

# 通用LLM配置
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048
LLM_TIMEOUT=600

# -----------------------------------------------------------------
# OpenAI配置 (LLM_PROVIDER=openai时使用)
# -----------------------------------------------------------------
LLM_API_KEY=sk-your-openai-api-key-here
LLM_BASE_URL=https://api.openai.com/v1

# 可选：使用自定义OpenAI兼容接口
# LLM_BASE_URL=https://your-custom-openai-api.com/v1

# -----------------------------------------------------------------
# Azure OpenAI配置 (LLM_PROVIDER=azure时使用)
# -----------------------------------------------------------------
# LLM_API_KEY=your-azure-openai-api-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
# AZURE_OPENAI_API_VERSION=2024-02-15-preview
# LLM_MODEL=gpt-35-turbo

# -----------------------------------------------------------------
# 阿里云通义千问配置 (LLM_PROVIDER=dashscope时使用)
# -----------------------------------------------------------------
# DASHSCOPE_API_KEY=sk-your-dashscope-api-key
# LLM_MODEL=qwen-turbo
# LLM_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# =================================================================
# 向量数据库配置
# =================================================================
VECTOR_DB_TYPE=chroma
CHROMA_PERSIST_DIRECTORY=./chroma_db

# 可选：Pinecone配置
# PINECONE_API_KEY=your-pinecone-api-key
# PINECONE_ENVIRONMENT=your-pinecone-environment
# PINECONE_INDEX_NAME=your-pinecone-index

# =================================================================
# 文件存储配置
# =================================================================
UPLOAD_PATH=./uploads
MAX_UPLOAD_SIZE=52428800

# =================================================================
# 任务队列配置
# =================================================================
TASK_TIMEOUT=3600
MAX_CONCURRENT_TASKS=10

# Celery配置 (通常使用默认值即可)
# CELERY_BROKER_URL=redis://localhost:6379/0
# CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =================================================================
# 其他配置
# =================================================================
TIMEZONE=Asia/Shanghai
LOG_FILE=./logs/app.log

# =================================================================
# Docker部署配置
# =================================================================

# Docker部署模式选择
# 1. 仅基础服务：docker-compose up -d postgres redis
# 2. 完整应用：docker-compose --profile app up -d
# 3. 包含监控：docker-compose --profile app --profile monitoring up -d
# 4. 包含向量数据库：docker-compose --profile app --profile vector_db up -d

# Docker内部网络主机名（容器化部署时使用）
# POSTGRES_HOST=novel_postgres
# REDIS_HOST=novel_redis

# =================================================================
# 配置验证说明
# 
# 必须配置的关键项目：
# 1. POSTGRES_PASSWORD - 数据库密码（已设置为novel_password）
# 2. SECRET_KEY - JWT密钥（已设置32字符以上）
# 3. LLM_API_KEY - AI服务API密钥（需要配置真实密钥）
# 4. LLM_PROVIDER - AI服务提供商
# 
# 可选但推荐配置：
# 5. WECHAT_APP_ID/WECHAT_APP_SECRET - 微信小程序集成
# 6. REDIS_PASSWORD - Redis密码（生产环境推荐）
# 7. BACKEND_CORS_ORIGINS - 前端域名
# 
# 部署方式：
# 1. 本地开发：使用虚拟环境 + Docker基础服务
# 2. 容器化部署：使用Docker Compose完整堆栈
# 3. 混合部署：Docker基础服务 + 本地应用服务
# ================================================================= 