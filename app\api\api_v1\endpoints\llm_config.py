#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LLM配置管理API接口
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    NotFoundException, DatabaseException, LLMServiceException, ErrorCode
)
from app.models.user import User
from app.schemas.llm_config import (
    LLMConfigCreate, LLMConfigUpdate, LLMConfigResponse, LLMConfigList,
    LLMUsageLogResponse, LLMUsageStats, LLMTestRequest, LLMTestResponse
)
from app.services.llm_config_service import llm_config_service
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=LLMConfigResponse)
async def create_llm_config(
    config_data: LLMConfigCreate,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """创建LLM配置"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        config = await llm_config_service.create_config(
            db=db,
            user_id=current_user.id,
            config_data=config_data
        )
        
        # 记录创建配置日志
        log_business_operation(
            logger=logger.logger,
            operation="create_llm_config",
            details={
                "config_name": config_data.name,
                "provider": config_data.provider,
                "model": config_data.model,
                "is_default": config_data.is_default
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return LLMConfigResponse.model_validate(config)
        
    except Exception as e:
        logger.error(f"创建LLM配置失败 - 用户ID: {user_id}, 配置名: {config_data.name}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'create_llm_config'
        })
        raise DatabaseException(
            message="创建LLM配置失败",
            details={"config_name": config_data.name}
        )


@router.get("/", response_model=LLMConfigList)
async def get_llm_configs(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    active_only: bool = Query(False, description="仅显示启用的配置"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """获取LLM配置列表"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        skip = (page - 1) * page_size
        
        configs, total = await llm_config_service.get_configs(
            db=db,
            user_id=current_user.id,
            skip=skip,
            limit=page_size,
            active_only=active_only
        )
        
        total_pages = (total + page_size - 1) // page_size
        
        config_responses = [LLMConfigResponse.model_validate(config) for config in configs]
        
        # 记录获取配置列表日志
        log_business_operation(
            logger=logger.logger,
            operation="get_llm_configs",
            details={
                "total_configs": total,
                "page": page,
                "page_size": page_size,
                "active_only": active_only
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return LLMConfigList(
            items=config_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"获取LLM配置列表失败 - 用户ID: {user_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_llm_configs'
        })
        raise DatabaseException(
            message="获取LLM配置列表失败",
            details={"user_id": user_id}
        )


@router.get("/{config_id}", response_model=LLMConfigResponse)
async def get_llm_config(
    config_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """获取单个LLM配置"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        config = await llm_config_service.get_config(
            db=db,
            user_id=current_user.id,
            config_id=config_id
        )
        
        if not config:
            raise NotFoundException(
                message=f"LLM配置 {config_id} 不存在",
                error_code=ErrorCode.LLM_CONFIG_NOT_FOUND
            )
        
        # 记录获取配置详情日志
        log_business_operation(
            logger=logger.logger,
            operation="get_llm_config",
            details={
                "config_id": config_id,
                "config_name": config.name,
                "provider": config.provider
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return LLMConfigResponse.model_validate(config)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取LLM配置失败 - 用户ID: {user_id}, 配置ID: {config_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_llm_config'
        })
        raise DatabaseException(
            message="获取LLM配置失败",
            details={"config_id": config_id}
        )


@router.put("/{config_id}", response_model=LLMConfigResponse)
async def update_llm_config(
    config_id: UUID,
    config_data: LLMConfigUpdate,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """更新LLM配置"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        config = await llm_config_service.update_config(
            db=db,
            user_id=current_user.id,
            config_id=config_id,
            config_data=config_data
        )
        
        if not config:
            raise NotFoundException(
                message=f"LLM配置 {config_id} 不存在",
                error_code=ErrorCode.LLM_CONFIG_NOT_FOUND
            )
        
        # 记录更新配置日志
        log_business_operation(
            logger=logger.logger,
            operation="update_llm_config",
            details={
                "config_id": config_id,
                "config_name": config.name,
                "updated_fields": list(config_data.dict(exclude_unset=True).keys())
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return LLMConfigResponse.model_validate(config)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"更新LLM配置失败 - 用户ID: {user_id}, 配置ID: {config_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'update_llm_config'
        })
        raise DatabaseException(
            message="更新LLM配置失败",
            details={"config_id": config_id}
        )


@router.delete("/{config_id}")
async def delete_llm_config(
    config_id: UUID,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """删除LLM配置"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        success = await llm_config_service.delete_config(
            db=db,
            user_id=current_user.id,
            config_id=config_id
        )
        
        if not success:
            raise NotFoundException(
                message=f"LLM配置 {config_id} 不存在",
                error_code=ErrorCode.LLM_CONFIG_NOT_FOUND
            )
        
        # 记录删除配置日志
        log_business_operation(
            logger=logger.logger,
            operation="delete_llm_config",
            details={"config_id": config_id},
            user_id=user_id,
            request_id=request_id
        )
        
        return {"message": "LLM配置删除成功"}
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"删除LLM配置失败 - 用户ID: {user_id}, 配置ID: {config_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'delete_llm_config'
        })
        raise DatabaseException(
            message="删除LLM配置失败",
            details={"config_id": config_id}
        )


@router.get("/default/config", response_model=LLMConfigResponse)
async def get_default_config(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """获取默认LLM配置"""
    
    config = await llm_config_service.get_default_config(
        db=db,
        user_id=current_user.id
    )
    
    if not config:
        raise NotFoundException("No default LLM config found")
    
    return LLMConfigResponse.model_validate(config)


@router.post("/{config_id}/set-default")
async def set_default_config(
    config_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """设置默认LLM配置"""
    
    config = await llm_config_service.update_config(
        db=db,
        user_id=current_user.id,
        config_id=config_id,
        config_data=LLMConfigUpdate(is_default=True)
    )
    
    if not config:
        raise NotFoundException(f"LLM config {config_id} not found")
    
    return {"message": "Default config set successfully"}


@router.post("/test", response_model=LLMTestResponse)
async def test_llm_config(
    test_request: LLMTestRequest,
    request: Request,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """测试LLM配置"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        result = await llm_config_service.test_config(
            db=db,
            user_id=current_user.id,
            test_request=test_request
        )
        
        # 记录测试配置日志
        log_business_operation(
            logger=logger.logger,
            operation="test_llm_config",
            details={
                "config_id": test_request.config_id,
                "test_message": test_request.message[:100] if test_request.message else None,
                "test_result": result.success
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"测试LLM配置失败 - 用户ID: {user_id}, 配置ID: {test_request.config_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'test_llm_config'
        })
        raise LLMServiceException(
            message="测试LLM配置失败",
            details={"config_id": test_request.config_id}
        )


@router.get("/{config_id}/usage-stats", response_model=LLMUsageStats)
async def get_config_usage_stats(
    config_id: UUID,
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """获取配置使用统计"""
    
    # 验证配置是否存在
    config = await llm_config_service.get_config(
        db=db,
        user_id=current_user.id,
        config_id=config_id
    )
    
    if not config:
        raise NotFoundException(f"LLM config {config_id} not found")
    
    stats = await llm_config_service.get_usage_stats(
        db=db,
        user_id=current_user.id,
        config_id=config_id,
        days=days
    )
    
    return stats


@router.get("/usage/stats", response_model=LLMUsageStats)
async def get_overall_usage_stats(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """获取总体使用统计"""
    
    stats = await llm_config_service.get_usage_stats(
        db=db,
        user_id=current_user.id,
        config_id=None,
        days=days
    )
    
    return stats


@router.post("/{config_id}/duplicate", response_model=LLMConfigResponse)
async def duplicate_config(
    config_id: UUID,
    new_name: str = Query(..., description="新配置名称"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """复制LLM配置"""
    
    # 获取原配置
    original_config = await llm_config_service.get_config(
        db=db,
        user_id=current_user.id,
        config_id=config_id
    )
    
    if not original_config:
        raise NotFoundException(f"LLM config {config_id} not found")
    
    # 创建新配置
    config_data = LLMConfigCreate(
        name=new_name,
        provider=original_config.provider,
        is_default=False,  # 复制的配置不设为默认
        is_active=original_config.is_active,
        api_key=None,  # 不复制API密钥，需要用户重新设置
        base_url=original_config.base_url,
        api_version=original_config.api_version,
        model=original_config.model,
        temperature=original_config.temperature,
        max_tokens=original_config.max_tokens,
        top_p=original_config.top_p,
        frequency_penalty=original_config.frequency_penalty,
        presence_penalty=original_config.presence_penalty,
        timeout=original_config.timeout,
        max_retries=original_config.max_retries,
        retry_delay=original_config.retry_delay,
        custom_headers=original_config.custom_headers,
        custom_params=original_config.custom_params
    )
    
    new_config = await llm_config_service.create_config(
        db=db,
        user_id=current_user.id,
        config_data=config_data
    )
    
    return LLMConfigResponse.model_validate(new_config)


@router.get("/providers/available")
async def get_available_providers():
    """获取可用的LLM服务提供商"""
    
    providers = [
        {
            "name": "openai",
            "display_name": "OpenAI",
            "description": "OpenAI官方API服务",
            "default_base_url": "https://api.openai.com/v1",
            "models": [
                "gpt-4-turbo-preview",
                "gpt-4",
                "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k"
            ]
        },
        {
            "name": "azure",
            "display_name": "Azure OpenAI",
            "description": "Microsoft Azure OpenAI服务",
            "default_base_url": "https://your-resource.openai.azure.com",
            "models": [
                "gpt-4",
                "gpt-35-turbo",
                "gpt-35-turbo-16k"
            ]
        },
        {
            "name": "dashscope",
            "display_name": "通义千问",
            "description": "阿里云通义千问服务",
            "default_base_url": "https://dashscope.aliyuncs.com/api/v1",
            "models": [
                "qwen-turbo",
                "qwen-plus",
                "qwen-max",
                "qwen-max-1201",
                "qwen-max-longcontext"
            ]
        },
        {
            "name": "custom",
            "display_name": "自定义",
            "description": "自定义API服务",
            "default_base_url": "",
            "models": []
        }
    ]
    
    return {"providers": providers}


@router.post("/{config_id}/toggle-status")
async def toggle_config_status(
    config_id: UUID,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(AuthService.get_current_user)
):
    """切换配置启用状态"""
    
    # 获取当前配置
    config = await llm_config_service.get_config(
        db=db,
        user_id=current_user.id,
        config_id=config_id
    )
    
    if not config:
        raise NotFoundException(f"LLM config {config_id} not found")
    
    # 切换状态
    updated_config = await llm_config_service.update_config(
        db=db,
        user_id=current_user.id,
        config_id=config_id,
        config_data=LLMConfigUpdate(is_active=not config.is_active)
    )
    
    status_text = "enabled" if updated_config.is_active else "disabled"
    return {"message": f"Config {status_text} successfully"} 