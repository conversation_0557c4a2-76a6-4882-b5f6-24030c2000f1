#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
小说生成相关的Celery任务
"""
import logging
import json
import asyncio
from uuid import UUID
from datetime import datetime
from typing import Dict, Any, List
from celery import Task
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
import json

from app.core.celery_app import celery_app
from app.core.database import AsyncSessionLocal
from app.models.generation_task import GenerationTask, TaskStatus
from app.core.websocket import send_task_update

# 统一在顶部导入，避免函数内重复导入
from app.models.document import Document, DocumentType
from app.models.novel import Novel
from app.models.chapter import Chapter
from app.models.generation_state import GenerationState
from app.services.cognitive_core import CognitiveWritingCore
from app.services.llm_service import llm_service
from app.services.prompt_service import PromptService
from app.services.vector_service import VectorStoreService
from app.services.summary_service import HierarchicalSummaryService
from app.services.quality_monitor import QualityMonitor

logger = logging.getLogger(__name__)


class CallbackTask(Task):
    """带有回调功能的任务基类"""
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功时的回调"""
        logger.info(f"Task {task_id} succeeded with result: {retval}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败时的回调"""
        logger.error(f"Task {task_id} failed with exception: {exc}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试时的回调"""
        logger.warning(f"Task {task_id} retrying due to: {exc}")
    
    def _run_async_task(self, async_func, *args, **kwargs):
        """运行异步任务的辅助方法"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(async_func(*args, **kwargs))


@celery_app.task(bind=True, base=CallbackTask)
def generate_novel_architecture(self, task_id: str, user_id, novel_id, parameters: dict):
    """生成小说架构任务"""
    # 使用Celery自身的任务ID
    celery_task_id = self.request.id

    # 确保ID参数是UUID对象
    if isinstance(user_id, str):
        user_id = UUID(user_id)
    if isinstance(novel_id, str):
        novel_id = UUID(novel_id)

    return self._run_async_task(
        _generate_novel_architecture_async,
        celery_task_id, user_id, novel_id, parameters
    )


@celery_app.task(bind=True, base=CallbackTask)
def generate_chapter_content(self, task_id: str, user_id, novel_id, chapter_id, parameters: dict):
    """生成章节内容任务"""
    # 使用Celery自身的任务ID
    actual_task_id = self.request.id
    
    # 确保ID参数是UUID对象
    if isinstance(user_id, str):
        user_id = UUID(user_id)
    if isinstance(novel_id, str):
        novel_id = UUID(novel_id)
    if isinstance(chapter_id, str):
        chapter_id = UUID(chapter_id)
    
    return self._run_async_task(
        _generate_chapter_content_async,
        actual_task_id, user_id, novel_id, chapter_id, parameters
    )


async def _load_or_create_generation_state(session: AsyncSession, task_id: str, novel_id: UUID) -> 'GenerationState':
    """加载或创建生成状态记录，实现断点续传"""
    
    # 尝试加载现有状态
    query = select(GenerationState).where(
        GenerationState.novel_id == novel_id,
        GenerationState.generation_type == "architecture",
        GenerationState.is_completed == False,
        GenerationState.is_failed == False
    ).order_by(GenerationState.created_at.desc())
    
    result = await session.execute(query)
    state = result.scalar_one_or_none()
    
    if state:
        # 更新task_id为当前任务
        state.task_id = task_id
        logger.info(f"Resuming architecture generation from step: {state.current_step}")
    else:
        # 创建新的状态记录
        state = GenerationState(
            novel_id=novel_id,
            task_id=task_id,
            generation_type="architecture",
            current_step="core_seed",
            completed_steps={},
            total_progress=0
        )
        session.add(state)
        logger.info("Starting new architecture generation")
    
    await session.commit()
    return state


async def _update_generation_state(session: AsyncSession, state: 'GenerationState', step: str, result: str, progress: int):
    """更新生成状态"""
    state.completed_steps[state.current_step] = result
    state.current_step = step
    state.total_progress = progress
    await session.commit()


async def _complete_generation_state(session: AsyncSession, state: 'GenerationState'):
    """标记生成状态为完成"""
    from sqlalchemy.sql import func
    state.is_completed = True
    state.completed_at = func.now()
    await session.commit()


async def _generate_novel_architecture_async(celery_task_id: str, user_id: UUID, novel_id: UUID, parameters: dict):
    """异步生成小说架构 - 带断点续传的四步生成流程"""

    async with AsyncSessionLocal() as session:
        try:
            # 通过Celery任务ID找到数据库任务记录，获取数据库主键UUID
            db_task_query = select(GenerationTask).where(GenerationTask.task_id == celery_task_id)
            db_task_result = await session.execute(db_task_query)
            db_task = db_task_result.scalar_one_or_none()

            if not db_task:
                logger.error(f"Database task not found for Celery task ID: {celery_task_id}")
                raise Exception("Database task not found")

            websocket_task_id = celery_task_id

            # 更新任务状态为开始执行
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 0)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 0, "初始化LLM服务...",
                                 current_stage="initializing",
                                 detailed_progress={"step": "准备", "step_name": "初始化LLM服务"})
            
            # 确保LLM服务已初始化
            from app.services.llm_service import initialize_default_llm_service
            llm_initialized = await initialize_default_llm_service()
            if not llm_initialized:
                error_msg = "LLM服务初始化失败，请检查API配置"
                logger.error(error_msg)
                await _update_task_status(session, celery_task_id, TaskStatus.FAILED, 0, error_message=error_msg)
                await send_task_update(websocket_task_id, "FAILED", 0, error_msg,
                                     current_stage="failed",
                                     detailed_progress={"step": "失败", "step_name": "LLM服务初始化失败"})
                return {"error": error_msg}

            # 加载或创建生成状态
            state = await _load_or_create_generation_state(session, celery_task_id, novel_id)

            # 更新任务状态
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, state.total_progress)
            await send_task_update(websocket_task_id, "IN_PROGRESS", state.total_progress, "初始化认知写作核心...",
                                 current_stage="cognitive_init",
                                 detailed_progress={"step": "准备", "step_name": "认知写作核心初始化"})
            
            # 初始化认知核心
            cognitive_core = CognitiveWritingCore(session)
            if not await cognitive_core.initialize_from_novel(novel_id):
                raise Exception("Failed to initialize cognitive core")
            
            prompt_service = PromptService(session)
            
            # 获取小说基础信息
            novel_query = select(Novel).where(Novel.id == novel_id)
            result = await session.execute(novel_query)
            novel = result.scalar_one_or_none()
            
            if not novel:
                raise Exception("Novel not found")
            
            # 从状态中恢复已完成的步骤
            generated_data = state.completed_steps.copy()
            
            # Step 1: 核心种子生成
            if "core_seed" not in generated_data:
                if state.current_step == "core_seed":
                    await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 20,
                                                     current_stage="core_seed",
                                                     detailed_progress={"step": "1/4", "step_name": "核心种子生成"})
                    await send_task_update(websocket_task_id, "IN_PROGRESS", 20, "生成核心种子设定...",
                                         current_stage="core_seed",
                                         detailed_progress={"step": "1/4", "step_name": "核心种子生成"})
                    
                    core_seed_prompt = await prompt_service.format_prompt(
                        "core_seed_prompt",
                        topic=novel.description or "通用主题",
                        genre=novel.genre or "通用",
                        number_of_chapters=30,
                        word_number=int(novel.target_length / 30) if novel.target_length else 2000
                    )
                    
                    if core_seed_prompt:
                        # 使用真正的实时流式调用
                        from app.services.llm_service import StreamingConfig

                        streaming_config = StreamingConfig(
                            chunk_size=30,      # 较小的chunk_size以获得更频繁的更新
                            flush_interval=0.2, # 200ms刷新间隔
                            enable_recovery=True
                        )

                        core_seed_result = await llm_service.stream_invoke_realtime(
                            core_seed_prompt,
                            task_id=websocket_task_id,
                            stage="core_seed",
                            streaming_config=streaming_config
                        )
                        generated_data["core_seed"] = core_seed_result
                        await _update_generation_state(session, state, "character_dynamics", core_seed_result, 20)
                    else:
                        raise Exception("Failed to get core seed prompt template")
            
            # Step 2: 角色动力学生成
            if "character_dynamics" not in generated_data:
                if state.current_step == "character_dynamics":
                    await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 40,
                                                     current_stage="character_dynamics",
                                                     detailed_progress={"step": "2/4", "step_name": "角色动力学构建"})
                    await send_task_update(websocket_task_id, "IN_PROGRESS", 40, "构建角色动力学...",
                                         current_stage="character_dynamics",
                                         detailed_progress={"step": "2/4", "step_name": "角色动力学构建"})
                    
                    character_prompt = await prompt_service.format_prompt(
                        "character_dynamics_prompt",
                        topic=novel.description or "通用主题",
                        core_seed=generated_data["core_seed"]
                    )
                    
                    if character_prompt:
                        # 使用真正的实时流式调用
                        character_result = await llm_service.stream_invoke_realtime(
                            character_prompt,
                            task_id=websocket_task_id,
                            stage="character_dynamics",
                            streaming_config=streaming_config
                        )
                        generated_data["character_dynamics"] = character_result
                        await _update_generation_state(session, state, "world_building", character_result, 40)

                        # 生成初始角色状态（基于旧版本逻辑）
                        await send_task_update(websocket_task_id, "IN_PROGRESS", 40, "生成角色状态档案...",
                                             current_stage="character_dynamics",
                                             detailed_progress={"step": "2/4", "step_name": "角色状态生成"})
                        
                        char_state_prompt = await prompt_service.format_prompt(
                            "create_character_state_prompt",
                            topic=novel.description or "通用主题",
                            character_dynamics=character_result
                        )
                        
                        if char_state_prompt:
                            char_state_result = await llm_service.stream_invoke_with_retry(char_state_prompt)
                            # 保存角色状态文档
                            char_state_doc = Document(
                                novel_id=novel_id,
                                doc_type=DocumentType.CHARACTER_STATE,
                                title="人物关系状态",
                                content=char_state_result
                            )
                            session.add(char_state_doc)
                            await session.commit()
                    else:
                        raise Exception("Failed to get character dynamics prompt template")
            
            # Step 3: 世界观构建
            if "world_building" not in generated_data:
                if state.current_step == "world_building":
                    await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 60,
                                                     current_stage="world_building",
                                                     detailed_progress={"step": "3/4", "step_name": "世界观构建"})
                    await send_task_update(websocket_task_id, "IN_PROGRESS", 60, "设计世界观架构...",
                                         current_stage="world_building",
                                         detailed_progress={"step": "3/4", "step_name": "世界观构建"})
                    
                    world_prompt = await prompt_service.format_prompt(
                        "world_building_prompt",
                        topic=novel.description or "通用主题",
                        core_seed=generated_data["core_seed"]
                    )
                    
                    if world_prompt:
                        # 使用真正的实时流式调用
                        world_result = await llm_service.stream_invoke_realtime(
                            world_prompt,
                            task_id=websocket_task_id,
                            stage="world_building",
                            streaming_config=streaming_config
                        )
                        generated_data["world_building"] = world_result
                        await _update_generation_state(session, state, "plot_architecture", world_result, 60)
                    else:
                        raise Exception("Failed to get world building prompt template")
            
            # Step 4: 情节架构生成
            if "plot_architecture" not in generated_data:
                if state.current_step == "plot_architecture":
                    await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 80,
                                                     current_stage="plot_architecture",
                                                     detailed_progress={"step": "4/4", "step_name": "情节架构设计"})
                    await send_task_update(websocket_task_id, "IN_PROGRESS", 80, "完成情节架构设计...",
                                         current_stage="plot_architecture",
                                         detailed_progress={"step": "4/4", "step_name": "情节架构设计"})
                    
                    plot_prompt = await prompt_service.format_prompt(
                        "plot_architecture_prompt",
                        core_seed=generated_data["core_seed"],
                        character_dynamics=generated_data["character_dynamics"],
                        world_building=generated_data["world_building"]
                    )
                    
                    if plot_prompt:
                        # 使用真正的实时流式调用
                        plot_result = await llm_service.stream_invoke_realtime(
                            plot_prompt,
                            task_id=websocket_task_id,
                            stage="plot_architecture",
                            streaming_config=streaming_config
                        )
                        generated_data["plot_architecture"] = plot_result
                        await _update_generation_state(session, state, "completed", plot_result, 85)
                    else:
                        raise Exception("Failed to get plot architecture prompt template")
            
            # 组装最终架构文档
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 90,
                                    current_stage="assembling",
                                    detailed_progress={"step": "完成", "step_name": "组装架构文档"})
            await send_task_update(websocket_task_id, "IN_PROGRESS", 90, "组装架构文档...",
                                 current_stage="assembling",
                                 detailed_progress={"step": "完成", "step_name": "组装架构文档"})

            final_architecture = _assemble_architecture_document(generated_data, novel)

            # 保存到数据库
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 95,
                                    current_stage="saving",
                                    detailed_progress={"step": "完成", "step_name": "保存架构文档"})
            await send_task_update(websocket_task_id, "IN_PROGRESS", 95, "保存架构到数据库...",
                                 current_stage="saving",
                                 detailed_progress={"step": "完成", "step_name": "保存架构文档"})
            
            await _save_architecture_to_database(session, novel_id, generated_data, final_architecture)
            
            # 标记生成状态为完成
            await _complete_generation_state(session, state)
            
            # 更新认知核心状态
            await send_task_update(websocket_task_id, "IN_PROGRESS", 98, "初始化认知核心...",
                                 current_stage="initializing",
                                 detailed_progress={"step": "完成", "step_name": "认知核心初始化"})

            await cognitive_core.initialize_from_novel(novel_id)

            result = {
                "architecture": final_architecture,
                "word_count": len(final_architecture),
                "components": generated_data
            }

            # 更新任务状态为成功
            await _update_task_status(session, celery_task_id, TaskStatus.SUCCESS, 100, result,
                                    current_stage="completed",
                                    detailed_progress={"step": "完成", "step_name": "架构生成完成"},
                                    result_data=result)
            await send_task_update(websocket_task_id, "SUCCESS", 100, "小说架构生成完成！", result,
                                 current_stage="completed",
                                 detailed_progress={"step": "完成", "step_name": "架构生成完成"})

            # 发送生成完成消息
            from app.core.websocket import send_generation_complete
            await send_generation_complete(
                websocket_task_id,
                result_data=result,
                summary=f"小说架构生成完成，共生成{len(result['architecture'])}字符的架构文档"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Architecture generation failed: {e}")
            # 更新生成状态为失败
            if 'state' in locals():
                state.is_failed = True
                state.error_message = str(e)
                await session.commit()

            await _update_task_status(session, celery_task_id, TaskStatus.FAILED, 0, error_message=str(e))
            await send_task_update(websocket_task_id, "FAILED", 0, f"生成失败: {str(e)}",
                                 current_stage="failed",
                                 detailed_progress={"step": "失败", "step_name": "任务执行失败"})
            raise


def _assemble_architecture_document(generated_data: dict, novel) -> str:
    """组装架构文档，保持与旧版本格式兼容"""
    return f"""#=== 0) 小说设定 ===
主题：{novel.description or '通用主题'}，类型：{novel.genre or '通用'}，篇幅：约30章（每章{int(novel.target_length / 30) if novel.target_length else 2000}字）

#=== 1) 核心种子 ===
{generated_data.get('core_seed', '')}

#=== 2) 角色动力学 ===
{generated_data.get('character_dynamics', '')}

#=== 3) 世界观 ===
{generated_data.get('world_building', '')}

#=== 4) 三幕式情节架构 ===
{generated_data.get('plot_architecture', '')}
"""


async def _save_architecture_to_database(session: AsyncSession, novel_id: UUID, 
                                   generated_data: dict, final_architecture: str):
    """保存架构文档到数据库"""
    try:
        # 保存完整架构文档
        architecture_doc = Document(
            novel_id=novel_id,
            doc_type=DocumentType.ARCHITECTURE,
            title="小说架构",
            content=final_architecture
        )
        session.add(architecture_doc)
        
        # 保存各个组件
        for component_name, content in generated_data.items():
            if content:
                component_doc = Document(
                    novel_id=novel_id,
                    doc_type=DocumentType.BLUEPRINT,
                    title=f"架构组件-{component_name}",
                    content=content
                )
                session.add(component_doc)
        
        await session.commit()
        
    except Exception as e:
        logger.error(f"Failed to save architecture to database: {e}")
        await session.rollback()
        raise


async def _generate_chapter_content_async(celery_task_id: str, user_id: UUID, novel_id: UUID, chapter_id: UUID, parameters: dict):
    """异步生成章节内容 - 基于认知写作学的语义一致性章节生成"""

    async with AsyncSessionLocal() as session:
        try:
            # 通过Celery任务ID找到数据库任务记录，获取数据库主键UUID
            db_task_query = select(GenerationTask).where(GenerationTask.task_id == celery_task_id)
            db_task_result = await session.execute(db_task_query)
            db_task = db_task_result.scalar_one_or_none()

            if not db_task:
                logger.error(f"Database task not found for Celery task ID: {celery_task_id}")
                raise Exception("Database task not found")

            websocket_task_id = celery_task_id

            # 更新任务状态
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 0)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 0, "初始化章节生成环境...")
            
            # 获取章节信息
            chapter_query = select(Chapter).where(Chapter.id == chapter_id)
            result = await session.execute(chapter_query)
            chapter = result.scalar_one_or_none()
            
            if not chapter:
                raise Exception("Chapter not found")
            
            chapter_number = chapter.chapter_number
            
            # 初始化服务
            cognitive_core = CognitiveWritingCore(session)
            vector_service = VectorStoreService(session)
            
            # 初始化认知核心和向量存储
            if not await cognitive_core.initialize_from_novel(novel_id):
                raise Exception("Failed to initialize cognitive core")
            
            if not await vector_service.initialize_for_novel(novel_id):
                raise Exception("Failed to initialize vector store")
            
            # Step 1: 收集上下文信息
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 25)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 25, "收集章节上下文...")

            context_info = await _collect_chapter_context(
                session, novel_id, chapter_number, vector_service
            )

            # 获取用户指导参数
            user_guidance = parameters.get("user_guidance", "")

            # 初始化认知核心以增强上下文
            cognitive_core = CognitiveWritingCore(session)

            # 尝试初始化认知核心
            cognitive_initialized = await cognitive_core.initialize_from_novel(novel_id)

            if cognitive_initialized:
                # 获取认知上下文
                cognitive_context = await cognitive_core.generate_context_for_chapter(
                    chapter_number, user_guidance or ""
                )

                # 将认知上下文合并到普通上下文中
                if cognitive_context:
                    context_info["cognitive_guidance"] = cognitive_context.get("cognitive_guidance", "")
                    context_info["story_dna"] = cognitive_context.get("story_dna", {})
                    context_info["character_insights"] = cognitive_context.get("character_insights", {})
                    context_info["emotional_trajectory"] = cognitive_context.get("emotional_trajectory", {})
                    context_info["narrative_focus"] = cognitive_context.get("narrative_focus", {})

            # Step 2: 生成章节大纲
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 50)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 50, "生成章节大纲...")

            chapter_outline = await _generate_chapter_outline(
                context_info, chapter_number
            )

            # Step 3: 生成章节内容
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 75)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 75, "生成章节内容...")

            chapter_content = await _generate_chapter_content(
                chapter_outline, context_info, chapter_number
            )

            # Step 4: 质量优化
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 90)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 90, "优化内容质量...")

            optimized_content = await _optimize_chapter_content(
                chapter_content, context_info
            )

            # Step 5: 更新章节和向量存储
            await _update_task_status(session, celery_task_id, TaskStatus.IN_PROGRESS, 95)
            await send_task_update(websocket_task_id, "IN_PROGRESS", 95, "保存章节内容...")
            
            # 更新章节内容
            chapter.content = optimized_content
            chapter.word_count = len(optimized_content)
            
            # 保存章节大纲
            outline_doc = Document(
                novel_id=novel_id,
                doc_type=DocumentType.BLUEPRINT,
                title=f"第{chapter_number}章大纲",
                content=chapter_outline
            )
            session.add(outline_doc)
            
            await session.commit()
            
            # 添加到向量存储
            await vector_service.add_chapter_content(optimized_content, chapter.id)
            
            # 初始化认知核心和质量监控
            cognitive_core = CognitiveWritingCore(session)
            quality_monitor = QualityMonitor(session)
            
            # 初始化认知核心
            await cognitive_core.initialize_from_novel(novel_id)
            
            # 质量评估
            context_for_quality = {
                "character_state": context_info.get("character_state", ""),
                "global_summary": context_info.get("global_summary", "")
            }
            
            quality_metrics, quality_issues = await quality_monitor.evaluate_chapter_quality(
                optimized_content, novel_id, chapter_number, context_for_quality
            )
            
            # 生成质量报告
            quality_report = quality_monitor.generate_quality_report(quality_metrics, quality_issues)
            
            # 更新认知状态
            await cognitive_core.update_cognitive_state(optimized_content, chapter_number)
            
            # 获取认知摘要
            cognitive_summary = cognitive_core.get_cognitive_summary()
            
            # 更新多级摘要和角色状态
            await _update_all_summaries_and_character_state(session, novel_id, chapter_number, optimized_content)
            
            result = {
                "content": optimized_content,
                "outline": chapter_outline,
                "word_count": len(optimized_content),
                "chapter_id": chapter_id,
                "chapter_number": chapter_number,
                "quality_report": quality_report,
                "cognitive_summary": cognitive_summary,
                "quality_score": quality_metrics.overall_quality,
                "quality_level": quality_monitor.get_quality_level(quality_metrics.overall_quality).value
            }
            
            # 更新任务状态为成功
            await _update_task_status(session, celery_task_id, TaskStatus.SUCCESS, 100, result)
            await send_task_update(websocket_task_id, "SUCCESS", 100, "章节内容生成完成！", result)

            # 发送生成完成消息
            from app.core.websocket import send_generation_complete
            await send_generation_complete(
                websocket_task_id,
                result_data=result,
                summary=f"第{result['chapter_number']}章生成完成，共{result['word_count']}字，质量评分：{result['quality_score']:.1f}"
            )

            return result

        except Exception as e:
            logger.error(f"Chapter generation failed: {e}")
            await _update_task_status(session, celery_task_id, TaskStatus.FAILED, 0, error_message=str(e))
            await send_task_update(websocket_task_id, "FAILED", 0, f"生成失败: {str(e)}")
            raise


async def _collect_chapter_context(session: AsyncSession, novel_id: UUID, 
                             chapter_number: int, vector_service) -> Dict[str, Any]:
    """收集章节上下文信息 - 基于旧版本复杂逻辑"""
    try:
        # 初始化上下文字典
        context = {
            "novel_id": novel_id,
            "chapter_number": chapter_number,
            "novel_setting": "",
            "global_summary": "",
            "character_state": "",
            "chapter_position": "",
            "previous_chapter_excerpt": "",
            "context_excerpt": "",
            "short_summary": "",
            "next_chapter_keywords": "",
            "rhythm_score": 5,
            "rhythm_suggestion": ""
        }
        
        # 获取小说架构
        arch_query = select(Document).where(
            Document.novel_id == novel_id,
            Document.doc_type == DocumentType.ARCHITECTURE
        )
        result = await session.execute(arch_query)
        architecture_doc = result.scalar_one_or_none()
        
        if architecture_doc:
            context["novel_setting"] = architecture_doc.content
        
        # 获取多级摘要
        summary_service = HierarchicalSummaryService(session)
        
        # 获取上下文摘要
        context_summaries = await summary_service.get_context_summaries(novel_id, chapter_number)
        
        # 优先使用即时摘要，其次是中期摘要，最后是全局摘要
        if context_summaries.get("immediate_summary"):
            context["global_summary"] = context_summaries["immediate_summary"]
        elif context_summaries.get("medium_summary"):
            context["global_summary"] = context_summaries["medium_summary"]
        elif context_summaries.get("global_summary"):
            context["global_summary"] = context_summaries["global_summary"]
        else:
            context["global_summary"] = ""
        
        # 将所有摘要信息存储到上下文中
        context.update(context_summaries)
        
        # 获取角色状态
        char_state_query = select(Document).where(
            Document.novel_id == novel_id,
            Document.doc_type == DocumentType.CHARACTER_STATE
        ).order_by(Document.updated_at.desc())
        
        result = await session.execute(char_state_query)
        char_state_doc = result.scalar_one_or_none()
        
        if char_state_doc:
            context["character_state"] = char_state_doc.content
        else:
            context["character_state"] = ""
        
        # 确定章节位置（前期/中期/后期）
        total_chapters_query = select(Chapter).where(Chapter.novel_id == novel_id)
        result = await session.execute(total_chapters_query)
        total_chapters = len(result.scalars().all())
        
        if chapter_number <= total_chapters * 0.33:
            chapter_position = "前期"
            recent_texts_count = 2
        elif chapter_number <= total_chapters * 0.67:
            chapter_position = "中期"
            recent_texts_count = 2
        else:
            chapter_position = "后期"
            recent_texts_count = 3  # 后期参考更多章节
            
        context["chapter_position"] = chapter_position
        
        # 获取前面几章的文本内容（基于旧版本逻辑）
        recent_chapters_texts = []
        if chapter_number > 1:
            start_chap = max(1, chapter_number - recent_texts_count)
            for c in range(start_chap, chapter_number):
                chap_query = select(Chapter).where(
                    Chapter.novel_id == novel_id,
                    Chapter.chapter_number == c
                )
                result = await session.execute(chap_query)
                chapter = result.scalar_one_or_none()
                
                if chapter and chapter.content:
                    recent_chapters_texts.append(chapter.content.strip())
                else:
                    recent_chapters_texts.append("")
        
        # 生成短期摘要和节奏评估（基于旧版本逻辑）
        if recent_chapters_texts and any(text.strip() for text in recent_chapters_texts):
            rhythm_analysis = await _analyze_chapter_rhythm(recent_chapters_texts)
            context.update(rhythm_analysis)
        else:
            context.update({
                "short_summary": "",
                "next_chapter_keywords": "",
                "rhythm_score": 5,
                "rhythm_suggestion": ""
            })
        
        # 获取前章结尾段落
        if recent_chapters_texts:
            for text_block in reversed(recent_chapters_texts):
                if text_block.strip():
                    if len(text_block) > 1500:
                        context["previous_chapter_excerpt"] = text_block[-1500:]
                    else:
                        context["previous_chapter_excerpt"] = text_block
                    break
            else:
                context["previous_chapter_excerpt"] = ""
        else:
            context["previous_chapter_excerpt"] = ""
        
        # 向量检索相关上下文（基于旧版本逻辑）
        if chapter_number > 1 and context.get("short_summary") and context.get("next_chapter_keywords"):
            retrieval_query = f"{context['short_summary']} {context['next_chapter_keywords']}"
            relevant_context = await vector_service.search_relevant_context(retrieval_query, k=2)
            if relevant_context.strip():
                context["context_excerpt"] = relevant_context
            else:
                context["context_excerpt"] = "（无检索到的上下文）"
        else:
            context["context_excerpt"] = "（无检索到的上下文）"
        
        return context
        
    except Exception as e:
        logger.error(f"Failed to collect chapter context: {e}")
        return {
            "novel_id": novel_id,
            "chapter_number": chapter_number,
            "novel_setting": "",
            "global_summary": "",
            "character_state": "",
            "chapter_position": "",
            "previous_chapter_excerpt": "",
            "context_excerpt": "",
            "short_summary": "",
            "next_chapter_keywords": "",
            "rhythm_score": 5,
            "rhythm_suggestion": ""
        }


async def _analyze_chapter_rhythm(chapters_text_list: List[str]) -> Dict[str, Any]:
    """分析章节节奏 - 基于旧版本的节奏评估逻辑"""
    try:
        combined_text = "\n".join(chapters_text_list).strip()
        if not combined_text:
            return {
                "short_summary": "",
                "next_chapter_keywords": "",
                "rhythm_score": 5,
                "rhythm_suggestion": ""
            }
        
        async with AsyncSessionLocal() as session:
            prompt_service = PromptService(session)
            rhythm_prompt = await prompt_service.format_prompt(
                "summarize_recent_chapters_prompt",
                combined_text=combined_text
            )
            
            if not rhythm_prompt:
                return {
                    "short_summary": combined_text[:200] + "..." if len(combined_text) > 200 else combined_text,
                    "next_chapter_keywords": "",
                    "rhythm_score": 5,
                    "rhythm_suggestion": ""
                }
            
            response_text = await llm_service.invoke_with_retry(rhythm_prompt)
            
            # 解析响应（基于旧版本逻辑）
            short_summary = ""
            next_chapter_keywords = ""
            rhythm_score = 5  # 默认中间值
            rhythm_suggestion = ""
            
            for line in response_text.splitlines():
                line = line.strip()
                if line.startswith("短期摘要:"):
                    short_summary = line.replace("短期摘要:", "").strip()
                elif line.startswith("下一章关键字:"):
                    next_chapter_keywords = line.replace("下一章关键字:", "").strip()
                elif line.startswith("剧情节奏评分:"):
                    score_text = line.replace("剧情节奏评分:", "").strip()
                    try:
                        score_part = score_text.split("/")[0]
                        rhythm_score = int(score_part)
                        suggestion_part = score_text.split("，")
                        if len(suggestion_part) > 1:
                            rhythm_suggestion = suggestion_part[1].strip()
                    except ValueError:
                        logger.warning(f"Failed to parse rhythm score from: {score_text}")
            
            if not short_summary and not next_chapter_keywords:
                short_summary = response_text
            
            return {
                "short_summary": short_summary,
                "next_chapter_keywords": next_chapter_keywords,
                "rhythm_score": rhythm_score,
                "rhythm_suggestion": rhythm_suggestion
            }
            
    except Exception as e:
        logger.error(f"Failed to analyze chapter rhythm: {e}")
        return {
            "short_summary": "",
            "next_chapter_keywords": "",
            "rhythm_score": 5,
            "rhythm_suggestion": ""
        }


async def _generate_chapter_outline(context_info: Dict, chapter_number: int) -> str:
    """生成章节大纲 - 基于章节蓝图信息"""
    try:
        async with AsyncSessionLocal() as session:
            prompt_service = PromptService(session)
            
            # 获取章节蓝图信息（如果有的话）
            chapter_info = await _get_chapter_blueprint_info(session, context_info.get("novel_id"), chapter_number)
            
            # 获取认知状态（如果有的话）
            cognitive_state = context_info.get("cognitive_guidance", "暂无认知指导")
            
            outline_prompt = f"""基于以下小说设定，请为第{chapter_number}章生成详细大纲：

小说架构：
{context_info.get('novel_setting', '暂无架构信息')}

认知状态：
{cognitive_state}

全局摘要：
{context_info.get('global_summary', '这是开篇阶段')}

角色当前状态：
{context_info.get('character_state', '暂无角色状态')}

节奏分析：
- 短期摘要：{context_info.get('short_summary', '暂无')}
- 关键要素：{context_info.get('next_chapter_keywords', '暂无')}
- 节奏评分：{context_info.get('rhythm_score', 5)}/10
- 节奏建议：{context_info.get('rhythm_suggestion', '保持平衡')}

章节信息：
- 章节位置：{context_info.get('chapter_position', '中期')}
- 标题：{chapter_info.get('title', f'第{chapter_number}章')}
- 定位：{chapter_info.get('role', '推进剧情')}
- 作用：{chapter_info.get('purpose', '发展故事')}

请生成第{chapter_number}章的章节大纲，包含：
1. 章节主要情节点（与节奏评分匹配）
2. 角色行为和对话要点
3. 情感节奏设计（考虑当前节奏评分）
4. 与整体架构和角色状态的呼应
5. 为下一章埋下的伏笔

请用简洁明了的语言描述，字数控制在500字以内。"""

            outline_result = await llm_service.invoke_with_retry(outline_prompt)
            return outline_result
            
    except Exception as e:
        logger.error(f"Failed to generate chapter outline: {e}")
        return f"第{chapter_number}章大纲生成失败"


async def _get_chapter_blueprint_info(session: AsyncSession, novel_id: UUID, chapter_number: int) -> Dict[str, str]:
    """获取章节蓝图信息"""
    try:
        # 查找章节目录文档
        blueprint_query = select(Document).where(
            Document.novel_id == novel_id,
            Document.doc_type == DocumentType.BLUEPRINT,
            Document.title.like("%章节目录%")
        ).order_by(Document.updated_at.desc())
        
        result = await session.execute(blueprint_query)
        blueprint_doc = result.scalar_one_or_none()
        
        if blueprint_doc and blueprint_doc.content:
            # 简单解析章节信息（可以后续优化）
            lines = blueprint_doc.content.split('\n')
            for i, line in enumerate(lines):
                if f"第{chapter_number}章" in line:
                    # 提取基本信息
                    title_line = line.strip()
                    info = {"title": title_line}
                    
                    # 查找后续的详细信息
                    for j in range(i+1, min(i+10, len(lines))):
                        detail_line = lines[j].strip()
                        if detail_line.startswith("本章定位："):
                            info["role"] = detail_line.replace("本章定位：", "").strip()
                        elif detail_line.startswith("核心作用："):
                            info["purpose"] = detail_line.replace("核心作用：", "").strip()
                        elif detail_line.startswith("第") and "章" in detail_line:
                            break  # 到了下一章
                    
                    return info
        
        return {"title": f"第{chapter_number}章", "role": "推进剧情", "purpose": "发展故事"}
        
    except Exception as e:
        logger.error(f"Failed to get chapter blueprint info: {e}")
        return {"title": f"第{chapter_number}章", "role": "推进剧情", "purpose": "发展故事"}


async def _generate_chapter_content(outline: str, context_info: Dict, chapter_number: int) -> str:
    """生成章节内容 - 使用复杂的章节写作提示词"""
    try:
        async with AsyncSessionLocal() as session:
            prompt_service = PromptService(session)
            
            # 获取章节详细信息
            chapter_info = await _get_chapter_blueprint_info(session, context_info.get("novel_id"), chapter_number)
            
            # 根据节奏评分调整用户指导
            rhythm_score = context_info.get("rhythm_score", 5)
            rhythm_suggestion = context_info.get("rhythm_suggestion", "")
            
            adjusted_user_guidance = ""
            if rhythm_score <= 3:
                adjusted_user_guidance += "\n提示：当前剧情节奏偏慢，请适当加快节奏。"
            elif rhythm_score >= 8:
                adjusted_user_guidance += "\n提示：当前剧情节奏过快，请适当放缓节奏，增加细节或情感描写。"
            elif rhythm_suggestion:
                adjusted_user_guidance += f"\n提示：{rhythm_suggestion}"
            
            # 确定字数要求
            word_number = 2500  # 默认字数
            
            # 根据章节编号选择提示词模板
            if chapter_number == 1:
                prompt_template = "first_chapter_draft_prompt"
                prompt_params = {
                    "novel_number": chapter_number,
                    "chapter_title": chapter_info.get("title", f"第{chapter_number}章"),
                    "chapter_role": chapter_info.get("role", "开场引入"),
                    "chapter_purpose": chapter_info.get("purpose", "建立世界观和角色"),
                    "suspense_level": "渐进",
                    "foreshadowing": "埋设主要伏笔",
                    "plot_twist_level": "★☆☆☆☆",
                    "chapter_summary": f"第{chapter_number}章内容",
                    "characters_involved": "主要角色",
                    "key_items": "无",
                    "scene_location": "初始场景",
                    "time_constraint": "无",
                    "novel_setting": context_info.get("novel_setting", ""),
                    "word_number": word_number,
                    "user_guidance": adjusted_user_guidance
                }
            else:
                prompt_template = "next_chapter_draft_prompt"
                prompt_params = {
                    "novel_number": chapter_number,
                    "chapter_title": chapter_info.get("title", f"第{chapter_number}章"),
                    "chapter_role": chapter_info.get("role", "推进剧情"),
                    "chapter_purpose": chapter_info.get("purpose", "发展故事"),
                    "suspense_level": "渐进",
                    "foreshadowing": "继续展开",
                    "plot_twist_level": "★★☆☆☆",
                    "chapter_summary": f"第{chapter_number}章内容",
                    "characters_involved": "相关角色",
                    "key_items": "无",
                    "scene_location": "当前场景",
                    "time_constraint": "无",
                    "novel_setting": context_info.get("novel_setting", ""),
                    "global_summary": context_info.get("global_summary", ""),
                    "character_state": context_info.get("character_state", ""),
                    "context_excerpt": context_info.get("context_excerpt", ""),
                    "previous_chapter_excerpt": context_info.get("previous_chapter_excerpt", ""),
                    "word_number": word_number,
                    "user_guidance": adjusted_user_guidance
                }
            
            content_prompt = await prompt_service.format_prompt(prompt_template, **prompt_params)
            
            if not content_prompt:
                # 降级到简单模式
                content_prompt = f"""基于以下大纲，请创作第{chapter_number}章的完整内容：

章节大纲：
{outline}

小说设定：
{context_info.get('novel_setting', '暂无架构信息')[:1000]}

前章结尾：
{context_info.get('previous_chapter_excerpt', '这是开篇章节')}

创作要求：
1. 严格按照大纲展开情节
2. 保持与前面章节的一致性
3. 语言风格统一，文笔流畅
4. 字数控制在{word_number}字
5. 包含适当的对话和描写
6. 体现人物性格和情感变化

{adjusted_user_guidance}

请开始创作第{chapter_number}章："""

            content_result = await llm_service.invoke_with_retry(content_prompt)
            return content_result
            
    except Exception as e:
        logger.error(f"Failed to generate chapter content: {e}")
        return f"第{chapter_number}章内容生成失败"


async def _optimize_chapter_content(content: str, context_info: Dict) -> str:
    """优化章节内容"""
    try:
        # 简单的内容清理和优化
        optimized = content.strip()
        
        # 可以在这里添加更多优化逻辑
        return optimized
        
    except Exception as e:
        logger.error(f"Failed to optimize chapter content: {e}")
        return content


async def _update_all_summaries_and_character_state(session: AsyncSession, novel_id: UUID, chapter_number: int, new_chapter_content: str):
    """更新多级摘要和角色状态 - 基于认知科学的分层记忆模型"""
    try:
        # 使用分层摘要服务
        summary_service = HierarchicalSummaryService(session)
        
        # 更新所有层级的摘要
        await summary_service.update_all_summaries(novel_id, chapter_number, new_chapter_content)
        
        # 更新角色状态（保持原有逻辑）
        prompt_service = PromptService(session)
        
        char_state_query = select(Document).where(
            Document.novel_id == novel_id,
            Document.doc_type == DocumentType.CHARACTER_STATE
        ).order_by(Document.updated_at.desc())
        
        result = await session.execute(char_state_query)
        char_state_doc = result.scalar_one_or_none()
        
        current_character_state = char_state_doc.content if char_state_doc else ""
        
        # 更新角色状态
        if current_character_state:  # 只有存在角色状态时才更新
            char_state_prompt = await prompt_service.format_prompt(
                "update_character_state_prompt",
                chapter_text=new_chapter_content,
                old_state=current_character_state
            )
            
            if char_state_prompt:
                new_character_state = await llm_service.invoke_with_retry(char_state_prompt)
                
                char_state_doc.content = new_character_state
                from datetime import timezone
                char_state_doc.updated_at = datetime.now(timezone.utc)
        
        await session.commit()
        
    except Exception as e:
        logger.error(f"Failed to update summaries and character state: {e}")
        try:
            await session.rollback()
        except Exception as rollback_error:
            logger.error(f"Failed to rollback session: {rollback_error}")
        raise


async def _update_task_status(
    session: AsyncSession, 
    task_id: str, 
    status: TaskStatus, 
    progress: int, 
    result: dict = None, 
    error_message: str = None,
    current_stage: str = None,
    detailed_progress: dict = None,
    result_data: dict = None
):
    """更新任务状态"""
    
    from datetime import timezone

    update_data = {
        "status": status,
        "progress": progress,
        "updated_at": datetime.now(timezone.utc)
    }

    if status == TaskStatus.IN_PROGRESS and not hasattr(session, '_started_at_set'):
        update_data["started_at"] = datetime.now(timezone.utc)
        session._started_at_set = True

    if status in [TaskStatus.SUCCESS, TaskStatus.FAILED]:
        update_data["completed_at"] = datetime.now(timezone.utc)
    
    if result:
        update_data["result"] = json.dumps(result, ensure_ascii=False)
    
    if error_message:
        update_data["error_message"] = error_message
        
    # 新增详细状态字段
    if current_stage:
        update_data["current_stage"] = current_stage
        
    if detailed_progress:
        update_data["detailed_progress"] = json.dumps(detailed_progress, ensure_ascii=False)
        
    if result_data:
        update_data["result_data"] = json.dumps(result_data, ensure_ascii=False)
    
    try:
        stmt = update(GenerationTask).where(GenerationTask.task_id == task_id).values(**update_data)
        await session.execute(stmt)
        await session.commit()
    except Exception as e:
        logger.error(f"Failed to update task status: {e}")
        await session.rollback()