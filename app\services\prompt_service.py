#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提示词管理服务
基于数据库PromptTemplate模型的动态提示词管理
"""
import logging
from typing import Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.prompt_template import PromptTemplate

logger = logging.getLogger(__name__)


class PromptService:
    """提示词服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self._cache: Dict[str, str] = {}
    
    async def get_prompt_template(self, template_name: str) -> Optional[str]:
        """获取提示词模板"""
        try:
            # 先查缓存
            if template_name in self._cache:
                return self._cache[template_name]
            
            # 从数据库查询
            query = select(PromptTemplate).where(
                PromptTemplate.name == template_name,
                PromptTemplate.is_active == True
            ).order_by(PromptTemplate.version.desc())
            
            result = await self.session.execute(query)
            template = result.scalar_one_or_none()
            
            if template:
                self._cache[template_name] = template.template
                return template.template
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get prompt template {template_name}: {e}")
            return None
    
    async def format_prompt(self, template_name: str, **kwargs) -> Optional[str]:
        """格式化提示词"""
        template = await self.get_prompt_template(template_name)
        if not template:
            return None
        
        try:
            return template.format(**kwargs)
        except Exception as e:
            logger.error(f"Failed to format prompt {template_name}: {e}")
            return None
    
    async def update_template(self, template_name: str, content: str, description: str = None) -> bool:
        """更新或创建提示词模板"""
        try:
            # 查找现有模板
            query = select(PromptTemplate).where(
                PromptTemplate.name == template_name,
                PromptTemplate.is_active == True
            )
            result = await self.session.execute(query)
            existing = result.scalar_one_or_none()
            
            if existing:
                # 停用旧版本
                existing.is_active = False
                
                # 创建新版本
                new_template = PromptTemplate(
                    name=template_name,
                    template=content,
                    description=description,
                    version=existing.version + 1,
                    is_active=True
                )
            else:
                # 创建首个版本
                new_template = PromptTemplate(
                    name=template_name,
                    template=content,
                    description=description,
                    version=1,
                    is_active=True
                )
            
            self.session.add(new_template)
            await self.session.commit()
            
            # 更新缓存
            self._cache[template_name] = content
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update template {template_name}: {e}")
            await self.session.rollback()
            return False


# 预定义的提示词模板（完整迁移自旧版本prompt_definitions.py）
DEFAULT_PROMPT_TEMPLATES = {
    "core_seed_prompt": """作为专业作家，请用"雪花写作法"第一步构建故事核心：
主题：{topic}
类型：{genre}
篇幅：约{number_of_chapters}章（每章{word_number}字）

请用单句公式概括故事本质，例如：
"当[主角]遭遇[核心事件]，必须[关键行动]，否则[灾难后果]；与此同时，[隐藏的更大危机]正在发酵。"

要求：
1. 必须包含三个层次的冲突：
   - 外部冲突（与环境/他人）
   - 内部冲突（自我矛盾）
   - 哲学冲突（核心价值观挑战）
2. 体现人物核心驱动力
3. 暗示世界观关键矛盾
4. 使用25-100字精准表达
5. 包含一个强烈的情感钩子，引发读者共鸣
6. 暗示故事节奏，确保在{number_of_chapters}章内有起伏变化

仅返回故事核心文本，不要解释任何内容。""",

    "character_dynamics_prompt": """主题：{topic}
基于核心种子：
{core_seed}

请设计5-6个具有动态变化潜力的核心角色（包括主角），每个角色需包含：
特征：
- 背景、外貌、性别、年龄、职业等
- 暗藏的秘密或潜在弱点(可与世界观或其他角色有关)
- 独特的性格魅力和价值观

内在驱动与成长：
- 核心价值观与内在矛盾
- 深层渴望（情感、归属、成就等）
- 潜在的成长方向或蜕变可能

角色弧线设计（包含情感与关系）：
初始状态（含初始人际关系） → 触发事件 → 认知/情感波动 → 关键抉择/行动 → 关系变化/新认知 → 蜕变节点 → 最终状态

关系互动网络：
- 与其他核心角色的初始关系（亲疏、合作、对立等）
- 至少两对角色间的价值观冲突点
- 潜在的情感连接点或浪漫可能（强调双向互动和可能性，而非单向攻略）
- 一个重要的合作纽带或同盟
- 一个隐藏的背叛或利益冲突点

要求：
仅给出最终文本，不要解释任何内容。""",

    "world_building_prompt": """主题：{topic}
为服务核心冲突"{core_seed}"，请构建三维交织的世界观：

1. 物理维度：
- 空间结构（地理×社会阶层分布图）
- 时间轴（关键历史事件年表）
- 法则体系（物理/魔法/社会规则的漏洞点）

2. 社会维度：
- 权力结构断层线（可引发冲突的阶层/种族/组织矛盾）
- 文化禁忌（可被打破的禁忌及其后果）
- 经济命脉（资源争夺焦点）

3. 隐喻维度：
- 贯穿全书的视觉符号系统（如反复出现的意象）
- 氣候/环境变化映射的心理状态
- 建筑风格暗示的文明困境

4. 阶段性情感动力（新增）：
- 前期（约1-1/3处）：以[情绪1]为主导
- 中期（约1/3-2/3处）：过渡到[情绪2]
- 后期（约2/3-结尾）：以[情绪3]为主导

要求：
每个维度至少包含3个可与角色决策产生互动的动态元素。
至少设计3个"情感歇息点"，主要用于后期章节减缓节奏。

仅给出最终文本，不要解释任何内容。""",

    "plot_architecture_prompt": """基于以下元素构建三幕式悬念架构：
核心种子：{core_seed}
角色体系：{character_dynamics}
世界观：{world_building}

要求按以下结构设计，并严格执行节奏控制：

第一幕（触发）
- 悬念密度：★★☆☆☆
- 日常状态中的异常征兆（2处铺垫）
- 引入故事：展示主线、暗线、副线的开端，建立核心人物关系
- 关键事件：打破平衡的催化剂（需改变至少3个角色的关系）
- 主角初步行动与受挫：展现主角当前能力局限或认知偏差

第二幕（对抗）
- 悬念密度前半：★★★☆☆
- 悬念密度后半：★★★★☆
- 剧情升级：主线、副线、情感线交织推进，冲突加剧
- 双重压力：外部障碍升级+内部挣扎（含情感纠葛）
- 关键情感节点：关系进展的里程碑或重大考验
- 虚假胜利/重大挫折：看似解决实则深化危机的转折点
- 灵魂黑夜：世界观认知颠覆或核心价值观受到严峻挑战的时刻
- 设置至少3个"情感沉淀点"，用于减缓节奏、深入角色内心成长和关系梳理

第三幕（解决）
- 悬念密度：★★★☆☆（高潮后注意放缓，避免持续紧绷）
- 设置至少2个"情感沉淀章节"，专注角色最终的内心转变、关系归宿和价值确认
- 伏笔回收计划：提前分配回收任务（情节+情感呼应），每章不超过2个关键伏笔
- 代价显现：解决危机或达成目标所需付出的牺牲或代价（物质或情感）
- 最终高潮与抉择：主角综合运用成长后的能力与智慧解决核心冲突，面临关键抉择（可能影响个人命运与重要关系）
- 结局：展示主要冲突解决后的世界状态和角色归宿（包含关系结局）

每幕需明确的伏笔计划：
- 第一幕：埋设主要伏笔（标记优先级）
- 第二幕：强化伏笔、埋设细节伏笔（含情感线索）
- 第三幕：回收伏笔（按照优先级分配到不同章节，注重情节与情感的呼应）

注意事项：
故事需要出场数位性格各异、有魅力的主要配角，其中可设计几位与主角可能发展出深刻情感联结的异性角色。主角的成长和魅力是吸引他人的基础，但感情的发展需要建立在真实的互动、理解和情感共鸣之上，过程需细腻、自然。

仅给出最终文本，不要解释任何内容。""",

    "summarize_recent_chapters_prompt": """你是一名资深长篇小说编辑，请分析以下合并文本（可能包含最近几章内容）：
{combined_text}

现在请你基于目前故事的进展，完成以下三件事：
1) 用最多200字，写一个简洁明了的「当前情节短期摘要」。
2) 提炼「下一章」的关键字（例如关键物品、重要人物、地点、事件、情节等），可以用逗号分隔或条目列出。
3) 评估当前剧情节奏(1-10分)：较低分数(1-3)表示节奏缓慢需要加速，中等分数(4-7)表示节奏平衡，较高分数(8-10)表示节奏过快需要放缓。

请按如下格式输出（不需要额外解释）：
短期摘要: <这里写短期摘要>
下一章关键字: <这里写下一章关键字>
剧情节奏评分: <评分>/10，<简要建议>

请确保「下一章关键字」与「当前情节摘要」有明确的因果或延续关系，体现故事发展的自然逻辑。

在摘要中简要提及主要人物当前的情感状态或心理变化。""",

    "create_character_state_prompt": """依据当前背景：{topic}

以及角色动力学设定：{character_dynamics}

请生成一个角色状态文档，内容格式：
角色A属性：
├──物品:
    ├──物品(若有初始物品则增加，没有则为暂无)：描述
    ...
├──能力
    ├──技能1(若有初始技能则增加，没有则为暂无)：描述
    ...
├──状态
    ├──身体状态：
        ├──Buff/Debuff
    ├──心理状态：描述
    
├──主要角色间关系网
    ├──角色B：描述(初始有关联则增加，没有则为暂无关系)
    ├──角色C：描述(初始有关联则增加，没有则为暂无关系)
    ...
├──触发或加深的事件
    ├──暂无事件
    ...
├──角色发展阶段（新增）
    ├──当前阶段：[初始状态/觉醒期/冲突期/转变期/最终形态]
    ├──下一发展关键点：[描述角色需要经历什么才能进入下一阶段]

角色B属性：
├──物品
    ├──...
├──能力
    ├──...
├──状态
    ├──...
├──主要角色间关系网
    ├──...
├──触发或加深的事件
    ├──...
├──角色发展阶段
    ├──...

角色C属性：
......

新出场角色：
- (此处填写未来任何新增角色或临时出场人物的基本信息)

要求：
仅返回编写好的角色状态文本，不要解释任何内容。""",

    # =============== 章节目录生成（悬念节奏曲线）===================
    "chapter_blueprint_prompt": """根据小说架构：\n
{novel_architecture}

设计{number_of_chapters}章的节奏分布，引入剧情节奏动态调控：

一、整体节奏控制：
- 前期（1-{front_chapter_end}章）：悬念密度上限★★★☆☆，重点建立世界观、角色关系
- 中期（{front_chapter_end_plus_1}-{middle_chapter_end}章）：悬念密度上限★★★★☆，重点推进冲突、深化矛盾
- 后期（{middle_chapter_end_plus_1}-{number_of_chapters}章）：设置"镜像节奏"，高潮过后必须安排1-2章"情感沉淀"，最终高潮不超过★★★★☆

二、章节集群划分：
- 每3-5章构成一个悬念单元，必须包含至少1章"缓冲章节"
- 后期（最后1/3章节）每出现一个高强度转折(★★★★及以上)后，必须安排一个情感消化章节
- 认知颠覆总配额：前期最多2次，中期最多3次，后期最多2次

三、伏笔管理：
- 伏笔埋设：集中在前半部分
- 伏笔强化：分散在中期章节
- 伏笔回收：分配到后期多个章节，最后5章的伏笔回收总量不超过总伏笔的40%

四、每章需明确：
- 章节定位（角色成长/事件推进/主题深化/关系发展/情感互动等）
- 核心作用（推进主线/揭示信息/转折/考验关系/深化情感/塑造人物等）
- 核心悬念类型（信息差/道德困境/时间压力/情感抉择等）
- 伏笔操作（埋设/强化/回收 - 情节或情感线索）
- 认知颠覆强度（1-5级，后期章节基准值-1）
- 是否为"情感沉淀章节"（是/否 - 用于内心戏、关系梳理、情感升温等）
- 关键道具（无/具体道具名称）

输出格式示例：
第n章 - [标题]
本章定位：[角色成长/事件推进/关系发展/...]
核心作用：[推进主线/考验关系/深化情感/...]
悬念密度：[紧凑/渐进/爆发/...]
伏笔操作：埋设(A线索)→强化(B矛盾)...
认知颠覆：★☆☆☆☆
情感沉淀：是/否
关键道具：[无/...]
本章简述：[一句话概括]

要求：
- 使用精炼语言描述，每章字数控制在100字以内。
- 合理安排节奏，确保整体悬念曲线的连贯性与张弛有度。
- 在生成{number_of_chapters}章前不要出现结局章节。
- {number_of_chapters}章为结局章节。
- 确保至少20%的章节标记为"情感沉淀：是"，主要集中在中后期。
- 设计自然的情感互动或关系进展的契机，避免生硬的"发糖"或"狗血"。

仅给出最终文本，不要解释任何内容。""",

    # =============== 全局摘要更新 ===================
    "summary_prompt": """以下是新完成的章节文本：
{chapter_text}

这是当前的全局摘要（可为空）：
{global_summary}

请根据本章新增内容，更新全局摘要。
要求：
- 保留既有重要信息，同时融入新剧情要点
- 以简洁、连贯的语言描述全书进展
- 客观描绘，不展开联想或解释
- 字数控制在2000字以内

仅返回全局摘要文本，不要解释任何内容。""",

    # =============== 角色状态更新 ===================
    "update_character_state_prompt": """以下是新完成的章节文本：
{chapter_text}

这是当前的角色状态文档：
{old_state}

请更新主要角色状态，内容格式：
角色A属性：
├──物品:
    ├──某物(道具)：描述
    ├──XX长剑(武器)：描述
    ...
├──能力
    ├──技能1：描述
    ├──技能2：描述
    ...
├──状态
    ├──身体状态：
        ├──Buff/Debuff
    ├──心理状态：描述（需体现当前情绪、压力、动机变化）
    
├──主要角色间关系网
    ├──角色B：描述（关系性质、信任度、当前主要矛盾或连接点）
    	├──关系状态：[陌生/初识/友谊/好感萌芽/暧昧升温/确定关系/情感挑战/稳定发展/关系破裂/...] (根据实际情况选择或自定义)
    ├──角色C：描述
    	├──关系状态：[...]
    ...
├──触发或加深的事件
    ├──事件1：简述事件及其对角色的影响（能力、认知、情感、关系等）
    ├──下一发展关键点：[描述角色需要经历什么才能进入下一阶段]
    ├──情感节奏指数(1-10)：[数字+简要说明；评估角色当前情感强度，高分表示情感紧张、波动大或处于关键节点]
    ├──情感张力指数(1-10): [数字+简要说明；评估角色与特定其他角色之间的情感氛围强度，高分表示张力强，可能暧昧或冲突]

角色B属性：
├──物品
    ├──...
├──能力
    ├──...
├──状态
    ├──...
├──主要角色间关系网
    ├──...
├──触发或加深的事件
    ├──...
├──角色发展阶段
    ├──...

角色C属性：
......

新出场角色：
- 任何新增角色或临时出场人物的基本信息，简要描述即可，不要展开，淡出视线的角色可删除。

伏笔回收进度：
- 已埋设伏笔：[列出所有已埋设但尚未回收的伏笔]
- 已回收伏笔：[列出已经回收的伏笔]
- 待回收优先级：[标注哪些伏笔应优先在后续章节中回收]

要求：
- 请直接在已有文档基础上进行增删
- 不改变原有结构，语言尽量简洁、有条理

仅返回更新后的角色状态文本，不要解释任何内容。""",

    # =============== 第一章草稿提示 ===================
    "first_chapter_draft_prompt": """即将创作：第 {novel_number} 章《{chapter_title}》
本章定位：{chapter_role}
核心作用：{chapter_purpose}
悬念密度：{suspense_level}
伏笔操作：{foreshadowing} 
认知颠覆：{plot_twist_level}
本章简述：{chapter_summary}

核心要求：文笔细腻生动，情节引人入胜，注重氛围营造和真实情感表达。叙事应有张力，避免平铺直叙和流水账。

可用元素：
- 核心人物(可能未指定)：{characters_involved}
- 关键道具(可能未指定)：{key_items}
- 空间坐标(可能未指定)：{scene_location}
- 时间压力(可能未指定)：{time_constraint}

参考文档：
- 小说设定：
{novel_setting}

请完成第 {novel_number} 章的正文，字数要求{word_number}字，至少设计下方2个或以上具有动态张力的场景，并注重以下方面：
1. 对话场景：
   - 潜台词冲突（表面谈论A，实际博弈B，或隐藏情感）
   - 通过对话展现角色性格、智慧、立场和关系张力
   - 至少1处运用语言技巧（如双关、反讽、隐喻）暗示深层含义或未来发展
   - 当涉及情感互动时，对话应注重潜台词、语气、语速的变化，以及对话间的沉默或停顿所传递的情感信息。

2. 动作/行为场景：
   - 环境交互细节（至少3个感官描写，突出氛围）
   - 节奏控制（短句与长句结合，营造紧张或舒缓感）
   - 通过动作或行为细节揭示人物性格、能力或隐藏情绪

3. 心理场景：
   - 深入挖掘角色内心世界，展现细腻的情感变化、动机、挣扎或认知失调
   - 善用内心独白、自由间接引语、意识流等手法
   - 将内心活动与外部事件或环境巧妙结合
   - 在浪漫场景中，心理描写应更侧重于情感的涟漪、内在的挣扎与渴望、对对方言行的解读等。

4. 环境/氛围场景：
   - 空间描写需服务于氛围营造和人物心境（如压抑、神秘、温馨、紧张）
   - 非常规感官组合或意象运用，增强文学性
   - 动态环境变化可反映心理或预示情节

5. 互动/情感场景 (新增)：
   - 设计能体现角色间化学反应、情感靠近、试探、冲突或疏远的互动细节
   - 强调 "Show, don't tell"，通过眼神、微表情、肢体语言、共同经历等展现情感流动
   - 若涉及情感升温，应自然、循序渐进，符合人物性格和当前情境

特别说明：
- 深化"爽点"的层次与内涵，避免肤浅的套路
- 如果本章标记为"情感沉淀：是"，请着重描写角色内心转变、关系深化/梳理或价值观碰撞，放缓外部事件推进速度

文末设置一个"钩链转折"：结尾时回收旧悬念/创造新悬念/抛出新危机/颠覆某个认知/神转折/关系上的重大变化或情感上的强烈冲击等，激发读者追更欲望。

格式要求：
- 仅返回章节正文文本；
- 不使用分章节小标题；
- 不要使用markdown格式；
- 避免使用过于网络化、缺乏文学性的词语，追求有质感的叙事风格。
- 不要在正文中注释伏笔操作、认知颠覆、情感沉淀等。

额外指导(可能未指定)：{user_guidance}""",

    # =============== 后续章节草稿提示 ===================
    "next_chapter_draft_prompt": """核心要求：文笔细腻生动，情节引人入胜，注重氛围营造和真实情感表达。叙事应有张力，避免平铺直叙和流水账。

参考文档：
- 小说设定：
{novel_setting}

- 全局摘要：
{global_summary}

- 角色状态：
{character_state}

本地知识库检索到的片段：
{context_excerpt}

即将创作：第 {novel_number} 章《{chapter_title}》
本章定位：{chapter_role}
核心作用：{chapter_purpose}
悬念密度：{suspense_level}
伏笔操作：{foreshadowing}
认知颠覆：{plot_twist_level}
本章简述：{chapter_summary}

可用元素：
- 核心人物(可能未指定)：{characters_involved}
- 关键道具(可能未指定)：{key_items}
- 空间坐标(可能未指定)：{scene_location}
- 时间压力(可能未指定)：{time_constraint}

前章结尾段：
{previous_chapter_excerpt}

依据前章结尾剧情，开始完成第 {novel_number} 章的正文，字数要求{word_number}字，确保与前章结尾衔接流畅，

本章至少设计下方2个或以上具有动态张力的场景，并注重以下方面：
1. 对话场景：
   - 潜台词冲突（表面谈论A，实际博弈B，或隐藏情感）
   - 通过对话展现角色性格、智慧、立场和关系张力
   - 至少1处运用语言技巧（如双关、反讽、隐喻）暗示深层含义或未来发展
   - 当涉及情感互动时，对话应注重潜台词、语气、语速的变化，以及对话间的沉默或停顿所传递的情感信息。

2. 动作/行为场景：
   - 环境交互细节（至少3个感官描写，突出氛围）
   - 节奏控制（短句与长句结合，营造紧张或舒缓感）
   - 通过动作或行为细节揭示人物性格、能力或隐藏情绪

3. 心理场景：
   - 深入挖掘角色内心世界，展现细腻的情感变化、动机、挣扎或认知失调
   - 善用内心独白、自由间接引语、意识流等手法
   - 将内心活动与外部事件或环境巧妙结合
   - 在浪漫场景中，心理描写应更侧重于情感的涟漪、内在的挣扎与渴望、对对方言行的解读等。

4. 环境/氛围场景：
   - 空间描写需服务于氛围营造和人物心境（如压抑、神秘、温馨、紧张）
   - 非常规感官组合或意象运用，增强文学性
   - 动态环境变化可反映心理或预示情节

5. 互动/情感场景 (新增)：
   - 设计能体现角色间化学反应、情感靠近、试探、冲突或疏远的互动细节
   - 强调 "Show, don't tell"，通过眼神、微表情、肢体语言、共同经历等展现情感流动
   - 若涉及情感升温，应自然、循序渐进，符合人物性格和当前情境

特别说明：
- 深化"爽点"的层次与内涵，避免肤浅的套路
- 如果本章标记为"情感沉淀：是"，请着重描写角色内心转变、关系深化/梳理或价值观碰撞，放缓外部事件推进速度，增加心理和情感描写的比重。
- 如果本章认知颠覆为★★★★或以上，确保在章节结尾前留出足够篇幅让角色和读者消化这一转折带来的冲击。
- 检查全局摘要和角色状态中有哪些未解决的剧情线或情感线，考虑在本章中如何推进或与之呼应，避免情节断裂或情感突兀。

文末设置一个"钩链转折"：结尾时回收旧悬念/创造新悬念/抛出新危机/颠覆某个认知/神转折/关系上的重大变化或情感上的强烈冲击等，激发读者追更欲望。
注意：如果后续章节标记为"情感沉淀：是"，那么本章结尾的钩链转折应更侧重于情感、关系层面或内心悬念，而非单纯的外部事件高潮。

格式要求：
- 仅返回章节正文文本；
- 不使用分章节小标题；
- 不要使用markdown格式；
- 避免使用过于网络化、缺乏文学性的词语，追求有质感的叙事风格。
- 不要在正文中注释伏笔操作、认知颠覆、情感沉淀等。

注意：返回的是纯正文文本，不要包含任何解释、说明、备注等。

额外指导(可能未指定)：{user_guidance}""",

    # =============== 摘要与下一章关键字提炼 ===================
    "summarize_recent_chapters_prompt": """你是一名资深长篇小说编辑，请分析以下合并文本（可能包含最近几章内容）：
{combined_text}

现在请你基于目前故事的进展，完成以下三件事：
1) 用最多200字，写一个简洁明了的「当前情节短期摘要」。
2) 提炼「下一章」的关键字（例如关键物品、重要人物、地点、事件、情节等），可以用逗号分隔或条目列出。
3) 评估当前剧情节奏(1-10分)：较低分数(1-3)表示节奏缓慢需要加速，中等分数(4-7)表示节奏平衡，较高分数(8-10)表示节奏过快需要放缓。

请按如下格式输出（不需要额外解释）：
短期摘要: <这里写短期摘要>
下一章关键字: <这里写下一章关键字>
剧情节奏评分: <评分>/10，<简要建议>

请确保「下一章关键字」与「当前情节摘要」有明确的因果或延续关系，体现故事发展的自然逻辑。

在摘要中简要提及主要人物当前的情感状态或心理变化。""",

    # =============== 多级摘要管理 ===================
    "immediate_summary_prompt": """请为以下最近章节生成详细的即时摘要：

章节内容：
{recent_chapters_content}

章节范围：{chapter_range}

要求：
1. 详细记录每章的关键情节点和转折
2. 跟踪角色状态和情感变化的细节
3. 标注重要对话和行动的具体内容
4. 记录所有伏笔和线索的埋设与回收
5. 追踪角色关系的微妙变化
6. 字数控制在800字以内

请按章节分别总结，格式：
第X章：[详细摘要，包含具体情节、对话要点、角色状态变化]

注意：这是即时摘要，需要保留足够的细节供后续章节生成参考。""",

    "medium_summary_prompt": """基于以下详细的即时摘要，生成中等详细度的中期摘要：

即时摘要内容：
{immediate_summaries}

要求：
1. 概括主要情节发展脉络
2. 总结角色关系变化
3. 梳理重要冲突和转折点
4. 保留关键伏笔和线索
5. 字数控制在1200字以内

请生成连贯的中期发展摘要：""",

    "global_summary_prompt": """基于当前全局摘要和最新章节信息，更新全局摘要：

当前全局摘要：
{current_global_summary}

最新发展信息：
{latest_developments}

要求：
1. 保持全书发展的宏观视角
2. 突出主要故事线和角色弧线
3. 记录重大转折和高潮点
4. 维护整体叙事逻辑
5. 字数控制在1500字以内

请生成更新后的全局摘要：""",

    "character_development_summary_prompt": """基于当前角色发展摘要和新章节内容，更新角色发展摘要：

当前角色发展摘要：
{current_character_summary}

新章节内容：
{new_chapter_content}

要求：
1. 跟踪主要角色的成长轨迹
2. 记录角色关系的重要变化
3. 总结角色的关键决策和转变
4. 追踪角色的情感发展
5. 字数控制在1000字以内

请更新角色发展摘要："""
}


async def initialize_default_prompts(session: AsyncSession):
    """初始化默认提示词模板"""
    try:
        prompt_service = PromptService(session)
        
        initialized_count = 0
        existing_count = 0
        failed_count = 0
        
        for name, template in DEFAULT_PROMPT_TEMPLATES.items():
            try:
                existing = await prompt_service.get_prompt_template(name)
                if not existing:
                    success = await prompt_service.update_template(
                        template_name=name,
                        content=template,
                        description=f"默认{name}模板"
                    )
                    if success:
                        initialized_count += 1
                        logger.info(f"Initialized default prompt template: {name}")
                    else:
                        failed_count += 1
                        logger.warning(f"Failed to initialize prompt template: {name}")
                else:
                    existing_count += 1
                    logger.debug(f"Prompt template already exists: {name}")
            except Exception as template_error:
                failed_count += 1
                logger.error(f"Error initializing template {name}: {template_error}")
                continue
        
        total_available = initialized_count + existing_count
        total_expected = len(DEFAULT_PROMPT_TEMPLATES)
        
        logger.info(f"Prompt template status: {initialized_count} new, {existing_count} existing, {failed_count} failed")
        logger.info(f"Total available templates: {total_available}/{total_expected}")
        
        # 只要大部分模板可用就认为成功
        return total_available >= (total_expected * 0.8)  # 至少80%的模板可用
        
    except Exception as e:
        logger.error(f"Failed to initialize default prompts: {e}")
        return False