#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置管理模块
"""
import os
from typing import List, Optional, Union
from pydantic import field_validator, model_validator, AnyHttpUrl, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    PROJECT_NAME: str = "AI Novel Generator"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 环境配置
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 服务器配置
    SERVER_HOST: str = "0.0.0.0"
    SERVER_PORT: int = 8000
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # json 或 text
    
    # CORS配置
    BACKEND_CORS_ORIGINS: Union[str, List[str]] = []
    TRUSTED_HOSTS: Union[str, List[str]] = ["localhost", "127.0.0.1", "*"]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode='before')
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and v.strip():
            if v.startswith("["):
                # JSON格式
                import json
                return json.loads(v)
            else:
                # 逗号分隔格式
                return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        return []
    
    @field_validator("TRUSTED_HOSTS", mode='before')
    @classmethod
    def assemble_trusted_hosts(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and v.strip():
            if v.startswith("["):
                # JSON格式
                import json
                return json.loads(v)
            else:
                # 逗号分隔格式
                return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        return ["localhost", "127.0.0.1", "*"]
    
    # 数据库配置
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str = "novel_user"
    POSTGRES_PASSWORD: str = "novel_password"
    POSTGRES_DB: str = "ai_novel_generator"
    
    DATABASE_URL: Optional[str] = None
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    REDIS_URL: Optional[str] = None
    
    # Celery配置
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    
    @model_validator(mode='after')
    def assemble_connection_urls(self) -> 'Settings':
        # 组装数据库URL
        if self.DATABASE_URL is None:
            self.DATABASE_URL = (
                f"postgresql+asyncpg://"
                f"{self.POSTGRES_USER}:"
                f"{self.POSTGRES_PASSWORD}@"
                f"{self.POSTGRES_HOST}:"
                f"{self.POSTGRES_PORT}/"
                f"{self.POSTGRES_DB}"
            )
        
        # 组装Redis URL
        if self.REDIS_URL is None:
            auth_part = ""
            if self.REDIS_PASSWORD:
                auth_part = f":{self.REDIS_PASSWORD}@"
            
            self.REDIS_URL = (
                f"redis://{auth_part}"
                f"{self.REDIS_HOST}:"
                f"{self.REDIS_PORT}/"
                f"{self.REDIS_DB}"
            )
        
        # 组装Celery配置
        if self.CELERY_BROKER_URL is None:
            self.CELERY_BROKER_URL = self.REDIS_URL or "redis://localhost:6379/0"
        
        if self.CELERY_RESULT_BACKEND is None:
            self.CELERY_RESULT_BACKEND = self.REDIS_URL or "redis://localhost:6379/0"
        
        return self
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8天
    
    # 微信小程序配置
    WECHAT_APP_ID: str = ""
    WECHAT_APP_SECRET: str = ""
    
    # 文件存储配置
    UPLOAD_PATH: str = "./uploads"
    MAX_UPLOAD_SIZE: int = 50 * 1024 * 1024  # 50MB
    
    # 向量数据库配置
    VECTOR_DB_TYPE: str = "chroma"  # chroma, pinecone, weaviate
    CHROMA_PERSIST_DIRECTORY: str = "./chroma_db"
    
    # Pinecone配置（如果使用）
    PINECONE_API_KEY: Optional[str] = None
    PINECONE_ENVIRONMENT: Optional[str] = None
    PINECONE_INDEX_NAME: Optional[str] = None
    
    # LLM服务配置
    LLM_PROVIDER: str = "openai"  # openai, azure, dashscope
    LLM_API_KEY: Optional[str] = None
    LLM_BASE_URL: str = "https://api.openai.com/v1"
    LLM_MODEL: str = "gpt-3.5-turbo"
    LLM_TEMPERATURE: float = 0.7
    LLM_MAX_TOKENS: int = 2048
    LLM_TIMEOUT: int = 600
    
    # Azure OpenAI特殊配置
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    AZURE_OPENAI_API_VERSION: str = "2024-02-15-preview"
    
    # 通义千问特殊配置
    DASHSCOPE_API_KEY: Optional[str] = None
    
    # 日志文件配置
    LOG_FILE: str = "./logs/app.log"
    
    # 任务配置
    TASK_TIMEOUT: int = 3600  # 任务超时时间（秒）
    MAX_CONCURRENT_TASKS: int = 10  # 最大并发任务数
    
    # 时区配置
    TIMEZONE: str = "UTC"
    
    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=True
    )


settings = Settings()


# Celery应用配置
class CeleryConfig:
    """Celery配置类"""
    broker_url = settings.CELERY_BROKER_URL
    result_backend = settings.CELERY_RESULT_BACKEND
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    timezone = 'UTC'
    enable_utc = True
    task_track_started = True
    task_time_limit = settings.TASK_TIMEOUT
    task_soft_time_limit = settings.TASK_TIMEOUT - 60
    worker_prefetch_multiplier = 1
    task_acks_late = True
    worker_max_tasks_per_child = 1000


celery_config = CeleryConfig() 