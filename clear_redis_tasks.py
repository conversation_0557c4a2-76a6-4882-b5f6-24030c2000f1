#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理Redis中的遗留Celery任务
"""
import redis
import json


def clear_celery_tasks():
    """清理Celery任务队列"""
    print("🧹 清理Redis中的Celery任务...")
    
    try:
        # 连接Redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis连接成功")
        
        # 获取所有键
        all_keys = r.keys('*')
        print(f"📊 Redis中共有 {len(all_keys)} 个键")
        
        # 清理Celery相关的键
        celery_keys = []
        for key in all_keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else str(key)
            if any(pattern in key_str.lower() for pattern in [
                'celery', 'default', 'generation', 'task', 'result', 'unacked'
            ]):
                celery_keys.append(key)
        
        print(f"🎯 发现 {len(celery_keys)} 个Celery相关键:")
        for key in celery_keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else str(key)
            print(f"  - {key_str}")
        
        if celery_keys:
            # 询问是否清理
            print(f"\n⚠️ 将要删除 {len(celery_keys)} 个键")
            confirm = input("确认清理？(y/N): ").strip().lower()
            
            if confirm == 'y':
                # 删除键
                deleted_count = r.delete(*celery_keys)
                print(f"✅ 成功删除 {deleted_count} 个键")
                
                # 验证清理结果
                remaining_keys = r.keys('*celery*')
                if not remaining_keys:
                    print("🎉 所有Celery任务已清理完成")
                else:
                    print(f"⚠️ 还有 {len(remaining_keys)} 个相关键未清理")
            else:
                print("❌ 用户取消清理操作")
        else:
            print("✅ 没有发现需要清理的Celery任务")
            
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False


def show_redis_info():
    """显示Redis信息"""
    print("\n📊 Redis状态信息:")
    
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        info = r.info()
        
        print(f"  - Redis版本: {info.get('redis_version', 'N/A')}")
        print(f"  - 连接数: {info.get('connected_clients', 'N/A')}")
        print(f"  - 使用内存: {info.get('used_memory_human', 'N/A')}")
        print(f"  - 键总数: {info.get('db0', {}).get('keys', 0) if 'db0' in info else 0}")
        
        # 显示队列信息
        queues = ['default', 'generation']
        for queue in queues:
            length = r.llen(queue)
            print(f"  - 队列 '{queue}': {length} 个任务")
            
    except Exception as e:
        print(f"❌ 获取Redis信息失败: {e}")


def clear_specific_queues():
    """清理特定队列"""
    print("\n🎯 清理特定任务队列...")
    
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        queues = ['default', 'generation']
        total_cleared = 0
        
        for queue in queues:
            length = r.llen(queue)
            if length > 0:
                print(f"📋 队列 '{queue}' 有 {length} 个任务")
                
                # 清空队列
                r.delete(queue)
                print(f"✅ 清空队列 '{queue}'")
                total_cleared += length
            else:
                print(f"✅ 队列 '{queue}' 已经是空的")
        
        if total_cleared > 0:
            print(f"🎉 总共清理了 {total_cleared} 个任务")
        else:
            print("✅ 所有队列都是空的")
            
        return True
        
    except Exception as e:
        print(f"❌ 清理队列失败: {e}")
        return False


def main():
    """主函数"""
    print("🧹 AI小说生成器 - Redis任务清理工具")
    print("=" * 50)
    
    # 显示Redis信息
    show_redis_info()
    
    print("\n请选择操作:")
    print("1. 清理所有Celery相关键")
    print("2. 只清理任务队列")
    print("3. 只查看信息，不清理")
    print("0. 退出")
    
    choice = input("\n请输入选项 (0-3): ").strip()
    
    if choice == '1':
        clear_celery_tasks()
    elif choice == '2':
        clear_specific_queues()
    elif choice == '3':
        print("✅ 仅查看信息，未进行清理")
    elif choice == '0':
        print("👋 退出")
    else:
        print("❌ 无效选项")
    
    print("\n" + "=" * 50)
    print("🔧 建议：清理后重新启动Celery Worker")


if __name__ == "__main__":
    main()
