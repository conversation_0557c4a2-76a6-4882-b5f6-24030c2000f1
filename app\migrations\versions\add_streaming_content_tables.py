"""Add streaming content tables for realtime streaming

Revision ID: add_streaming_content
Revises: convert_integer_ids_to_uuid
Create Date: 2025-06-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_streaming_content'
down_revision = 'convert_integer_ids_to_uuid'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add streaming content tables"""
    
    # Create streaming_contents table
    op.create_table('streaming_contents',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('task_id', sa.String(length=255), nullable=False),
        sa.Column('stage', sa.String(length=100), nullable=False),
        sa.Column('chunk_index', sa.Integer(), nullable=False),
        sa.Column('chunk_content', sa.Text(), nullable=False),
        sa.Column('accumulated_content', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('total_length', sa.Integer(), nullable=True),
        sa.Column('is_final', sa.Boolean(), nullable=True),
        sa.Column('metadata', sa.Text(), nullable=True),
        sa.Column('checksum', sa.String(length=64), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['task_id'], ['generation_tasks.task_id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_streaming_contents_id'), 'streaming_contents', ['id'], unique=False)
    op.create_index(op.f('ix_streaming_contents_stage'), 'streaming_contents', ['stage'], unique=False)
    op.create_index(op.f('ix_streaming_contents_task_id'), 'streaming_contents', ['task_id'], unique=False)
    
    # Create content_recovery_points table
    op.create_table('content_recovery_points',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('task_id', sa.String(length=255), nullable=False),
        sa.Column('stage', sa.String(length=100), nullable=False),
        sa.Column('last_chunk_index', sa.Integer(), nullable=False),
        sa.Column('accumulated_content', sa.Text(), nullable=False),
        sa.Column('total_length', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('recovery_count', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_content_recovery_points_id'), 'content_recovery_points', ['id'], unique=False)
    op.create_index(op.f('ix_content_recovery_points_task_id'), 'content_recovery_points', ['task_id'], unique=False)
    
    # Create streaming_metrics table
    op.create_table('streaming_metrics',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('task_id', sa.String(length=255), nullable=False),
        sa.Column('stage', sa.String(length=100), nullable=False),
        sa.Column('total_chunks', sa.Integer(), nullable=False),
        sa.Column('total_bytes', sa.Integer(), nullable=False),
        sa.Column('streaming_duration', sa.Float(), nullable=False),
        sa.Column('average_chunk_size', sa.Float(), nullable=False),
        sa.Column('chunks_per_second', sa.Float(), nullable=False),
        sa.Column('error_count', sa.Integer(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True),
        sa.Column('recovery_count', sa.Integer(), nullable=True),
        sa.Column('completion_rate', sa.Float(), nullable=True),
        sa.Column('data_integrity_score', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_streaming_metrics_id'), 'streaming_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_streaming_metrics_task_id'), 'streaming_metrics', ['task_id'], unique=False)


def downgrade() -> None:
    """Remove streaming content tables"""
    
    # Drop indexes first
    op.drop_index(op.f('ix_streaming_metrics_task_id'), table_name='streaming_metrics')
    op.drop_index(op.f('ix_streaming_metrics_id'), table_name='streaming_metrics')
    op.drop_index(op.f('ix_content_recovery_points_task_id'), table_name='content_recovery_points')
    op.drop_index(op.f('ix_content_recovery_points_id'), table_name='content_recovery_points')
    op.drop_index(op.f('ix_streaming_contents_task_id'), table_name='streaming_contents')
    op.drop_index(op.f('ix_streaming_contents_stage'), table_name='streaming_contents')
    op.drop_index(op.f('ix_streaming_contents_id'), table_name='streaming_contents')
    
    # Drop tables
    op.drop_table('streaming_metrics')
    op.drop_table('content_recovery_points')
    op.drop_table('streaming_contents')
