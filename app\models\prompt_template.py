#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提示词模板模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.core.database import Base


class PromptTemplate(Base):
    """提示词模板表"""
    __tablename__ = "prompt_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # 模板基本信息
    name = Column(String(100), unique=True, nullable=False, index=True, comment="模板名称")
    template = Column(Text, nullable=False, comment="模板内容")
    description = Column(Text, nullable=True, comment="模板描述")
    
    # 版本控制
    version = Column(Integer, default=1, comment="版本号")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    def __repr__(self):
        return f"<PromptTemplate(id={self.id}, name={self.name}, version={self.version})>"