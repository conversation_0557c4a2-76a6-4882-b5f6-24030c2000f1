#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 完整生成流程测试脚本
模拟前端完整操作流程，测试后端API的所有核心功能
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any
import aiohttp
import websockets
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


@dataclass
class TestConfig:
    """测试配置"""
    base_url: str = "http://localhost:8000"
    ws_url: str = "ws://localhost:8000"
    api_prefix: str = "/api/v1"
    # 模拟微信登录的测试参数
    test_wx_code: str = "test_code_123456"
    test_nickname: str = "AI小说测试用户"
    test_avatar: str = "https://example.com/avatar.jpg"
    # 测试小说参数
    novel_title: str = "AI科幻测试小说"
    novel_theme: str = "2099年，人工智能与人类共存的未来世界，一个程序员发现了改变世界的秘密"
    novel_genre: str = "science_fiction"
    target_length: int = 10000
    chapter_count: int = 3


class NovelGenerationTester:
    """小说生成测试器"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.user_info: Optional[Dict] = None
        self.novel_id: Optional[int] = None
        self.generated_tasks: Dict[str, Dict] = {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=300)  # 5分钟超时
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        return headers
    
    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.config.base_url}{self.config.api_prefix}{endpoint}"
        headers = self._get_headers()
        
        self._log(f"🌐 {method} {url}")
        if data:
            self._log(f"📤 请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        async with self.session.request(method, url, headers=headers, json=data) as response:
            response_data = await response.json()
            
            if response.status >= 400:
                self._log(f"❌ 请求失败 [{response.status}]: {response_data}")
                raise Exception(f"API请求失败: {response_data}")
            
            self._log(f"✅ 响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            return response_data
    
    def _log(self, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    async def reset_quota(self) -> bool:
        """重置用户配额 - 通过删除已有小说来释放配额"""
        try:
            # 获取用户的小说列表
            novels_response = await self._make_request("GET", "/novels/?limit=100")
            
            novels = []
            if "success" in novels_response and novels_response["success"]:
                novels = novels_response["data"]["novels"]
            elif "novels" in novels_response:
                novels = novels_response["novels"]
            elif isinstance(novels_response, list):
                novels = novels_response
            
            if novels:
                self._log(f"🗑️ 找到 {len(novels)} 个小说，开始删除以释放配额...")
                deleted_count = 0
                
                for novel in novels:
                    try:
                        await self._make_request("DELETE", f"/novels/{novel['id']}")
                        deleted_count += 1
                        self._log(f"🗑️ 已删除小说: {novel.get('title', '未命名')} (ID: {novel['id']})")
                    except Exception as e:
                        self._log(f"⚠️ 删除小说 {novel['id']} 失败: {str(e)}")
                
                if deleted_count > 0:
                    self._log(f"✅ 成功删除 {deleted_count} 个小说，配额已释放")
                    # 重新获取用户信息来确认配额重置
                    user_response = await self._make_request("GET", "/auth/me")
                    if "quota_used" in user_response:
                        self.user_info = user_response
                        self._log(f"📊 更新后配额: {self.user_info['quota_used']}/{self.user_info['quota_limit']}")
                    return True
                else:
                    return False
            else:
                # 没有小说可删除，但配额已满，可能需要手动处理
                self._log("⚠️ 没有找到可删除的小说，但配额已满")
                self._log("💡 提示: 可能需要手动重置数据库中的配额字段")
                
                # 尝试直接通过用户资料更新 (这需要后端支持直接修改配额)
                return await self.try_direct_quota_reset()
                
        except Exception as e:
            self._log(f"❌ 重置配额异常: {str(e)}")
            return False
    
    async def check_quota_status(self) -> Dict[str, Any]:
        """检查当前配额状态"""
        try:
            response = await self._make_request("GET", "/users/quota")
            
            if "success" in response and response["success"]:
                return response["data"]
            elif "quota_used" in response:
                return response
            else:
                return {"quota_used": 0, "quota_limit": 10, "quota_remaining": 10}
                
        except Exception as e:
            self._log(f"❌ 检查配额状态异常: {str(e)}")
            return {"quota_used": 0, "quota_limit": 10, "quota_remaining": 10}
    
    async def try_direct_quota_reset(self) -> bool:
        """尝试直接重置配额 (如果后端支持的话)"""
        try:
            # 尝试通过更新用户资料来重置配额
            # 注意：这需要后端API支持修改quota_used字段
            reset_data = {
                "quota_used": 0  # 尝试重置为0
            }
            
            # 先尝试通过用户资料更新接口
            response = await self._make_request("PUT", "/users/profile", reset_data)
            
            # 检查是否成功
            user_response = await self._make_request("GET", "/auth/me")
            if "quota_used" in user_response and user_response["quota_used"] == 0:
                self.user_info = user_response
                self._log(f"✅ 直接配额重置成功: {self.user_info['quota_used']}/{self.user_info['quota_limit']}")
                return True
            else:
                self._log("❌ 直接配额重置失败，后端可能不支持此操作")
                return False
                
        except Exception as e:
            self._log(f"❌ 直接配额重置异常: {str(e)}")
            return False
    
    async def step1_user_login(self) -> bool:
        """步骤1: 用户登录"""
        self._log("🔐 步骤1: 开始用户登录...")
        
        try:
            login_data = {
                "code": self.config.test_wx_code,
                "nickname": self.config.test_nickname,
                "avatar_url": self.config.test_avatar
            }
            
            # 使用测试登录端点避免微信API验证
            response = await self._make_request("POST", "/auth/test/login", login_data)
            
            # 测试端点直接返回TokenResponse格式，不包装在success/data中
            if "access_token" in response:
                self.access_token = response["access_token"]
                self.user_info = response["user"]
                
                self._log(f"✅ 登录成功! 用户ID: {self.user_info['id']}")
                self._log(f"📊 用户配额: {self.user_info['quota_used']}/{self.user_info['quota_limit']}")
                
                # 检查配额是否足够，如果不够则重置配额
                if self.user_info['quota_used'] >= self.user_info['quota_limit']:
                    self._log("⚠️ 配额不足，尝试重置配额...")
                    if await self.reset_quota():
                        self._log("✅ 配额重置成功!")
                    else:
                        self._log("❌ 配额重置失败!")
                        return False
                
                return True
            else:
                self._log(f"❌ 登录失败: {response}")
                return False
                
        except Exception as e:
            self._log(f"❌ 登录异常: {str(e)}")
            return False
    
    async def step2_create_novel(self) -> bool:
        """步骤2: 创建小说项目"""
        self._log("📚 步骤2: 创建小说项目...")
        
        try:
            novel_data = {
                "title": self.config.novel_title,
                "description": self.config.novel_theme,
                "genre": self.config.novel_genre,
                "target_length": self.config.target_length,
                "style_settings": {
                    "style": "细腻科幻",
                    "pace": "medium",
                    "focus": "plot_and_character"
                }
            }
            
            response = await self._make_request("POST", "/novels/", novel_data)
            
            # 兼容两种响应格式：统一格式和直接返回对象
            if "success" in response:
                # 统一响应格式
                if response["success"]:
                    novel = response["data"]
                    self.novel_id = novel["id"]
                    
                    self._log(f"✅ 小说创建成功! 小说ID: {self.novel_id}")
                    self._log(f"📖 小说标题: {novel['title']}")
                    return True
                else:
                    self._log(f"❌ 小说创建失败: {response}")
                    return False
            elif "id" in response:
                # 直接返回小说对象格式
                self.novel_id = response["id"]
                
                self._log(f"✅ 小说创建成功! 小说ID: {self.novel_id}")
                self._log(f"📖 小说标题: {response['title']}")
                return True
            else:
                self._log(f"❌ 未知响应格式: {response}")
                return False
                
        except Exception as e:
            self._log(f"❌ 小说创建异常: {str(e)}")
            return False
    
    async def step3_generate_architecture(self) -> bool:
        """步骤3: 生成小说架构"""
        self._log("🏗️ 步骤3: 生成小说架构...")
        
        try:
            arch_data = {
                "novel_id": self.novel_id,
                "theme": self.config.novel_theme,
                "genre": self.config.novel_genre,
                "target_length": self.config.target_length,
                "style_preferences": {
                    "style": "细腻科幻",
                    "pace": "medium",
                    "focus": "character_development"
                }
            }
            
            response = await self._make_request("POST", "/generation/architecture", arch_data)
            
            # 兼容两种响应格式
            if "success" in response and response["success"]:
                # 统一响应格式
                task_info = response["data"]
                task_id = task_info["task_id"]
            elif "task_id" in response:
                # 直接返回任务对象格式
                task_id = response["task_id"]
            else:
                self._log(f"❌ 架构生成失败: {response}")
                return False
                
            self._log(f"✅ 架构生成任务启动! 任务ID: {task_id}")
            
            # 等待任务完成
            success = await self._wait_for_task_completion(task_id, "架构生成")
            return success
                
        except Exception as e:
            self._log(f"❌ 架构生成异常: {str(e)}")
            return False
    
    async def step4_generate_chapters(self) -> bool:
        """步骤4: 生成章节内容"""
        self._log(f"📝 步骤4: 生成章节内容 (共{self.config.chapter_count}章)...")
        
        try:
            for chapter_num in range(1, self.config.chapter_count + 1):
                self._log(f"✍️ 开始生成第{chapter_num}章...")
                
                # 获取上下文章节（前面的章节）
                context_chapters = list(range(max(1, chapter_num - 2), chapter_num))
                
                chapter_data = {
                    "novel_id": self.novel_id,
                    "chapter_number": chapter_num,
                    "chapter_outline": f"第{chapter_num}章：故事发展的关键转折点",
                    "context_chapters": context_chapters,
                    "style_preferences": {
                        "style": "细腻科幻",
                        "tone": "紧张而富有悬念"
                    }
                }
                
                response = await self._make_request("POST", "/generation/chapter", chapter_data)
                
                # 兼容两种响应格式
                if "success" in response and response["success"]:
                    # 统一响应格式
                    task_info = response["data"]
                    task_id = task_info["task_id"]
                elif "task_id" in response:
                    # 直接返回任务对象格式
                    task_id = response["task_id"]
                else:
                    self._log(f"❌ 第{chapter_num}章生成失败: {response}")
                    return False
                    
                self._log(f"✅ 第{chapter_num}章生成任务启动! 任务ID: {task_id}")
                
                # 等待当前章节完成
                success = await self._wait_for_task_completion(task_id, f"第{chapter_num}章生成")
                if not success:
                    return False
                    
                # 短暂延迟，避免过快请求
                await asyncio.sleep(2)
            
            self._log("✅ 所有章节生成完成!")
            return True
            
        except Exception as e:
            self._log(f"❌ 章节生成异常: {str(e)}")
            return False
    
    async def step5_verify_results(self) -> bool:
        """步骤5: 验证生成结果"""
        self._log("🔍 步骤5: 验证生成结果...")
        
        try:
            # 获取小说详情
            response = await self._make_request("GET", f"/novels/{self.novel_id}")
            
            # 兼容两种响应格式
            if "success" in response and response["success"]:
                novel = response["data"]
            elif "id" in response:
                novel = response
            else:
                self._log(f"❌ 获取小说详情失败: {response}")
                return False
                
            self._log(f"📊 小说状态: {novel['status']}")
            self._log(f"📊 目标长度: {novel.get('target_length', 'N/A')}")
            
            # 获取章节列表
            chapters_response = await self._make_request("GET", f"/chapters/novel/{self.novel_id}")
            
            # 兼容两种响应格式
            if "success" in chapters_response and chapters_response["success"]:
                chapters = chapters_response["data"]["items"]
            elif "novels" in chapters_response or isinstance(chapters_response, list):
                chapters = chapters_response if isinstance(chapters_response, list) else chapters_response.get("items", [])
            else:
                self._log(f"❌ 获取章节列表失败: {chapters_response}")
                return False
                
            self._log(f"📊 已生成章节数: {len(chapters)}")
            
            total_words = 0
            for chapter in chapters:
                word_count = chapter.get("word_count", 0)
                total_words += word_count
                self._log(f"  📄 {chapter.get('title', '未命名章节')}: {word_count}字")
            
            self._log(f"📊 总字数: {total_words}")
            self._log(f"📊 完成度: {(total_words / self.config.target_length * 100):.1f}%")
            
            return len(chapters) >= self.config.chapter_count
                
        except Exception as e:
            self._log(f"❌ 结果验证异常: {str(e)}")
            return False
    
    async def _wait_for_task_completion(self, task_id: str, task_name: str, max_wait_time: int = 300) -> bool:
        """等待任务完成（使用轮询方式）"""
        self._log(f"⏳ 等待{task_name}完成...")
        
        start_time = time.time()
        last_progress = -1
        
        while time.time() - start_time < max_wait_time:
            try:
                # 获取任务状态
                response = await self._make_request("GET", f"/tasks/{task_id}")
                
                # 兼容两种响应格式
                if "success" in response and response["success"]:
                    # 统一响应格式
                    task = response["data"]
                elif "status" in response:
                    # 直接返回任务对象格式
                    task = response
                else:
                    self._log(f"❌ 获取任务状态失败: {response}")
                    await asyncio.sleep(5)
                    continue
                    
                status = task["status"]
                progress = task.get("progress", 0)
                
                # 只在进度有变化时输出
                if progress != last_progress:
                    self._log(f"📈 {task_name}进度: {progress}% (状态: {status})")
                    last_progress = progress
                
                if status == TaskStatus.SUCCESS.value:
                    self._log(f"✅ {task_name}完成!")
                    return True
                elif status == TaskStatus.FAILED.value:
                    error_msg = task.get("error_message", "未知错误")
                    self._log(f"❌ {task_name}失败: {error_msg}")
                    return False
                elif status == TaskStatus.CANCELLED.value:
                    self._log(f"⚠️ {task_name}已取消")
                    return False
                
                # 等待一段时间后再次检查
                await asyncio.sleep(5)
                    
            except Exception as e:
                self._log(f"⚠️ 检查任务状态异常: {str(e)}")
                await asyncio.sleep(5)
        
        self._log(f"⏰ {task_name}等待超时 ({max_wait_time}秒)")
        return False
    
    async def run_complete_flow(self) -> bool:
        """运行完整的生成流程"""
        self._log("🚀 开始AI小说生成完整流程测试")
        self._log("=" * 60)
        
        start_time = time.time()
        
        try:
            # 步骤1: 用户登录
            if not await self.step1_user_login():
                return False
            
            # 步骤2: 创建小说项目
            if not await self.step2_create_novel():
                return False
            
            # 步骤3: 生成小说架构
            if not await self.step3_generate_architecture():
                return False
            
            # 步骤4: 生成章节内容
            if not await self.step4_generate_chapters():
                return False
            
            # 步骤5: 验证生成结果
            if not await self.step5_verify_results():
                return False
            
            elapsed_time = time.time() - start_time
            self._log("=" * 60)
            self._log(f"🎉 完整流程测试成功! 总耗时: {elapsed_time:.1f}秒")
            self._log(f"📚 生成的小说ID: {self.novel_id}")
            
            return True
            
        except Exception as e:
            self._log(f"❌ 流程执行异常: {str(e)}")
            return False
    
    async def run_quota_reset_only(self) -> bool:
        """仅运行配额重置功能"""
        self._log("🔄 开始配额重置操作")
        self._log("=" * 60)
        
        try:
            # 步骤1: 用户登录
            if not await self.step1_user_login():
                return False
            
            # 显示当前配额状态
            quota_info = await self.check_quota_status()
            self._log(f"📊 当前配额状态: {quota_info.get('quota_used', 0)}/{quota_info.get('quota_limit', 10)}")
            
            if quota_info.get('quota_used', 0) == 0:
                self._log("✅ 配额已经为0，无需重置")
                return True
            
            # 直接运行配额重置
            self._log("🔄 执行配额重置...")
            if await self.reset_quota():
                # 再次检查配额状态
                updated_quota = await self.check_quota_status()
                self._log(f"📊 重置后配额状态: {updated_quota.get('quota_used', 0)}/{updated_quota.get('quota_limit', 10)}")
                self._log("✅ 配额重置成功!")
                return True
            else:
                self._log("❌ 配额重置失败!")
                return False
                
        except Exception as e:
            self._log(f"❌ 配额重置异常: {str(e)}")
            return False


async def run_novel_generation_test():
    """运行小说生成测试"""
    config = TestConfig()
    
    print("🤖 AI小说生成器 - 测试工具")
    print("=" * 60)
    print(f"📡 API服务地址: {config.base_url}")
    print(f"📖 测试小说主题: {config.novel_theme}")
    print(f"📊 目标长度: {config.target_length}字")
    print(f"📝 计划章节数: {config.chapter_count}章")
    print("=" * 60)
    
    # 显示选择菜单
    print("\n请选择测试模式:")
    print("1. 完整流程测试 (登录 → 创建小说 → 生成架构 → 生成章节 → 验证结果)")
    print("2. 仅配额重置 (清理现有小说并重置配额)")
    print("0. 退出")
    print("-" * 60)
    
    try:
        choice = input("请输入选项 (0-2): ").strip()
    except KeyboardInterrupt:
        print("\n👋 测试已取消")
        return 0
    
    if choice == "0":
        print("👋 再见!")
        return 0
    elif choice == "1":
        print("\n🚀 执行完整流程测试...")
        
        async with NovelGenerationTester(config) as tester:
            success = await tester.run_complete_flow()
            
            if success:
                print("\n🎊 测试完成 - 所有步骤都成功执行!")
                print("💡 建议: 可以通过API或数据库查看生成的具体内容")
            else:
                print("\n💥 测试失败 - 请检查服务状态和配置")
                return 1
        
        return 0
        
    elif choice == "2":
        print("\n🔄 执行配额重置...")
        
        async with NovelGenerationTester(config) as tester:
            success = await tester.run_quota_reset_only()
            
            if success:
                print("\n✅ 配额重置完成!")
                print("💡 现在可以重新运行完整流程测试")
            else:
                print("\n❌ 配额重置失败")
                print("💡 提示: 请检查是否有足够的权限或联系管理员")
                return 1
        
        return 0
        
    else:
        print("❌ 无效选项，请重新运行程序")
        return 1


if __name__ == "__main__":
    print("启动AI小说生成流程测试...")
    exit_code = asyncio.run(run_novel_generation_test())
    exit(exit_code) 