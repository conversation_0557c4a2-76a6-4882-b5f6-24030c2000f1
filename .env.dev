# 开发环境配置文件
# 使用SQLite数据库，无需安装PostgreSQL

# 应用配置
APP_NAME=AI Novel Generator
DEBUG=true
VERSION=1.0.0

# 数据库配置 - 使用SQLite
DATABASE_URL=sqlite+aiosqlite:///./ai_novel_dev.db

# Redis配置 (如果没有Redis，可以使用内存存储)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Celery配置 (使用Redis或内存)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# LLM配置
LLM_PROVIDER=openai
LLM_MODEL=gemini-2.5-flash
LLM_API_KEY=your_api_key_here
LLM_BASE_URL=https://api.openai.com/v1

# JWT配置
SECRET_KEY=your-secret-key-for-development
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 开发模式特殊配置
ENABLE_CORS=true
ALLOW_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
