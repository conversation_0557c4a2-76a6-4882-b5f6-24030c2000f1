services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: novel_postgres
    environment:
      POSTGRES_DB: ai_novel_generator
      POSTGRES_USER: novel_user
      POSTGRES_PASSWORD: novel_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - novel_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U novel_user -d ai_novel_generator"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: novel_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - novel_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # RabbitMQ消息队列（备选）
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: novel_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: novel_user
      RABBITMQ_DEFAULT_PASS: novel_password
      RABBITMQ_DEFAULT_VHOST: novel_vhost
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - rabbitmq  # 可选服务，使用 --profile rabbitmq 启动

  # FastAPI应用服务
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: novel_api
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./chroma_db:/app/chroma_db
    environment:
      - POSTGRES_HOST=novel_postgres
      - REDIS_HOST=novel_redis
      - DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@novel_postgres:5432/ai_novel_generator
      - REDIS_URL=redis://novel_redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - app  # 可选服务，使用 --profile app 启动

  # Celery Worker服务
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: novel_celery_worker
    command: celery -A app.core.celery_app worker -l info --concurrency=4
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./chroma_db:/app/chroma_db
    environment:
      - POSTGRES_HOST=novel_postgres
      - REDIS_HOST=novel_redis
      - DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@novel_postgres:5432/ai_novel_generator
      - REDIS_URL=redis://novel_redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - app  # 可选服务

  # Celery Beat调度器（定时任务）
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: novel_celery_beat
    command: celery -A app.core.celery_app beat -l info
    volumes:
      - .:/app
      - ./logs:/app/logs
    environment:
      - POSTGRES_HOST=novel_postgres
      - REDIS_HOST=novel_redis
      - DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@novel_postgres:5432/ai_novel_generator
      - REDIS_URL=redis://novel_redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - app  # 可选服务

  # ChromaDB向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: novel_chromadb
    ports:
      - "8001:8000"  # 避免与API端口冲突
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      CHROMA_DB_IMPL: clickhouse
      CLICKHOUSE_HOST: clickhouse
      CLICKHOUSE_PORT: 8123
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - vector_db  # 可选服务

  # ClickHouse（ChromaDB后端）
  clickhouse:
    image: clickhouse/clickhouse-server:latest
    container_name: novel_clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    environment:
      CLICKHOUSE_DB: chroma
      CLICKHOUSE_USER: chroma
      CLICKHOUSE_PASSWORD: chroma_password
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - vector_db  # 可选服务

  # Prometheus监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: novel_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana监控界面（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: novel_grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    networks:
      - novel_network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  chroma_data:
  clickhouse_data:
  prometheus_data:
  grafana_data:

networks:
  novel_network:
    driver: bridge 