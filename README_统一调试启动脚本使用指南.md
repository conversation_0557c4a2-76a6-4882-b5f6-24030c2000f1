# AI小说生成器 - 统一调试启动脚本使用指南

## 📋 概述

为了方便开发调试，我们提供了统一的调试启动脚本，可以同时启动FastAPI服务和Celery Worker，并提供统一的日志输出和进程管理。

## 🚀 快速开始

### 方式一：直接启动（推荐）
```bash
# 激活虚拟环境
novel_ai_env/Scripts/Activate.ps1

# 启动调试系统
python start_debug.py
```

### 方式二：使用完整脚本
```bash
# 激活虚拟环境
novel_ai_env/Scripts/Activate.ps1

# 启动统一调试系统
python scripts/start_debug_system.py
```

### 方式三：通过PowerShell脚本
```powershell
# 运行系统启动脚本（会询问启动方式）
./start_system.ps1
```

## 📁 脚本文件说明

### 主要脚本

| 脚本文件 | 功能描述 | 使用场景 |
|---------|----------|----------|
| `start_debug.py` | 快速调试启动入口 | 日常开发调试 |
| `scripts/start_debug_system.py` | 统一调试系统管理器 | 完整的调试环境 |
| `scripts/start_api.py` | 单独启动API服务 | 只需要API服务时 |
| `scripts/start_worker_windows.py` | 单独启动Worker | 只需要Worker时 |
| `start_system.ps1` | PowerShell一键启动 | 完整的系统部署 |

### 脚本关系图
```
start_debug.py (快速入口)
    ↓
scripts/start_debug_system.py (统一管理器)
    ├── 启动 API 服务 (基于 start_api.py 逻辑)
    └── 启动 Celery Worker (基于 start_worker_windows.py 逻辑)
```

## 🔧 功能特性

### 统一日志输出
- **时间戳标记**：每条日志都带有精确的时间戳
- **服务分类**：不同服务的日志用不同前缀标识
  - `[SYSTEM]` - 系统管理日志
  - `[API]` - FastAPI服务日志
  - `[WORKER]` - Celery Worker日志

### 智能环境检查
- **Python版本检查**：确保使用Python 3.8+
- **虚拟环境检测**：自动发现和使用项目虚拟环境
- **依赖服务检查**：验证PostgreSQL和Redis连接
- **环境变量设置**：自动设置默认配置

### 进程生命周期管理
- **并发启动**：同时启动API和Worker服务
- **健康监控**：实时监控服务状态
- **优雅关闭**：Ctrl+C时自动清理所有进程
- **异常恢复**：服务异常时自动关闭整个系统

## 📊 日志输出示例

```
14:30:15 [SYSTEM] AI小说生成器 - 统一调试启动脚本
14:30:15 [SYSTEM] ==================================================
14:30:15 [SYSTEM] 正在检查运行环境...
14:30:15 [SYSTEM] ✓ Python版本: 3.9.7 (tags/v3.9.7:1016ef3, Aug 30 2021, 20:19:38)
14:30:15 [SYSTEM] ✓ 虚拟环境: E:\Ecode\AInovel_v2\ai_novel_mini_program\AI_NovelGenerator\novel_ai_env
14:30:15 [SYSTEM] 正在设置环境变量...
14:30:15 [SYSTEM] ✓ 设置了 8 个默认环境变量
14:30:15 [SYSTEM] 正在检查服务依赖...
14:30:16 [SYSTEM] ✓ 数据库连接成功
14:30:16 [SYSTEM] ✓ Redis连接成功
14:30:16 [SYSTEM] 正在启动服务...
14:30:16 [API] 正在启动API服务器...
14:30:16 [API] 执行命令: E:\...\python.exe -m uvicorn app.main:app --host=0.0.0.0 --port=8000 --reload --log-level=info
14:30:16 [API] API服务已启动，PID: 12345
14:30:16 [API] 服务地址: http://0.0.0.0:8000
14:30:18 [WORKER] 正在启动Celery Worker...
14:30:18 [WORKER] 执行命令: E:\...\python.exe -m celery -A app.core.celery_app:celery_app worker --loglevel=info --concurrency=2 --pool=solo --without-mingle --without-gossip
14:30:18 [WORKER] Celery Worker已启动，PID: 12346
14:30:20 [SYSTEM] ✓ 所有服务启动完成
14:30:20 [SYSTEM] 
14:30:20 [SYSTEM] 服务状态:
14:30:20 [SYSTEM]   - API服务: http://0.0.0.0:8000
14:30:20 [SYSTEM]   - Swagger文档: http://0.0.0.0:8000/docs
14:30:20 [SYSTEM]   - Celery Worker: 运行中
14:30:20 [SYSTEM] 
14:30:20 [SYSTEM] 按 Ctrl+C 停止所有服务
14:30:20 [SYSTEM] ==================================================
14:30:21 [API] INFO:     Started server process [12345]
14:30:21 [API] INFO:     Waiting for application startup.
14:30:21 [API] INFO:     Application startup complete.
14:30:21 [API] INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
14:30:22 [WORKER] [2024-01-01 14:30:22,123: INFO/MainProcess] Connected to redis://localhost:6379/0
14:30:22 [WORKER] [2024-01-01 14:30:22,135: INFO/MainProcess] mingle: searching for available nodes...
14:30:22 [WORKER] [2024-01-01 14:30:22,145: INFO/MainProcess] mingle: all alone
```

## ⚙️ 配置说明

### 环境变量

脚本会自动设置以下默认环境变量（如果不存在）：

```bash
ENVIRONMENT=development
DEBUG=true
PROJECT_NAME=AI Novel Generator
DATABASE_URL=postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
HOST=0.0.0.0
PORT=8000
```

### 自定义配置

如需自定义配置，请在运行脚本前设置环境变量或创建 `.env` 文件：

```bash
# .env 文件示例
DATABASE_URL=postgresql+asyncpg://custom_user:custom_pass@custom_host:5432/custom_db
REDIS_URL=redis://custom_host:6379/1
PORT=8080
LLM_API_KEY=your-api-key
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案：修改 PORT 环境变量或关闭占用端口的程序
   ```

2. **数据库连接失败**
   ```
   解决方案：检查PostgreSQL服务是否启动，检查DATABASE_URL配置
   ```

3. **Redis连接失败**
   ```
   解决方案：检查Redis服务是否启动，检查REDIS_URL配置
   ```

4. **虚拟环境未找到**
   ```
   解决方案：确保虚拟环境名为 novel_ai_env 或设置 VIRTUAL_ENV 环境变量
   ```

### 调试技巧

1. **查看详细日志**：脚本会显示所有服务的实时日志输出

2. **单独测试服务**：
   ```bash
   # 只测试API服务
   python scripts/start_api.py
   
   # 只测试Worker服务
   python scripts/start_worker_windows.py
   ```

3. **检查服务状态**：
   ```bash
   # 检查API服务
   curl http://localhost:8000/health
   
   # 检查数据库连接
   python -c "import asyncio; from app.core.database import test_connection; asyncio.run(test_connection())"
   ```

## 🔄 开发工作流

### 推荐的开发流程

1. **启动基础服务**
   ```bash
   # 启动Docker服务（PostgreSQL + Redis）
   docker-compose up -d postgres redis
   ```

2. **激活虚拟环境**
   ```bash
   novel_ai_env/Scripts/Activate.ps1
   ```

3. **启动调试系统**
   ```bash
   python start_debug.py
   ```

4. **开始开发**
   - API文档：http://localhost:8000/docs
   - 健康检查：http://localhost:8000/health
   - 实时日志：控制台输出

5. **停止服务**
   ```
   按 Ctrl+C 停止所有服务
   ```

### 与其他工具集成

- **VS Code调试**：可以在IDE中设置断点，然后使用统一启动脚本
- **测试运行**：启动调试系统后运行测试脚本
- **API测试**：使用Postman或其他工具测试API接口

## 📋 检查清单

启动前请确认：

- [ ] 已安装Python 3.8+
- [ ] 已创建并激活虚拟环境
- [ ] 已安装项目依赖 (`pip install -r app/requirements.txt`)
- [ ] PostgreSQL服务正在运行
- [ ] Redis服务正在运行
- [ ] 环境变量配置正确（或使用默认配置）
- [ ] 在项目根目录下运行脚本

## 🎯 性能建议

1. **开发环境**：使用统一调试脚本，方便实时查看日志
2. **测试环境**：可以使用分离的脚本，分别启动和停止服务
3. **生产环境**：使用Docker容器化部署，不要使用调试脚本

---

**注意**：此脚本仅用于开发调试环境，生产环境请使用Docker部署或其他生产级别的部署方案。 