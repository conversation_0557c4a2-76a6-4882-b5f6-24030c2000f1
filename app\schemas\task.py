#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务相关数据传输对象
"""
from typing import Optional, List, Any
from datetime import datetime
from pydantic import BaseModel, field_validator
from uuid import UUID
import json

class TaskResponse(BaseModel):
    """任务响应对象"""
    id: UUID  # UUID
    task_type: str
    status: str
    progress: int = 0
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    # 新增详细状态字段
    current_stage: Optional[str] = None
    detailed_progress: Optional[dict] = None
    result_data: Optional[dict] = None
    
    @field_validator('detailed_progress', mode='before')
    @classmethod
    def parse_detailed_progress(cls, v: Any) -> Optional[dict]:
        """解析详细进度字段 - 处理JSON字符串"""
        if v is None:
            return None
        if isinstance(v, dict):
            return v
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, ValueError):
                return None
        return None
    
    @field_validator('result_data', mode='before')
    @classmethod
    def parse_result_data(cls, v: Any) -> Optional[dict]:
        """解析结果数据字段 - 处理JSON字符串"""
        if v is None:
            return None
        if isinstance(v, dict):
            return v
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, ValueError):
                return None
        return None
    
    model_config = {"from_attributes": True}


class TaskListResponse(BaseModel):
    """任务列表响应"""
    tasks: List[TaskResponse]
    total: int
    page: int
    per_page: int 
