# ====================================================================
# Docker构建忽略文件
# ====================================================================

# 虚拟环境
novel_ai_env/
venv/
env/
.venv/

# Python缓存
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# 日志文件
logs/*.log
*.log

# 临时文件
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 环境配置（敏感信息）
.env
.env.local
.env.production

# 输出目录
output/
uploads/
chroma_db/

# 文档
*.md
docs/

# 测试
tests/
.pytest_cache/
.coverage
htmlcov/

# Node.js（如果有前端资源）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker相关
Dockerfile
.dockerignore
docker-compose*.yml 