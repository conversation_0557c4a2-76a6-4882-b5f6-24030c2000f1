#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库配置和连接管理
"""
import logging
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
try:
    from sqlalchemy.ext.asyncio import async_sessionmaker
except ImportError:
    # 兼容旧版本SQLAlchemy，使用同步sessionmaker的异步版本
    from sqlalchemy.orm import sessionmaker
    def async_sessionmaker(engine, **kwargs):
        return sessionmaker(bind=engine, **kwargs)
try:
    from sqlalchemy.orm import DeclarativeBase
except ImportError:
    # 兼容旧版本SQLAlchemy
    from sqlalchemy.ext.declarative import declarative_base
    DeclarativeBase = None
from sqlalchemy import text

from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建异步数据库引擎
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)


if DeclarativeBase is not None:
    # SQLAlchemy 2.0+
    class Base(DeclarativeBase):
        """数据库基类"""
        pass
else:
    # SQLAlchemy 1.4
    Base = declarative_base()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话的依赖注入函数"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """初始化数据库"""
    try:
        # 测试数据库连接
        async with engine.begin() as conn:
            # 测试连接
            result = await conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_db() -> None:
    """关闭数据库连接"""
    await engine.dispose()
    logger.info("Database connections closed")