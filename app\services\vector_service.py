#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
向量存储服务
基于旧版本vectorstore_utils.py的成熟语义处理逻辑
"""
import os
import re
import logging
import asyncio
from typing import List, Optional, Dict, Any
from uuid import UUID
from pathlib import Path
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class VectorStoreService:
    """向量存储服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.vectorstore = None
        self.embedding_model = None
    
    async def initialize_for_novel(self, novel_id: UUID) -> bool:
        """为指定小说初始化向量存储"""
        try:
            # 初始化embedding模型
            await self._initialize_embedding_model()
            
            # 创建或加载向量存储
            store_path = self._get_vectorstore_path(novel_id)
            
            if os.path.exists(store_path):
                self.vectorstore = await self._load_existing_vectorstore(store_path)
                logger.info(f"Loaded existing vectorstore for novel {novel_id}")
            else:
                self.vectorstore = await self._create_new_vectorstore(store_path)
                logger.info(f"Created new vectorstore for novel {novel_id}")
            
            if self.vectorstore is None:
                logger.error(f"Failed to initialize vectorstore for novel {novel_id}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store for novel {novel_id}: {e}")
            # 清理可能的残留状态
            self.vectorstore = None
            self.embedding_model = None
            return False
    
    async def _initialize_embedding_model(self):
        """初始化embedding模型"""
        try:
            from sentence_transformers import SentenceTransformer
            
            # 使用本地模型路径
            model_path = './paraphrase-MiniLM-L6-v2'
            if os.path.exists(model_path):
                self.embedding_model = SentenceTransformer(model_path)
            else:
                # 如果本地模型不存在，使用默认模型
                self.embedding_model = SentenceTransformer('paraphrase-MiniLM-L6-v2')
                
        except Exception as e:
            logger.error(f"Failed to initialize embedding model: {e}")
            raise
    
    def _get_vectorstore_path(self, novel_id: UUID) -> str:
        """获取向量存储路径"""
        base_path = "./vectorstores"
        os.makedirs(base_path, exist_ok=True)
        return os.path.join(base_path, f"novel_{novel_id}")
    
    async def _create_new_vectorstore(self, store_path: str):
        """创建新的向量存储"""
        try:
            from langchain_chroma import Chroma
            from langchain.docstore.document import Document
            from chromadb.config import Settings
            
            # 创建空的向量存储
            embedding_wrapper = self._create_embedding_wrapper()
            
            vectorstore = Chroma(
                persist_directory=store_path,
                embedding_function=embedding_wrapper,
                client_settings=Settings(anonymized_telemetry=False),
                collection_name="novel_collection"
            )
            
            return vectorstore
            
        except Exception as e:
            logger.error(f"Failed to create new vectorstore: {e}")
            return None
    
    async def _load_existing_vectorstore(self, store_path: str):
        """加载已存在的向量存储"""
        try:
            from langchain_chroma import Chroma
            from chromadb.config import Settings
            
            embedding_wrapper = self._create_embedding_wrapper()
            
            vectorstore = Chroma(
                persist_directory=store_path,
                embedding_function=embedding_wrapper,
                client_settings=Settings(anonymized_telemetry=False),
                collection_name="novel_collection"
            )
            
            return vectorstore
            
        except Exception as e:
            logger.error(f"Failed to load existing vectorstore: {e}")
            return None
    
    def _create_embedding_wrapper(self):
        """创建embedding包装器"""
        from langchain.embeddings.base import Embeddings as LCEmbeddings
        
        class SentenceTransformerEmbedding(LCEmbeddings):
            def __init__(self, model):
                self.model = model
            
            def embed_documents(self, texts: List[str]) -> List[List[float]]:
                return self.model.encode(texts).tolist()
            
            def embed_query(self, query: str) -> List[float]:
                return self.model.encode([query])[0].tolist()
        
        return SentenceTransformerEmbedding(self.embedding_model)
    
    async def add_chapter_content(self, chapter_content: str, chapter_id: UUID) -> bool:
        """添加章节内容到向量存储"""
        try:
            if not self.vectorstore:
                logger.error("Vectorstore not initialized")
                return False
            
            if not chapter_content or not chapter_content.strip():
                logger.warning(f"Chapter {chapter_id} has empty content, skipping vectorstore update")
                return True  # 空内容不算错误
            
            # 智能文本分段，基于旧版本的语义处理逻辑
            segments = await self._smart_text_segmentation(chapter_content)
            
            if not segments:
                logger.warning(f"Chapter {chapter_id} segmentation resulted in no segments")
                return False
            
            from langchain.docstore.document import Document
            
            # 创建文档对象
            documents = []
            for i, segment in enumerate(segments):
                if segment.strip():  # 只添加非空段落
                    doc = Document(
                        page_content=segment,
                        metadata={
                            "chapter_id": chapter_id,
                            "segment_index": i,
                            "segment_type": "chapter_content"
                        }
                    )
                    documents.append(doc)
            
            if not documents:
                logger.warning(f"Chapter {chapter_id} has no valid segments to add")
                return False
            
            # 添加到向量存储
            await asyncio.to_thread(self.vectorstore.add_documents, documents)
            
            logger.info(f"Successfully added {len(documents)} segments from chapter {chapter_id} to vectorstore")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add chapter content to vectorstore: {e}")
            return False
    
    async def _smart_text_segmentation(self, text: str, max_length: int = 500, 
                                     similarity_threshold: float = 0.7) -> List[str]:
        """智能文本分段，基于旧版本vectorstore_utils.py的逻辑"""
        
        if not text.strip():
            return []
        
        try:
            # 1. 按句子分割
            sentences = self._split_into_sentences(text)
            
            if not sentences:
                return []
            
            # 2. 计算句子的embedding
            embeddings = self.embedding_model.encode(sentences)
            
            # 3. 基于语义相似度合并段落
            merged_paragraphs = []
            current_sentences = [sentences[0]]
            current_embedding = embeddings[0]
            
            for i in range(1, len(sentences)):
                # 计算余弦相似度
                sim = self._cosine_similarity(current_embedding, embeddings[i])
                
                if sim >= similarity_threshold:
                    # 相似度高，合并到当前段落
                    current_sentences.append(sentences[i])
                    # 更新embedding为平均值
                    current_embedding = (current_embedding + embeddings[i]) / 2.0
                else:
                    # 相似度低，创建新段落
                    merged_paragraphs.append("".join(current_sentences))
                    current_sentences = [sentences[i]]
                    current_embedding = embeddings[i]
            
            # 添加最后一个段落
            if current_sentences:
                merged_paragraphs.append("".join(current_sentences))
            
            # 4. 按长度进一步分割
            final_segments = []
            for paragraph in merged_paragraphs:
                if len(paragraph) <= max_length:
                    final_segments.append(paragraph)
                else:
                    # 长段落按长度分割
                    segments = self._split_by_length(paragraph, max_length)
                    final_segments.extend(segments)
            
            return final_segments
            
        except Exception as e:
            logger.error(f"Failed in smart text segmentation: {e}")
            # 降级到简单分割
            return self._split_by_length(text, max_length)
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """按句子分割文本，基于旧版本的正则表达式逻辑"""
        
        # 使用正则表达式按中文标点或换行符分割句子
        sentence_delimiters = r'([.。！？?!\n])'
        split_parts = re.split(sentence_delimiters, text)
        
        # 合并句子片段和它们后面的分隔符
        merged = []
        current_sentence = ""
        
        for part in split_parts:
            if not part:
                continue
            
            # 检查当前部分是否是分隔符
            is_delimiter = re.fullmatch(sentence_delimiters, part)
            
            if is_delimiter:
                # 将分隔符附加到当前句子，并完成该句子
                current_sentence += part
                if current_sentence.strip():
                    merged.append(current_sentence.strip())
                current_sentence = ""
            else:
                # 如果是文本片段，累加到当前句子
                current_sentence += part
        
        # 添加最后一个可能未被分隔符结束的句子
        if current_sentence.strip():
            merged.append(current_sentence.strip())
        
        return merged
    
    def _split_by_length(self, text: str, max_length: int) -> List[str]:
        """按长度分割文本"""
        segments = []
        start_idx = 0
        
        while start_idx < len(text):
            end_idx = min(start_idx + max_length, len(text))
            segment = text[start_idx:end_idx]
            segments.append(segment.strip())
            start_idx = end_idx
        
        return [seg for seg in segments if seg]
    
    def _cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        import numpy as np
        
        # 计算点积
        dot_product = np.dot(vec1, vec2)
        
        # 计算向量的模长
        norm_a = np.linalg.norm(vec1)
        norm_b = np.linalg.norm(vec2)
        
        # 避免除零
        if norm_a == 0 or norm_b == 0:
            return 0
        
        return dot_product / (norm_a * norm_b)
    
    async def search_relevant_context(self, query: str, k: int = 3) -> str:
        """搜索相关上下文 - 智能语义检索，基于旧版本逻辑优化"""
        try:
            if not self.vectorstore:
                return ""
            
            # 执行相似度搜索
            results = await asyncio.to_thread(
                self.vectorstore.similarity_search_with_score,
                query,
                k=k*2  # 获取更多结果用于过滤
            )
            
            if not results:
                return ""
            
            # 智能过滤和排序结果
            context_parts = []
            for doc, score in results:
                # 基于相似度阈值过滤（score越小越相似）
                if score < 0.8:  # 只保留相似度较高的结果
                    metadata = doc.metadata
                    content = doc.page_content
                    
                    # 为不同类型的内容添加上下文标签
                    content_type = metadata.get("segment_type", "general")
                    if content_type == "chapter_content":
                        chapter_id = metadata.get("chapter_id", "")
                        context_parts.append(f"[相关章节内容]：{content}")
                    else:
                        context_parts.append(content)
            
            # 智能合并相似内容，避免重复
            merged_contexts = self._merge_similar_contexts(context_parts)
            
            return "\n\n".join(merged_contexts[:k])  # 限制最终返回数量
            
        except Exception as e:
            logger.error(f"Failed to search relevant context: {e}")
            # 降级到简单搜索
            try:
                results = await asyncio.to_thread(
                    self.vectorstore.similarity_search,
                    query,
                    k=k
                )
                
                if results:
                    context_parts = [doc.page_content for doc in results]
                    return "\n\n".join(context_parts)
                
            except Exception as fallback_e:
                logger.error(f"Fallback search also failed: {fallback_e}")
            
            return ""
    
    def _merge_similar_contexts(self, contexts: List[str]) -> List[str]:
        """合并相似的上下文内容"""
        if len(contexts) <= 1:
            return contexts
        
        merged = []
        used_indices = set()
        
        for i, context in enumerate(contexts):
            if i in used_indices:
                continue
            
            # 检查是否与已有内容相似
            is_similar = False
            for j in range(i+1, len(contexts)):
                if j in used_indices:
                    continue
                
                # 简单的相似度检测
                if self._calculate_text_similarity(context, contexts[j]) > 0.7:
                    # 合并相似内容，保留较长的
                    if len(context) >= len(contexts[j]):
                        merged.append(context)
                    else:
                        merged.append(contexts[j])
                    used_indices.add(i)
                    used_indices.add(j)
                    is_similar = True
                    break
            
            if not is_similar:
                merged.append(context)
                used_indices.add(i)
        
        return merged
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        # 移除标签进行比较
        clean_text1 = text1.split("：", 1)[-1] if "：" in text1 else text1
        clean_text2 = text2.split("：", 1)[-1] if "：" in text2 else text2
        
        # 简单的字符重叠率计算
        set1 = set(clean_text1[:100])  # 只比较前100个字符
        set2 = set(clean_text2[:100])
        
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    async def clear_vectorstore(self, novel_id: UUID) -> bool:
        """清空指定小说的向量存储"""
        try:
            store_path = self._get_vectorstore_path(novel_id)
            
            if os.path.exists(store_path):
                import shutil
                shutil.rmtree(store_path)
                logger.info(f"Vector store for novel {novel_id} cleared")
            
            self.vectorstore = None
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear vector store for novel {novel_id}: {e}")
            return False