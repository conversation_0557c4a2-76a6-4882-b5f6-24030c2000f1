#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
章节管理API端点
"""
from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, Query, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from app.core.database import get_async_db
from app.core.logging_config import get_logger, log_business_operation
from app.core.exceptions import (
    NotFoundException, PermissionDeniedException, DatabaseException, ErrorCode
)
from app.models.user import User
from app.models.novel import Novel
from app.models.chapter import Chapter, ChapterStatus
from app.schemas.chapter import (
    ChapterCreate, ChapterUpdate, ChapterResponse, ChapterListResponse
)
from app.services.auth_service import AuthService

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=ChapterResponse, summary="创建新章节")
async def create_chapter(
    chapter_data: ChapterCreate,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    为指定小说创建新章节
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证小说是否属于当前用户
        novel_query = select(Novel).where(
            and_(Novel.id == chapter_data.novel_id, Novel.user_id == current_user.id)
        )
        novel_result = await session.execute(novel_query)
        novel = novel_result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 获取下一个章节号
        max_chapter_query = select(Chapter.chapter_number).where(
            Chapter.novel_id == chapter_data.novel_id
        ).order_by(Chapter.chapter_number.desc()).limit(1)
        
        max_result = await session.execute(max_chapter_query)
        max_chapter_number = max_result.scalar_one_or_none() or 0
        
        # 创建章节
        chapter = Chapter(
            novel_id=chapter_data.novel_id,
            chapter_number=max_chapter_number + 1,
            title=chapter_data.title,
            content=chapter_data.content,
            summary=chapter_data.summary
        )
        
        session.add(chapter)
        await session.commit()
        await session.refresh(chapter)
        
        # 记录创建章节日志
        log_business_operation(
            logger=logger.logger,
            operation="create_chapter",
            details={
                "chapter_id": chapter.id,
                "novel_id": chapter_data.novel_id,
                "chapter_number": chapter.chapter_number,
                "title": chapter_data.title
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return ChapterResponse.model_validate(chapter)
        
    except (NotFoundException, PermissionDeniedException):
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"创建章节失败 - 用户ID: {user_id}, 小说ID: {chapter_data.novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'create_chapter'
        })
        raise DatabaseException(
            message="创建章节失败",
            details={"novel_id": chapter_data.novel_id}
        )


@router.get("/novel/{novel_id}", response_model=ChapterListResponse, summary="获取小说章节列表")
async def get_novel_chapters(
    novel_id: UUID,
    request: Request,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取指定小说的章节列表
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 验证小说权限
        novel_query = select(Novel).where(
            and_(Novel.id == novel_id, Novel.user_id == current_user.id)
        )
        novel_result = await session.execute(novel_query)
        novel = novel_result.scalar_one_or_none()
        
        if not novel:
            raise NotFoundException(
                message="小说不存在或无权限访问",
                error_code=ErrorCode.NOVEL_NOT_FOUND
            )
        
        # 获取章节列表
        query = select(Chapter).where(Chapter.novel_id == novel_id)
        
        # 获取总数
        total_result = await session.execute(query)
        total = len(total_result.scalars().all())
        
        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(Chapter.chapter_number.asc())
        result = await session.execute(query)
        chapters = result.scalars().all()
        
        # 记录获取章节列表日志
        log_business_operation(
            logger=logger.logger,
            operation="get_novel_chapters",
            details={
                "novel_id": novel_id,
                "total_chapters": total,
                "page": skip // limit + 1,
                "per_page": limit
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return ChapterListResponse(
            chapters=[ChapterResponse.model_validate(chapter) for chapter in chapters],
            total=total,
            page=skip // limit + 1,
            per_page=limit
        )
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取章节列表失败 - 用户ID: {user_id}, 小说ID: {novel_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_novel_chapters'
        })
        raise DatabaseException(
            message="获取章节列表失败",
            details={"novel_id": novel_id}
        )


@router.get("/{chapter_id}", response_model=ChapterResponse, summary="获取章节详情")
async def get_chapter_detail(
    chapter_id: UUID,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    获取指定章节的详细内容
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Chapter).join(Novel).where(
            and_(
                Chapter.id == chapter_id,
                Novel.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        chapter = result.scalar_one_or_none()
        
        if not chapter:
            raise NotFoundException(
                message="章节不存在或无权限访问",
                error_code=ErrorCode.CHAPTER_NOT_FOUND
            )
        
        # 记录获取章节详情日志
        log_business_operation(
            logger=logger.logger,
            operation="get_chapter_detail",
            details={
                "chapter_id": chapter_id,
                "novel_id": chapter.novel_id,
                "chapter_number": chapter.chapter_number,
                "title": chapter.title
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return ChapterResponse.model_validate(chapter)
        
    except NotFoundException:
        raise
    except Exception as e:
        logger.error(f"获取章节详情失败 - 用户ID: {user_id}, 章节ID: {chapter_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'get_chapter_detail'
        })
        raise DatabaseException(
            message="获取章节详情失败",
            details={"chapter_id": chapter_id}
        )


@router.put("/{chapter_id}", response_model=ChapterResponse, summary="更新章节")
async def update_chapter(
    chapter_id: UUID,
    update_data: ChapterUpdate,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    更新指定章节的内容
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Chapter).join(Novel).where(
            and_(
                Chapter.id == chapter_id,
                Novel.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        chapter = result.scalar_one_or_none()
        
        if not chapter:
            raise NotFoundException(
                message="章节不存在或无权限访问",
                error_code=ErrorCode.CHAPTER_NOT_FOUND
            )
        
        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(chapter, field, value)
        
        await session.commit()
        await session.refresh(chapter)
        
        # 记录更新章节日志
        log_business_operation(
            logger=logger.logger,
            operation="update_chapter",
            details={
                "chapter_id": chapter_id,
                "novel_id": chapter.novel_id,
                "updated_fields": list(update_dict.keys()),
                "title": chapter.title
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return ChapterResponse.model_validate(chapter)
        
    except NotFoundException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"更新章节失败 - 用户ID: {user_id}, 章节ID: {chapter_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'update_chapter'
        })
        raise DatabaseException(
            message="更新章节失败",
            details={"chapter_id": chapter_id}
        )


@router.delete("/{chapter_id}", summary="删除章节")
async def delete_chapter(
    chapter_id: UUID,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    删除指定章节
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        query = select(Chapter).join(Novel).where(
            and_(
                Chapter.id == chapter_id,
                Novel.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        chapter = result.scalar_one_or_none()
        
        if not chapter:
            raise NotFoundException(
                message="章节不存在或无权限访问",
                error_code=ErrorCode.CHAPTER_NOT_FOUND
            )
        
        # 记录删除前的信息
        chapter_info = {
            "chapter_id": chapter_id,
            "novel_id": chapter.novel_id,
            "chapter_number": chapter.chapter_number,
            "title": chapter.title
        }
        
        await session.delete(chapter)
        await session.commit()
        
        # 记录删除章节日志
        log_business_operation(
            logger=logger.logger,
            operation="delete_chapter",
            details=chapter_info,
            user_id=user_id,
            request_id=request_id
        )
        
        return {"message": "章节已成功删除"}
        
    except NotFoundException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"删除章节失败 - 用户ID: {user_id}, 章节ID: {chapter_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'delete_chapter'
        })
        raise DatabaseException(
            message="删除章节失败",
            details={"chapter_id": chapter_id}
        )


@router.post("/{chapter_id}/versions", response_model=ChapterResponse, summary="创建章节版本")
async def create_chapter_version(
    chapter_id: UUID,
    request: Request,
    current_user: User = Depends(AuthService.get_current_user),
    session: AsyncSession = Depends(get_async_db)
):
    """
    为指定章节创建新版本
    """
    request_id = getattr(request.state, 'request_id', 'unknown')
    user_id = str(current_user.id)
    
    try:
        # 获取原章节
        query = select(Chapter).join(Novel).where(
            and_(
                Chapter.id == chapter_id,
                Novel.user_id == current_user.id
            )
        )
        
        result = await session.execute(query)
        original_chapter = result.scalar_one_or_none()
        
        if not original_chapter:
            raise NotFoundException(
                message="章节不存在或无权限访问",
                error_code=ErrorCode.CHAPTER_NOT_FOUND
            )
        
        # 创建新版本
        new_version = original_chapter.version + 1
        
        chapter_version = Chapter(
            novel_id=original_chapter.novel_id,
            chapter_number=original_chapter.chapter_number,
            version=new_version,
            title=original_chapter.title,
            content=original_chapter.content,
            summary=original_chapter.summary,
            status=ChapterStatus.DRAFT
        )
        
        session.add(chapter_version)
        await session.commit()
        await session.refresh(chapter_version)
        
        # 记录创建章节版本日志
        log_business_operation(
            logger=logger.logger,
            operation="create_chapter_version",
            details={
                "original_chapter_id": chapter_id,
                "new_chapter_id": chapter_version.id,
                "novel_id": original_chapter.novel_id,
                "chapter_number": original_chapter.chapter_number,
                "new_version": new_version
            },
            user_id=user_id,
            request_id=request_id
        )
        
        return ChapterResponse.model_validate(chapter_version)
        
    except NotFoundException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"创建章节版本失败 - 用户ID: {user_id}, 章节ID: {chapter_id}, 错误: {str(e)}", extra={
            'request_id': request_id,
            'user_id': user_id,
            'operation': 'create_chapter_version'
        })
        raise DatabaseException(
            message="创建章节版本失败",
            details={"chapter_id": chapter_id}
        ) 
