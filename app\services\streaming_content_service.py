#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
流式内容服务 - 管理增量内容存储和恢复
"""
import json
import hashlib
import time
import logging
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from datetime import datetime, timezone

from app.models.streaming_content import (
    StreamingContent, 
    ContentRecoveryPoint, 
    StreamingMetrics,
    ContentStatus
)

logger = logging.getLogger(__name__)


class StreamingContentService:
    """流式内容服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def start_streaming_session(self, task_id: str, stage: str) -> bool:
        """开始流式传输会话"""
        try:
            # 创建恢复点
            recovery_point = ContentRecoveryPoint(
                task_id=task_id,
                stage=stage,
                last_chunk_index=-1,
                accumulated_content="",
                total_length=0,
                is_active=True
            )
            self.session.add(recovery_point)
            
            # 创建指标记录
            metrics = StreamingMetrics(
                task_id=task_id,
                stage=stage,
                total_chunks=0,
                total_bytes=0,
                streaming_duration=0.0,
                average_chunk_size=0.0,
                chunks_per_second=0.0
            )
            self.session.add(metrics)
            
            await self.session.commit()
            logger.info(f"Started streaming session for task {task_id}, stage {stage}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start streaming session: {e}")
            await self.session.rollback()
            return False
    
    async def save_content_chunk(self, task_id: str, stage: str, chunk: str, 
                                chunk_index: int, total_length: int = 0,
                                is_final: bool = False) -> bool:
        """保存内容片段"""
        try:
            # 计算校验和
            checksum = hashlib.sha256(chunk.encode('utf-8')).hexdigest()
            
            # 创建内容片段记录
            content = StreamingContent(
                task_id=task_id,
                stage=stage,
                chunk_index=chunk_index,
                chunk_content=chunk,
                total_length=total_length,
                is_final=is_final,
                checksum=checksum,
                status=ContentStatus.COMPLETED.value if is_final else ContentStatus.STREAMING.value
            )
            self.session.add(content)
            
            # 更新恢复点
            await self._update_recovery_point(task_id, stage, chunk_index, chunk, total_length)
            
            # 更新指标
            await self._update_metrics(task_id, stage, len(chunk.encode('utf-8')))
            
            await self.session.commit()
            return True
            
        except Exception as e:
            logger.error(f"Failed to save content chunk: {e}")
            await self.session.rollback()
            return False
    
    async def get_accumulated_content(self, task_id: str, stage: str) -> Optional[str]:
        """获取累积内容"""
        try:
            # 首先尝试从恢复点获取
            recovery_query = select(ContentRecoveryPoint).where(
                and_(
                    ContentRecoveryPoint.task_id == task_id,
                    ContentRecoveryPoint.stage == stage,
                    ContentRecoveryPoint.is_active == True
                )
            ).order_by(desc(ContentRecoveryPoint.updated_at))
            
            result = await self.session.execute(recovery_query)
            recovery_point = result.scalar_one_or_none()
            
            if recovery_point:
                return recovery_point.accumulated_content
            
            # 如果没有恢复点，从内容片段重建
            return await self._rebuild_content_from_chunks(task_id, stage)
            
        except Exception as e:
            logger.error(f"Failed to get accumulated content: {e}")
            return None
    
    async def recover_streaming_session(self, task_id: str, stage: str) -> Optional[Dict[str, Any]]:
        """恢复流式传输会话"""
        try:
            # 获取恢复点
            recovery_query = select(ContentRecoveryPoint).where(
                and_(
                    ContentRecoveryPoint.task_id == task_id,
                    ContentRecoveryPoint.stage == stage,
                    ContentRecoveryPoint.is_active == True
                )
            ).order_by(desc(ContentRecoveryPoint.updated_at))
            
            result = await self.session.execute(recovery_query)
            recovery_point = result.scalar_one_or_none()
            
            if not recovery_point:
                logger.warning(f"No recovery point found for task {task_id}, stage {stage}")
                return None
            
            # 更新恢复次数
            recovery_point.recovery_count += 1
            
            # 更新指标中的恢复次数
            metrics_query = select(StreamingMetrics).where(
                and_(
                    StreamingMetrics.task_id == task_id,
                    StreamingMetrics.stage == stage
                )
            )
            metrics_result = await self.session.execute(metrics_query)
            metrics = metrics_result.scalar_one_or_none()
            
            if metrics:
                metrics.recovery_count += 1
            
            await self.session.commit()
            
            return {
                "last_chunk_index": recovery_point.last_chunk_index,
                "accumulated_content": recovery_point.accumulated_content,
                "total_length": recovery_point.total_length,
                "recovery_count": recovery_point.recovery_count
            }
            
        except Exception as e:
            logger.error(f"Failed to recover streaming session: {e}")
            await self.session.rollback()
            return None
    
    async def complete_streaming_session(self, task_id: str, stage: str, 
                                       final_content: str = None) -> bool:
        """完成流式传输会话"""
        try:
            # 更新恢复点状态
            recovery_query = select(ContentRecoveryPoint).where(
                and_(
                    ContentRecoveryPoint.task_id == task_id,
                    ContentRecoveryPoint.stage == stage,
                    ContentRecoveryPoint.is_active == True
                )
            )
            result = await self.session.execute(recovery_query)
            recovery_point = result.scalar_one_or_none()
            
            if recovery_point:
                recovery_point.is_active = False
                if final_content:
                    recovery_point.accumulated_content = final_content
                    recovery_point.total_length = len(final_content)
            
            # 更新指标
            metrics_query = select(StreamingMetrics).where(
                and_(
                    StreamingMetrics.task_id == task_id,
                    StreamingMetrics.stage == stage
                )
            )
            metrics_result = await self.session.execute(metrics_query)
            metrics = metrics_result.scalar_one_or_none()
            
            if metrics:
                metrics.completed_at = datetime.now(timezone.utc)
                metrics.completion_rate = 1.0
                
                # 计算最终指标
                if metrics.total_chunks > 0:
                    metrics.average_chunk_size = metrics.total_bytes / metrics.total_chunks
                
                duration = (metrics.completed_at - metrics.created_at).total_seconds()
                metrics.streaming_duration = duration
                
                if duration > 0:
                    metrics.chunks_per_second = metrics.total_chunks / duration
            
            await self.session.commit()
            logger.info(f"Completed streaming session for task {task_id}, stage {stage}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete streaming session: {e}")
            await self.session.rollback()
            return False
    
    async def _update_recovery_point(self, task_id: str, stage: str, 
                                   chunk_index: int, chunk: str, total_length: int):
        """更新恢复点"""
        recovery_query = select(ContentRecoveryPoint).where(
            and_(
                ContentRecoveryPoint.task_id == task_id,
                ContentRecoveryPoint.stage == stage,
                ContentRecoveryPoint.is_active == True
            )
        )
        result = await self.session.execute(recovery_query)
        recovery_point = result.scalar_one_or_none()
        
        if recovery_point:
            recovery_point.last_chunk_index = chunk_index
            recovery_point.accumulated_content += chunk
            recovery_point.total_length = total_length
    
    async def _update_metrics(self, task_id: str, stage: str, chunk_bytes: int):
        """更新指标"""
        metrics_query = select(StreamingMetrics).where(
            and_(
                StreamingMetrics.task_id == task_id,
                StreamingMetrics.stage == stage
            )
        )
        result = await self.session.execute(metrics_query)
        metrics = result.scalar_one_or_none()
        
        if metrics:
            metrics.total_chunks += 1
            metrics.total_bytes += chunk_bytes
    
    async def _rebuild_content_from_chunks(self, task_id: str, stage: str) -> str:
        """从内容片段重建完整内容"""
        chunks_query = select(StreamingContent).where(
            and_(
                StreamingContent.task_id == task_id,
                StreamingContent.stage == stage
            )
        ).order_by(StreamingContent.chunk_index)
        
        result = await self.session.execute(chunks_query)
        chunks = result.scalars().all()
        
        return "".join(chunk.chunk_content for chunk in chunks)
