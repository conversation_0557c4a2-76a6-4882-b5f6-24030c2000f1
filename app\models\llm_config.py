#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LLM配置数据模型
"""
from sqlalchemy import Column, String, Float, Integer, Boolean, DateTime, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.core.database import Base


class LLMConfig(Base):
    """LLM配置表"""
    __tablename__ = "llm_configs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # 配置基本信息
    name = Column(String(255), nullable=False)  # 配置名称
    provider = Column(String(50), nullable=False)  # openai, azure, dashscope, custom
    is_default = Column(Boolean, default=False)  # 是否为默认配置
    is_active = Column(Boolean, default=True)  # 是否启用
    
    # API配置
    api_key = Column(String(500))  # API密钥（加密存储）
    base_url = Column(String(500))  # API基础URL
    api_version = Column(String(50))  # API版本（Azure使用）
    
    # 模型参数
    model = Column(String(100), nullable=False)  # 模型名称
    temperature = Column(Float, default=0.7)  # 温度参数
    max_tokens = Column(Integer, default=2048)  # 最大token数
    top_p = Column(Float, default=1.0)  # top_p参数
    frequency_penalty = Column(Float, default=0.0)  # 频率惩罚
    presence_penalty = Column(Float, default=0.0)  # 存在惩罚
    
    # 请求配置
    timeout = Column(Integer, default=60)  # 请求超时时间（秒）
    max_retries = Column(Integer, default=3)  # 最大重试次数
    retry_delay = Column(Float, default=1.0)  # 重试延迟（秒）
    
    # 扩展配置
    custom_headers = Column(JSON)  # 自定义请求头
    custom_params = Column(JSON)  # 自定义参数
    
    # 使用统计
    total_requests = Column(Integer, default=0)  # 总请求次数
    total_tokens = Column(Integer, default=0)  # 总token使用量
    last_used_at = Column(DateTime(timezone=True))  # 最后使用时间
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="llm_configs")
    
    def __repr__(self):
        return f"<LLMConfig(id={self.id}, name={self.name}, provider={self.provider})>"


class LLMUsageLog(Base):
    """LLM使用日志表"""
    __tablename__ = "llm_usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    config_id = Column(UUID(as_uuid=True), ForeignKey("llm_configs.id", ondelete="CASCADE"), nullable=False)
    
    # 请求信息
    request_id = Column(String(100))  # 请求ID
    endpoint = Column(String(200))  # 请求端点
    method = Column(String(10))  # 请求方法
    
    # 使用统计
    prompt_tokens = Column(Integer, default=0)  # 输入token数
    completion_tokens = Column(Integer, default=0)  # 输出token数
    total_tokens = Column(Integer, default=0)  # 总token数
    
    # 性能指标
    response_time = Column(Float)  # 响应时间（秒）
    success = Column(Boolean, default=True)  # 是否成功
    error_message = Column(Text)  # 错误信息
    
    # 成本信息
    estimated_cost = Column(Float)  # 预估成本（美元）
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User")
    config = relationship("LLMConfig")
    
    def __repr__(self):
        return f"<LLMUsageLog(id={self.id}, total_tokens={self.total_tokens}, success={self.success})>" 