#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一异常处理模块
"""
import traceback
from typing import Any, Dict, Optional
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
import logging

logger = logging.getLogger(__name__)


class ErrorCode:
    """错误代码常量"""
    # 通用错误 (1000-1999)
    UNKNOWN_ERROR = "GENERAL_001"
    VALIDATION_ERROR = "GENERAL_002"
    DATABASE_ERROR = "GENERAL_003"
    PERMISSION_DENIED = "GENERAL_004"
    RESOURCE_NOT_FOUND = "GENERAL_005"
    QUOTA_EXCEEDED = "GENERAL_006"
    
    # 认证相关错误 (2000-2099)
    AUTH_TOKEN_INVALID = "AUTH_001"
    AUTH_TOKEN_EXPIRED = "AUTH_002"
    AUTH_WECHAT_LOGIN_FAILED = "AUTH_003"
    AUTH_USER_NOT_FOUND = "AUTH_004"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH_005"
    
    # 用户相关错误 (2100-2199)
    USER_NOT_FOUND = "USER_001"
    USER_QUOTA_EXCEEDED = "USER_002"
    USER_UPDATE_FAILED = "USER_003"
    
    # 小说相关错误 (2200-2299)
    NOVEL_NOT_FOUND = "NOVEL_001"
    NOVEL_CREATE_FAILED = "NOVEL_002"
    NOVEL_UPDATE_FAILED = "NOVEL_003"
    NOVEL_DELETE_FAILED = "NOVEL_004"
    NOVEL_ACCESS_DENIED = "NOVEL_005"
    
    # 章节相关错误 (2300-2399)
    CHAPTER_NOT_FOUND = "CHAPTER_001"
    CHAPTER_CREATE_FAILED = "CHAPTER_002"
    CHAPTER_UPDATE_FAILED = "CHAPTER_003"
    CHAPTER_DELETE_FAILED = "CHAPTER_004"
    CHAPTER_ACCESS_DENIED = "CHAPTER_005"
    
    # 生成任务相关错误 (2400-2499)
    GENERATION_TASK_CREATE_FAILED = "TASK_001"
    GENERATION_TASK_NOT_FOUND = "TASK_002"
    GENERATION_TASK_ALREADY_RUNNING = "TASK_003"
    GENERATION_TASK_FAILED = "TASK_004"
    GENERATION_LLM_ERROR = "TASK_005"
    GENERATION_QUOTA_EXCEEDED = "TASK_006"
    
    # LLM配置相关错误 (2500-2599)
    LLM_CONFIG_NOT_FOUND = "LLM_001"
    LLM_CONFIG_INVALID = "LLM_002"
    LLM_API_ERROR = "LLM_003"
    LLM_QUOTA_EXCEEDED = "LLM_004"


class BaseAPIException(Exception):
    """API异常基类"""
    def __init__(
        self, 
        message: str, 
        error_code: str, 
        status_code: int = status.HTTP_400_BAD_REQUEST,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(BaseAPIException):
    """数据验证异常"""
    def __init__(self, message: str = "数据验证失败", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details
        )


class NotFoundException(BaseAPIException):
    """资源不存在异常"""
    def __init__(self, message: str = "资源不存在", error_code: str = ErrorCode.RESOURCE_NOT_FOUND):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_404_NOT_FOUND
        )


class PermissionDeniedException(BaseAPIException):
    """权限拒绝异常"""
    def __init__(self, message: str = "权限不足", error_code: str = ErrorCode.PERMISSION_DENIED):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_403_FORBIDDEN
        )


class QuotaExceededException(BaseAPIException):
    """配额超限异常"""
    def __init__(self, message: str = "配额已达上限", error_code: str = ErrorCode.QUOTA_EXCEEDED):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )


class AuthenticationException(BaseAPIException):
    """认证异常"""
    def __init__(self, message: str = "认证失败", error_code: str = ErrorCode.AUTH_TOKEN_INVALID):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class DatabaseException(BaseAPIException):
    """数据库异常"""
    def __init__(self, message: str = "数据库操作失败", details: Optional[Dict] = None):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details
        )


class LLMServiceException(BaseAPIException):
    """LLM服务异常"""
    def __init__(self, message: str = "AI服务调用失败", error_code: str = ErrorCode.GENERATION_LLM_ERROR):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE
        )


class BusinessException(BaseAPIException):
    """业务逻辑异常"""
    def __init__(self, message: str, error_code: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        super().__init__(
            message=message,
            error_code=error_code,
            status_code=status_code
        )


# 全局异常处理器
async def base_api_exception_handler(request: Request, exc: BaseAPIException) -> JSONResponse:
    """处理自定义API异常"""
    logger.error(
        f"API异常 - 路径: {request.url.path}, 方法: {request.method}, "
        f"错误代码: {exc.error_code}, 消息: {exc.message}, 详情: {exc.details}"
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": exc.error_code,
                "message": exc.message,
                "details": exc.details
            },
            "timestamp": None,  # 这里会在响应中间件中设置
            "path": request.url.path
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """处理数据验证异常"""
    logger.error(f"数据验证失败 - 路径: {request.url.path}, 错误: {exc.errors()}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "success": False,
            "error": {
                "code": ErrorCode.VALIDATION_ERROR,
                "message": "请求数据验证失败",
                "details": {"validation_errors": exc.errors()}
            },
            "timestamp": None,
            "path": request.url.path
        }
    )


async def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """处理数据库异常"""
    logger.error(f"数据库异常 - 路径: {request.url.path}, 错误: {str(exc)}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": {
                "code": ErrorCode.DATABASE_ERROR,
                "message": "数据库操作失败",
                "details": {}
            },
            "timestamp": None,
            "path": request.url.path
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """处理HTTP异常"""
    logger.error(f"HTTP异常 - 路径: {request.url.path}, 状态码: {exc.status_code}, 详情: {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": {
                "code": ErrorCode.UNKNOWN_ERROR,
                "message": exc.detail,
                "details": {}
            },
            "timestamp": None,
            "path": request.url.path
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理未捕获的异常"""
    logger.error(
        f"未捕获异常 - 路径: {request.url.path}, 方法: {request.method}, "
        f"异常类型: {type(exc).__name__}, 错误: {str(exc)}, "
        f"堆栈: {traceback.format_exc()}"
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "success": False,
            "error": {
                "code": ErrorCode.UNKNOWN_ERROR,
                "message": "服务器内部错误",
                "details": {}
            },
            "timestamp": None,
            "path": request.url.path
        }
    ) 