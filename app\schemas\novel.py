#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
小说相关数据传输对象
"""
from typing import Optional, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel


class NovelCreate(BaseModel):
    """创建小说请求"""
    title: str
    description: Optional[str] = None
    genre: str
    target_length: Optional[int] = None
    style_settings: Optional[dict] = None


class NovelUpdate(BaseModel):
    """更新小说请求"""
    title: Optional[str] = None
    description: Optional[str] = None
    genre: Optional[str] = None
    target_length: Optional[int] = None
    style_settings: Optional[dict] = None
    status: Optional[str] = None


class NovelResponse(BaseModel):
    """小说响应对象"""
    id: UUID
    user_id: UUID
    title: str
    description: Optional[str] = None
    genre: str
    target_length: Optional[int] = None
    status: str
    style_settings: Optional[dict] = None
    created_at: datetime
    updated_at: datetime
    
    model_config = {"from_attributes": True}


class NovelListResponse(BaseModel):
    """小说列表响应"""
    novels: List[NovelResponse]
    total: int
    page: int
    per_page: int 
