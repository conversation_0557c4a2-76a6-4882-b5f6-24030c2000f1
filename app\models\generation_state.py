#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成状态模型 - 用于断点续传功能
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from app.core.database import Base


class GenerationState(Base):
    """生成状态模型，用于保存架构生成的中间状态"""
    __tablename__ = "generation_states"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    novel_id = Column(UUID(as_uuid=True), index=True, nullable=False)
    task_id = Column(String, index=True, nullable=False)
    generation_type = Column(String, nullable=False)  # "architecture", "chapter"
    
    # 生成状态
    current_step = Column(String, nullable=False)  # "core_seed", "character_dynamics", "world_building", "plot_architecture"
    completed_steps = Column(JSON, default={})  # 已完成的步骤和结果
    step_progress = Column(Integer, default=0)  # 当前步骤的进度
    total_progress = Column(Integer, default=0)  # 总进度
    
    # 状态标识
    is_completed = Column(Boolean, default=False)
    is_failed = Column(Boolean, default=False)
    error_message = Column(Text, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<GenerationState(id={self.id}, novel_id={self.novel_id}, step={self.current_step})>" 
