{"timestamp": "2025-06-19T08:53:32.056357Z", "level": "ERROR", "name": "app.services.auth_service", "message": "WeChat API error: {'errcode': 40013, 'errmsg': 'invalid appid, rid: 6853d00c-22f89b09-44efc264'}", "request_id": "937e50e5", "user_id": "anonymous", "operation": "unknown", "module": "auth_service", "function": "_get_openid_from_wechat", "line": 67, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T08:53:32.056357Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.auth", "message": "\u5fae\u4fe1\u767b\u5f55\u5931\u8d25 - \u9519\u8bef: ", "request_id": "9ea4e3c1", "user_id": "anonymous", "operation": "unknown", "module": "auth", "function": "wechat_login", "line": 62, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T08:53:32.057357Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5", "request_id": "dcfae1a9", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T08:53:32.057357Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/auth/wechat/login, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: AUTH_003, \u6d88\u606f: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5, \u8be6\u60c5: {}", "request_id": "2b75f186", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.728927Z", "level": "ERROR", "name": "app.services.auth_service", "message": "WeChat API error: {'errcode': 40029, 'errmsg': 'invalid code, rid: 6853d262-63904421-1b9a02fc'}", "request_id": "73e15543", "user_id": "anonymous", "operation": "unknown", "module": "auth_service", "function": "_get_openid_from_wechat", "line": 67, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.729926Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.auth", "message": "\u5fae\u4fe1\u767b\u5f55\u5931\u8d25 - \u9519\u8bef: ", "request_id": "b1aeb043", "user_id": "anonymous", "operation": "unknown", "module": "auth", "function": "wechat_login", "line": 62, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.729926Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5", "request_id": "c9e1ddf9", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.729926Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/auth/wechat/login, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: AUTH_003, \u6d88\u606f: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5, \u8be6\u60c5: {}", "request_id": "2ce9b382", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:59.256774Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.novels", "message": "\u521b\u5efa\u5c0f\u8bf4\u5931\u8d25 - \u7528\u6237ID: 1, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column \"description\" of relation \"novels\" does not exist\n[SQL: INSERT INTO novels (user_id, title, description, genre, target_length, style_settings, status, chapter_count, total_words) VALUES ($1::INTEGER, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::JSON, $7::novelstatus, $8::INTEGER, $9::INTEGER) RETURNING novels.id, novels.created_at, novels.updated_at]\n[parameters: (1, 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4', '2099\u5e74\uff0c\u4eba\u5de5\u667a\u80fd\u4e0e\u4eba\u7c7b\u5171\u5b58\u7684\u672a\u6765\u4e16\u754c\uff0c\u4e00\u4e2a\u7a0b\u5e8f\u5458\u53d1\u73b0\u4e86\u6539\u53d8\u4e16\u754c\u7684\u79d8\u5bc6', 'science_fiction', 10000, '{\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"plot_and_character\"}', 'DRAFT', 0, 0)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "f8e78c69", "user_id": "anonymous", "operation": "unknown", "module": "novels", "function": "create_novel", "line": 90, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:59.256774Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25", "request_id": "f75ce8a9", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:59.257774Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25, \u8be6\u60c5: {'title': 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4'}", "request_id": "6ffcb573", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.398951Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.novels", "message": "\u521b\u5efa\u5c0f\u8bf4\u5931\u8d25 - \u7528\u6237ID: 1, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column \"description\" of relation \"novels\" does not exist\n[SQL: INSERT INTO novels (user_id, title, description, genre, target_length, style_settings, status, chapter_count, total_words) VALUES ($1::INTEGER, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::JSON, $7::novelstatus, $8::INTEGER, $9::INTEGER) RETURNING novels.id, novels.created_at, novels.updated_at]\n[parameters: (1, 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4', '2099\u5e74\uff0c\u4eba\u5de5\u667a\u80fd\u4e0e\u4eba\u7c7b\u5171\u5b58\u7684\u672a\u6765\u4e16\u754c\uff0c\u4e00\u4e2a\u7a0b\u5e8f\u5458\u53d1\u73b0\u4e86\u6539\u53d8\u4e16\u754c\u7684\u79d8\u5bc6', 'science_fiction', 10000, '{\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"plot_and_character\"}', 'DRAFT', 0, 0)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "f678571b", "user_id": "anonymous", "operation": "unknown", "module": "novels", "function": "create_novel", "line": 90, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.400216Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25", "request_id": "269d1bb3", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.401216Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25, \u8be6\u60c5: {'title': 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4'}", "request_id": "6c673c45", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.195349Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "33652576", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.195349Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "b21bb72a", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.196866Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "5730eebc", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:29.208009Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "3ce12e87", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:29.208009Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "34e99b2e", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:29.210010Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "2bf9b51d", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:34.498708Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "80772ec4", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:34.498708Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "f87147d6", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:34.500720Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "c0b3402d", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:39.497909Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "ce623d91", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:39.497909Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "d0a4e6bf", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:39.498915Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "eedde7ec", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:44.778218Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "493e2815", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:44.779288Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "23b51fcf", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:44.780288Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "865db1a3", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:49.797217Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "aea101a6", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:49.797217Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "28870b27", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:49.799217Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "4365afc0", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:54.818976Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "6884f207", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:54.820480Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "32375420", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:54.822488Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "4c10eb44", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:00.104413Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "a8bf56cd", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:00.104933Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "654ede6a", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:00.105972Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "15e041cb", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:05.125521Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "c81fd448", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:05.126530Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "59c5d538", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:05.128034Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "294e04db", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:10.125867Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "15d04a3c", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:10.125867Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "da9d8e67", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:10.127867Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "ffb64e8b", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:15.142983Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "b6df7ace", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:15.143986Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "ec8f695f", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:15.144986Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "7f697b8d", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:20.160190Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "5f140cbd", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:20.160190Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "73d97a0c", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:20.162697Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "521d4d46", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:25.447934Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "6b1b5187", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:25.447934Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "0dd3321c", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:25.448934Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "ccb260bc", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:30.457037Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "50485536", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:30.457037Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "91ae6739", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:30.458041Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "0158c20b", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:35.474753Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "20a943ea", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:35.474753Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "ce668307", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:35.476755Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "fe0b7e4a", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:40.492811Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "94e6296e", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:40.492811Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "da365f91", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:40.495018Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "a214b24a", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:45.503673Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "0dbb293f", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:45.504677Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "86e96afa", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:45.505676Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "321f8c0d", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:50.775928Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "a641338b", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:50.776927Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "82254ab5", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:50.777926Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "e49d262a", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:55.778695Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "dda92543", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:55.778695Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "f6d8a2ab", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:55.779694Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "8e7a4df7", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:01.054431Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "2ddfaf45", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:01.055430Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "307a3b73", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:01.056429Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "e0333e4b", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:06.073161Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "d7f61248", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:06.073161Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "d635cc17", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:06.075162Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "002095df", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:11.349066Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "c8d96d6e", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:11.350066Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "e5bab984", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:11.351065Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "623c6844", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:16.366442Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "58a7e476", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:16.366959Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "43459ef5", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:16.368509Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "98443386", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:23.039934Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "863e4da6", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:23.040454Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "de7fea9f", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:23.041997Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "35ffcee4", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:28.036665Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "3e9749ad", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:28.037662Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "7eb257e5", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:28.038662Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "3b4e7e3a", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:33.331248Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "44d40e81", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:33.331248Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "df108a43", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:33.333289Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "871bd459", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:38.348043Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "3d78a789", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:38.349041Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "663a127e", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:38.350548Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "2a2259d8", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:43.366571Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "522ede6d", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:43.366571Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "ccfec353", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:43.368571Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "66e1b621", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:48.654150Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "e2e1c66d", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:48.655149Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "b75140ce", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:48.656151Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "f528ec7f", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:53.675996Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: a788917a-76e1-46c0-a919-12a813b536e2, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column generation_tasks.task_id does not exist\n[SQL: SELECT generation_tasks.id, generation_tasks.task_id, generation_tasks.user_id, generation_tasks.novel_id, generation_tasks.task_type, generation_tasks.status, generation_tasks.progress, generation_tasks.parameters, generation_tasks.result_doc_id, generation_tasks.result_chapter_id, generation_tasks.error_message, generation_tasks.created_at, generation_tasks.updated_at, generation_tasks.started_at, generation_tasks.completed_at \nFROM generation_tasks \nWHERE generation_tasks.id = $1::UUID AND generation_tasks.user_id = $2::INTEGER]\n[parameters: ('a788917a-76e1-46c0-a919-12a813b536e2', 1)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "25e4f565", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:53.676517Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "afd2f626", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:53.678061Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': 'a788917a-76e1-46c0-a919-12a813b536e2'}", "request_id": "b0b8b66b", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.856122Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "283882d6", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.858125Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a707a38f-7456-4471-8499-14592dc3f957, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "0508ebc1", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:21.867558Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "19dda779", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:21.868562Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/a707a38f-7456-4471-8499-14592dc3f957, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "782036e7", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.670595Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "99bf5393", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.672595Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/6aac93dc-e0ca-481c-881e-5e7bfa522267, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "d19e4eba", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.449165Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "e8eb46f5", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.451166Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "cbf4cdd0", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:20.462468Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "363f9b48", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:20.464560Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "9488e530", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:25.719312Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "2e96aed9", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:25.720317Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "c363fe76", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:30.739884Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "f1386a86", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:30.740882Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "034651db", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:36.017748Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "05484eca", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:36.019750Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "889fd3aa", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:41.031033Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "f0188fcc", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:41.033033Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "d6af6a24", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:46.043122Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "871ce1fc", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:46.045637Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "395cfc27", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:51.044623Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "fec10f84", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:51.045677Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "88c713fa", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:56.326127Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "904be093", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:56.328432Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "ea54c6af", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:28:01.337754Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee", "request_id": "52c15cf6", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:28:01.339754Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: TASK_002, \u6d88\u606f: \u4efb\u52a1\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u9650\u8bbf\u95ee, \u8be6\u60c5: {}", "request_id": "e9268f35", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T16:35:31.215409Z", "level": "ERROR", "name": "app.core.database", "message": "Failed to initialize database: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "51c49489", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "init_db", "line": 85, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T16:35:31.216408Z", "level": "ERROR", "name": "app.main", "message": "\u274c Database initialization failed: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "d2a2febc", "user_id": "anonymous", "operation": "unknown", "module": "main", "function": "lifespan", "line": 49, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T17:21:09.352302Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236", "request_id": "ac33eb98", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:21:09.354383Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: USER_002, \u6d88\u606f: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236, \u8be6\u60c5: {}", "request_id": "d509a899", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.164313Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: 3d77b679-cebc-441c-b5ba-131ee91fecef, \u9519\u8bef: 1 validation error for TaskResponse\nid\n  Input should be a valid string [type=string_type, input_value=UUID('3d77b679-cebc-441c-b5ba-131ee91fecef'), input_type=UUID]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "request_id": "73419906", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.164313Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "14aac2e7", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.166312Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '3d77b679-cebc-441c-b5ba-131ee91fecef'}", "request_id": "f0a0ea53", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:30.168947Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: 3d77b679-cebc-441c-b5ba-131ee91fecef, \u9519\u8bef: 1 validation error for TaskResponse\nid\n  Input should be a valid string [type=string_type, input_value=UUID('3d77b679-cebc-441c-b5ba-131ee91fecef'), input_type=UUID]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "request_id": "c6d452c2", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:30.169948Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "9306f893", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:30.170948Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '3d77b679-cebc-441c-b5ba-131ee91fecef'}", "request_id": "a26712a0", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:35.183551Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 1, \u4efb\u52a1ID: 3d77b679-cebc-441c-b5ba-131ee91fecef, \u9519\u8bef: 1 validation error for TaskResponse\nid\n  Input should be a valid string [type=string_type, input_value=UUID('3d77b679-cebc-441c-b5ba-131ee91fecef'), input_type=UUID]\n    For further information visit https://errors.pydantic.dev/2.5/v/string_type", "request_id": "3d3149ef", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:35.183551Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "bae29faa", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:35.185551Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '3d77b679-cebc-441c-b5ba-131ee91fecef'}", "request_id": "8c6abe69", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.737205Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template chapter_blueprint_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(chapter_blueprint_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('9734a731-ebce-4c7c-9a94-ae0b4eddc6e9'), 'chapter_blueprint_prompt', '\u6839\u636e\u5c0f\u8bf4\u67b6\u6784\uff1a\\n\\n{novel_architecture}\\n\\n\u8bbe\u8ba1{number_of_chapters}\u7ae0\u7684\u8282\u594f\u5206\u5e03\uff0c\u5f15\u5165\u5267\u60c5\u8282\u594f\u52a8\u6001\u8c03\u63a7\uff1a\\n\\n\u4e00\u3001\u6574\u4f53\u8282\u594f\u63a7\u5236\uff1a\\n- \u524d\u671f\uff081-{front_chapter_end}\u7ae0\uff09\uff1a\u60ac\u5ff5\u5bc6\u5ea6\u4e0a\u9650\u2605\u2605\u2605\u2606\u2606\uff0c\u91cd\u70b9\u5efa\u7acb\u4e16\u754c\u89c2\u3001\u89d2\u8272\u5173\u7cfb\\n- \u4e2d\u671f ... (856 characters truncated) ... r_of_chapters}\u7ae0\u524d\u4e0d\u8981\u51fa\u73b0\u7ed3\u5c40\u7ae0\u8282\u3002\\n- {number_of_chapters}\u7ae0\u4e3a\u7ed3\u5c40\u7ae0\u8282\u3002\\n- \u786e\u4fdd\u81f3\u5c1120%\u7684\u7ae0\u8282\u6807\u8bb0\u4e3a\"\u60c5\u611f\u6c89\u6dc0\uff1a\u662f\"\uff0c\u4e3b\u8981\u96c6\u4e2d\u5728\u4e2d\u540e\u671f\u3002\\n- \u8bbe\u8ba1\u81ea\u7136\u7684\u60c5\u611f\u4e92\u52a8\u6216\u5173\u7cfb\u8fdb\u5c55\u7684\u5951\u673a\uff0c\u907f\u514d\u751f\u786c\u7684\"\u53d1\u7cd6\"\u6216\"\u72d7\u8840\"\u3002\\n\\n\u4ec5\u7ed9\u51fa\u6700\u7ec8\u6587\u672c\uff0c\u4e0d\u8981\u89e3\u91ca\u4efb\u4f55\u5185\u5bb9\u3002', '\u9ed8\u8ba4chapter_blueprint_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "6a36e230", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.744270Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('a3db3faa-be52-4da8-81dc-2b3fc1e04984'), 'summary_prompt', '\u4ee5\u4e0b\u662f\u65b0\u5b8c\u6210\u7684\u7ae0\u8282\u6587\u672c\uff1a\\n{chapter_text}\\n\\n\u8fd9\u662f\u5f53\u524d\u7684\u5168\u5c40\u6458\u8981\uff08\u53ef\u4e3a\u7a7a\uff09\uff1a\\n{global_summary}\\n\\n\u8bf7\u6839\u636e\u672c\u7ae0\u65b0\u589e\u5185\u5bb9\uff0c\u66f4\u65b0\u5168\u5c40\u6458\u8981\u3002\\n\u8981\u6c42\uff1a\\n- \u4fdd\u7559\u65e2\u6709\u91cd\u8981\u4fe1\u606f\uff0c\u540c\u65f6\u878d\u5165\u65b0\u5267\u60c5\u8981\u70b9\\n- \u4ee5\u7b80\u6d01\u3001\u8fde\u8d2f\u7684\u8bed\u8a00\u63cf\u8ff0\u5168\u4e66\u8fdb\u5c55\\n- \u5ba2\u89c2\u63cf\u7ed8\uff0c\u4e0d\u5c55\u5f00\u8054\u60f3\u6216\u89e3\u91ca\\n- \u5b57\u6570\u63a7\u5236\u57282000\u5b57\u4ee5\u5185\\n\\n\u4ec5\u8fd4\u56de\u5168\u5c40\u6458\u8981\u6587\u672c\uff0c\u4e0d\u8981\u89e3\u91ca\u4efb\u4f55\u5185\u5bb9\u3002', '\u9ed8\u8ba4summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "8d67d1f4", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.750778Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('fa646e3d-9de7-49d8-848f-a3272fc42149'), 'summary_prompt', '\u4ee5\u4e0b\u662f\u65b0\u5b8c\u6210\u7684\u7ae0\u8282\u6587\u672c\uff1a\\n{chapter_text}\\n\\n\u8fd9\u662f\u5f53\u524d\u7684\u5168\u5c40\u6458\u8981\uff08\u53ef\u4e3a\u7a7a\uff09\uff1a\\n{global_summary}\\n\\n\u8bf7\u6839\u636e\u672c\u7ae0\u65b0\u589e\u5185\u5bb9\uff0c\u66f4\u65b0\u5168\u5c40\u6458\u8981\u3002\\n\u8981\u6c42\uff1a\\n- \u4fdd\u7559\u65e2\u6709\u91cd\u8981\u4fe1\u606f\uff0c\u540c\u65f6\u878d\u5165\u65b0\u5267\u60c5\u8981\u70b9\\n- \u4ee5\u7b80\u6d01\u3001\u8fde\u8d2f\u7684\u8bed\u8a00\u63cf\u8ff0\u5168\u4e66\u8fdb\u5c55\\n- \u5ba2\u89c2\u63cf\u7ed8\uff0c\u4e0d\u5c55\u5f00\u8054\u60f3\u6216\u89e3\u91ca\\n- \u5b57\u6570\u63a7\u5236\u57282000\u5b57\u4ee5\u5185\\n\\n\u4ec5\u8fd4\u56de\u5168\u5c40\u6458\u8981\u6587\u672c\uff0c\u4e0d\u8981\u89e3\u91ca\u4efb\u4f55\u5185\u5bb9\u3002', '\u9ed8\u8ba4summary_prompt\u6a21\u677f', 2, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "776181a6", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.758286Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template update_character_state_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(update_character_state_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('1eff03ca-a7a3-483f-8749-f6b218f4f7f9'), 'update_character_state_prompt', '\u4ee5\u4e0b\u662f\u65b0\u5b8c\u6210\u7684\u7ae0\u8282\u6587\u672c\uff1a\\n{chapter_text}\\n\\n\u8fd9\u662f\u5f53\u524d\u7684\u89d2\u8272\u72b6\u6001\u6587\u6863\uff1a\\n{old_state}\\n\\n\u8bf7\u66f4\u65b0\u4e3b\u8981\u89d2\u8272\u72b6\u6001\uff0c\u5185\u5bb9\u683c\u5f0f\uff1a\\n\u89d2\u8272A\u5c5e\u6027\uff1a\\n\u251c\u2500\u2500\u7269\u54c1:\\n    \u251c\u2500\u2500\u67d0\u7269(\u9053\u5177)\uff1a\u63cf\u8ff0\\n    \u251c\u2500\u2500XX\u957f\u5251(\u6b66\u5668)\uff1a\u63cf\u8ff0\\n    ...\\n\u251c\u2500\u2500\u80fd\u529b\\n  ... (738 characters truncated) ... \u5df2\u57cb\u8bbe\u4f0f\u7b14\uff1a[\u5217\u51fa\u6240\u6709\u5df2\u57cb\u8bbe\u4f46\u5c1a\u672a\u56de\u6536\u7684\u4f0f\u7b14]\\n- \u5df2\u56de\u6536\u4f0f\u7b14\uff1a[\u5217\u51fa\u5df2\u7ecf\u56de\u6536\u7684\u4f0f\u7b14]\\n- \u5f85\u56de\u6536\u4f18\u5148\u7ea7\uff1a[\u6807\u6ce8\u54ea\u4e9b\u4f0f\u7b14\u5e94\u4f18\u5148\u5728\u540e\u7eed\u7ae0\u8282\u4e2d\u56de\u6536]\\n\\n\u8981\u6c42\uff1a\\n- \u8bf7\u76f4\u63a5\u5728\u5df2\u6709\u6587\u6863\u57fa\u7840\u4e0a\u8fdb\u884c\u589e\u5220\\n- \u4e0d\u6539\u53d8\u539f\u6709\u7ed3\u6784\uff0c\u8bed\u8a00\u5c3d\u91cf\u7b80\u6d01\u3001\u6709\u6761\u7406\\n\\n\u4ec5\u8fd4\u56de\u66f4\u65b0\u540e\u7684\u89d2\u8272\u72b6\u6001\u6587\u672c\uff0c\u4e0d\u8981\u89e3\u91ca\u4efb\u4f55\u5185\u5bb9\u3002', '\u9ed8\u8ba4update_character_state_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "14db96be", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.760794Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template update_character_state_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(update_character_state_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('e36593fe-1c00-466b-aed2-b5f71dd86dc7'), 'update_character_state_prompt', '\u4ee5\u4e0b\u662f\u65b0\u5b8c\u6210\u7684\u7ae0\u8282\u6587\u672c\uff1a\\n{chapter_text}\\n\\n\u8fd9\u662f\u5f53\u524d\u7684\u89d2\u8272\u72b6\u6001\u6587\u6863\uff1a\\n{old_state}\\n\\n\u8bf7\u66f4\u65b0\u4e3b\u8981\u89d2\u8272\u72b6\u6001\uff0c\u5185\u5bb9\u683c\u5f0f\uff1a\\n\u89d2\u8272A\u5c5e\u6027\uff1a\\n\u251c\u2500\u2500\u7269\u54c1:\\n    \u251c\u2500\u2500\u67d0\u7269(\u9053\u5177)\uff1a\u63cf\u8ff0\\n    \u251c\u2500\u2500XX\u957f\u5251(\u6b66\u5668)\uff1a\u63cf\u8ff0\\n    ...\\n\u251c\u2500\u2500\u80fd\u529b\\n  ... (738 characters truncated) ... \u5df2\u57cb\u8bbe\u4f0f\u7b14\uff1a[\u5217\u51fa\u6240\u6709\u5df2\u57cb\u8bbe\u4f46\u5c1a\u672a\u56de\u6536\u7684\u4f0f\u7b14]\\n- \u5df2\u56de\u6536\u4f0f\u7b14\uff1a[\u5217\u51fa\u5df2\u7ecf\u56de\u6536\u7684\u4f0f\u7b14]\\n- \u5f85\u56de\u6536\u4f18\u5148\u7ea7\uff1a[\u6807\u6ce8\u54ea\u4e9b\u4f0f\u7b14\u5e94\u4f18\u5148\u5728\u540e\u7eed\u7ae0\u8282\u4e2d\u56de\u6536]\\n\\n\u8981\u6c42\uff1a\\n- \u8bf7\u76f4\u63a5\u5728\u5df2\u6709\u6587\u6863\u57fa\u7840\u4e0a\u8fdb\u884c\u589e\u5220\\n- \u4e0d\u6539\u53d8\u539f\u6709\u7ed3\u6784\uff0c\u8bed\u8a00\u5c3d\u91cf\u7b80\u6d01\u3001\u6709\u6761\u7406\\n\\n\u4ec5\u8fd4\u56de\u66f4\u65b0\u540e\u7684\u89d2\u8272\u72b6\u6001\u6587\u672c\uff0c\u4e0d\u8981\u89e3\u91ca\u4efb\u4f55\u5185\u5bb9\u3002', '\u9ed8\u8ba4update_character_state_prompt\u6a21\u677f', 2, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "362a0b7b", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.770304Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template first_chapter_draft_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(first_chapter_draft_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('c722325f-248e-453f-a9ea-32f6c9231c02'), 'first_chapter_draft_prompt', '\u5373\u5c06\u521b\u4f5c\uff1a\u7b2c {novel_number} \u7ae0\u300a{chapter_title}\u300b\\n\u672c\u7ae0\u5b9a\u4f4d\uff1a{chapter_role}\\n\u6838\u5fc3\u4f5c\u7528\uff1a{chapter_purpose}\\n\u60ac\u5ff5\u5bc6\u5ea6\uff1a{suspense_level}\\n\u4f0f\u7b14\u64cd\u4f5c\uff1a{foreshadowing} \\n\u8ba4\u77e5\u98a0\u8986\uff1a{plot_twist ... (1164 characters truncated) ... \\n\\n\u683c\u5f0f\u8981\u6c42\uff1a\\n- \u4ec5\u8fd4\u56de\u7ae0\u8282\u6b63\u6587\u6587\u672c\uff1b\\n- \u4e0d\u4f7f\u7528\u5206\u7ae0\u8282\u5c0f\u6807\u9898\uff1b\\n- \u4e0d\u8981\u4f7f\u7528markdown\u683c\u5f0f\uff1b\\n- \u907f\u514d\u4f7f\u7528\u8fc7\u4e8e\u7f51\u7edc\u5316\u3001\u7f3a\u4e4f\u6587\u5b66\u6027\u7684\u8bcd\u8bed\uff0c\u8ffd\u6c42\u6709\u8d28\u611f\u7684\u53d9\u4e8b\u98ce\u683c\u3002\\n- \u4e0d\u8981\u5728\u6b63\u6587\u4e2d\u6ce8\u91ca\u4f0f\u7b14\u64cd\u4f5c\u3001\u8ba4\u77e5\u98a0\u8986\u3001\u60c5\u611f\u6c89\u6dc0\u7b49\u3002\\n\\n\u989d\u5916\u6307\u5bfc(\u53ef\u80fd\u672a\u6307\u5b9a)\uff1a{user_guidance}', '\u9ed8\u8ba4first_chapter_draft_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "6b3c87f0", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.772305Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template first_chapter_draft_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(first_chapter_draft_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('13adb121-a179-4b44-bd0d-72ae8fa4b371'), 'first_chapter_draft_prompt', '\u5373\u5c06\u521b\u4f5c\uff1a\u7b2c {novel_number} \u7ae0\u300a{chapter_title}\u300b\\n\u672c\u7ae0\u5b9a\u4f4d\uff1a{chapter_role}\\n\u6838\u5fc3\u4f5c\u7528\uff1a{chapter_purpose}\\n\u60ac\u5ff5\u5bc6\u5ea6\uff1a{suspense_level}\\n\u4f0f\u7b14\u64cd\u4f5c\uff1a{foreshadowing} \\n\u8ba4\u77e5\u98a0\u8986\uff1a{plot_twist ... (1164 characters truncated) ... \\n\\n\u683c\u5f0f\u8981\u6c42\uff1a\\n- \u4ec5\u8fd4\u56de\u7ae0\u8282\u6b63\u6587\u6587\u672c\uff1b\\n- \u4e0d\u4f7f\u7528\u5206\u7ae0\u8282\u5c0f\u6807\u9898\uff1b\\n- \u4e0d\u8981\u4f7f\u7528markdown\u683c\u5f0f\uff1b\\n- \u907f\u514d\u4f7f\u7528\u8fc7\u4e8e\u7f51\u7edc\u5316\u3001\u7f3a\u4e4f\u6587\u5b66\u6027\u7684\u8bcd\u8bed\uff0c\u8ffd\u6c42\u6709\u8d28\u611f\u7684\u53d9\u4e8b\u98ce\u683c\u3002\\n- \u4e0d\u8981\u5728\u6b63\u6587\u4e2d\u6ce8\u91ca\u4f0f\u7b14\u64cd\u4f5c\u3001\u8ba4\u77e5\u98a0\u8986\u3001\u60c5\u611f\u6c89\u6dc0\u7b49\u3002\\n\\n\u989d\u5916\u6307\u5bfc(\u53ef\u80fd\u672a\u6307\u5b9a)\uff1a{user_guidance}', '\u9ed8\u8ba4first_chapter_draft_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "0ad18cf7", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.785329Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template next_chapter_draft_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(next_chapter_draft_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('aefeef90-d143-4c53-bbca-dd6d4fda63dd'), 'next_chapter_draft_prompt', '\u6838\u5fc3\u8981\u6c42\uff1a\u6587\u7b14\u7ec6\u817b\u751f\u52a8\uff0c\u60c5\u8282\u5f15\u4eba\u5165\u80dc\uff0c\u6ce8\u91cd\u6c1b\u56f4\u8425\u9020\u548c\u771f\u5b9e\u60c5\u611f\u8868\u8fbe\u3002\u53d9\u4e8b\u5e94\u6709\u5f20\u529b\uff0c\u907f\u514d\u5e73\u94fa\u76f4\u53d9\u548c\u6d41\u6c34\u8d26\u3002\\n\\n\u53c2\u8003\u6587\u6863\uff1a\\n- \u5c0f\u8bf4\u8bbe\u5b9a\uff1a\\n{novel_setting}\\n\\n- \u5168\u5c40\u6458\u8981\uff1a\\n{global_summary}\\n\\n- \u89d2\u8272\u72b6\u6001\uff1a\\n{character_state}\\n\\n ... (1546 characters truncated) ... \u5c0f\u6807\u9898\uff1b\\n- \u4e0d\u8981\u4f7f\u7528markdown\u683c\u5f0f\uff1b\\n- \u907f\u514d\u4f7f\u7528\u8fc7\u4e8e\u7f51\u7edc\u5316\u3001\u7f3a\u4e4f\u6587\u5b66\u6027\u7684\u8bcd\u8bed\uff0c\u8ffd\u6c42\u6709\u8d28\u611f\u7684\u53d9\u4e8b\u98ce\u683c\u3002\\n- \u4e0d\u8981\u5728\u6b63\u6587\u4e2d\u6ce8\u91ca\u4f0f\u7b14\u64cd\u4f5c\u3001\u8ba4\u77e5\u98a0\u8986\u3001\u60c5\u611f\u6c89\u6dc0\u7b49\u3002\\n\\n\u6ce8\u610f\uff1a\u8fd4\u56de\u7684\u662f\u7eaf\u6b63\u6587\u6587\u672c\uff0c\u4e0d\u8981\u5305\u542b\u4efb\u4f55\u89e3\u91ca\u3001\u8bf4\u660e\u3001\u5907\u6ce8\u7b49\u3002\\n\\n\u989d\u5916\u6307\u5bfc(\u53ef\u80fd\u672a\u6307\u5b9a)\uff1a{user_guidance}', '\u9ed8\u8ba4next_chapter_draft_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "c821f8e9", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.786329Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template next_chapter_draft_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(next_chapter_draft_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('d20f53d6-10f0-46ae-bcf9-b2df44968c71'), 'next_chapter_draft_prompt', '\u6838\u5fc3\u8981\u6c42\uff1a\u6587\u7b14\u7ec6\u817b\u751f\u52a8\uff0c\u60c5\u8282\u5f15\u4eba\u5165\u80dc\uff0c\u6ce8\u91cd\u6c1b\u56f4\u8425\u9020\u548c\u771f\u5b9e\u60c5\u611f\u8868\u8fbe\u3002\u53d9\u4e8b\u5e94\u6709\u5f20\u529b\uff0c\u907f\u514d\u5e73\u94fa\u76f4\u53d9\u548c\u6d41\u6c34\u8d26\u3002\\n\\n\u53c2\u8003\u6587\u6863\uff1a\\n- \u5c0f\u8bf4\u8bbe\u5b9a\uff1a\\n{novel_setting}\\n\\n- \u5168\u5c40\u6458\u8981\uff1a\\n{global_summary}\\n\\n- \u89d2\u8272\u72b6\u6001\uff1a\\n{character_state}\\n\\n ... (1546 characters truncated) ... \u5c0f\u6807\u9898\uff1b\\n- \u4e0d\u8981\u4f7f\u7528markdown\u683c\u5f0f\uff1b\\n- \u907f\u514d\u4f7f\u7528\u8fc7\u4e8e\u7f51\u7edc\u5316\u3001\u7f3a\u4e4f\u6587\u5b66\u6027\u7684\u8bcd\u8bed\uff0c\u8ffd\u6c42\u6709\u8d28\u611f\u7684\u53d9\u4e8b\u98ce\u683c\u3002\\n- \u4e0d\u8981\u5728\u6b63\u6587\u4e2d\u6ce8\u91ca\u4f0f\u7b14\u64cd\u4f5c\u3001\u8ba4\u77e5\u98a0\u8986\u3001\u60c5\u611f\u6c89\u6dc0\u7b49\u3002\\n\\n\u6ce8\u610f\uff1a\u8fd4\u56de\u7684\u662f\u7eaf\u6b63\u6587\u6587\u672c\uff0c\u4e0d\u8981\u5305\u542b\u4efb\u4f55\u89e3\u91ca\u3001\u8bf4\u660e\u3001\u5907\u6ce8\u7b49\u3002\\n\\n\u989d\u5916\u6307\u5bfc(\u53ef\u80fd\u672a\u6307\u5b9a)\uff1a{user_guidance}', '\u9ed8\u8ba4next_chapter_draft_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "156b7c63", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.796842Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template immediate_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(immediate_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('c2f750f8-9a86-457b-ad35-99bd63e3ec51'), 'immediate_summary_prompt', '\u8bf7\u4e3a\u4ee5\u4e0b\u6700\u8fd1\u7ae0\u8282\u751f\u6210\u8be6\u7ec6\u7684\u5373\u65f6\u6458\u8981\uff1a\\n\\n\u7ae0\u8282\u5185\u5bb9\uff1a\\n{recent_chapters_content}\\n\\n\u7ae0\u8282\u8303\u56f4\uff1a{chapter_range}\\n\\n\u8981\u6c42\uff1a\\n1. \u8be6\u7ec6\u8bb0\u5f55\u6bcf\u7ae0\u7684\u5173\u952e\u60c5\u8282\u70b9\u548c\u8f6c\u6298\\n2. \u8ddf\u8e2a\u89d2\u8272\u72b6\u6001\u548c\u60c5\u611f\u53d8\u5316\u7684\u7ec6\u8282\\n3. \u6807\u6ce8\u91cd\u8981\u5bf9\u8bdd\u548c\u884c\u52a8\u7684\u5177\u4f53\u5185\u5bb9\\n4. \u8bb0\u5f55\u6240\u6709\u4f0f\u7b14\u548c\u7ebf\u7d22\u7684\u57cb\u8bbe\u4e0e\u56de\u6536\\n5. \u8ffd\u8e2a\u89d2\u8272\u5173\u7cfb\u7684\u5fae\u5999\u53d8\u5316\\n6. \u5b57\u6570\u63a7\u5236\u5728800\u5b57\u4ee5\u5185\\n\\n\u8bf7\u6309\u7ae0\u8282\u5206\u522b\u603b\u7ed3\uff0c\u683c\u5f0f\uff1a\\n\u7b2cX\u7ae0\uff1a[\u8be6\u7ec6\u6458\u8981\uff0c\u5305\u542b\u5177\u4f53\u60c5\u8282\u3001\u5bf9\u8bdd\u8981\u70b9\u3001\u89d2\u8272\u72b6\u6001\u53d8\u5316]\\n\\n\u6ce8\u610f\uff1a\u8fd9\u662f\u5373\u65f6\u6458\u8981\uff0c\u9700\u8981\u4fdd\u7559\u8db3\u591f\u7684\u7ec6\u8282\u4f9b\u540e\u7eed\u7ae0\u8282\u751f\u6210\u53c2\u8003\u3002', '\u9ed8\u8ba4immediate_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "6ee937b5", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.797841Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template immediate_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(immediate_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('2cfcdd83-8df3-469f-a48b-ea48159eb79f'), 'immediate_summary_prompt', '\u8bf7\u4e3a\u4ee5\u4e0b\u6700\u8fd1\u7ae0\u8282\u751f\u6210\u8be6\u7ec6\u7684\u5373\u65f6\u6458\u8981\uff1a\\n\\n\u7ae0\u8282\u5185\u5bb9\uff1a\\n{recent_chapters_content}\\n\\n\u7ae0\u8282\u8303\u56f4\uff1a{chapter_range}\\n\\n\u8981\u6c42\uff1a\\n1. \u8be6\u7ec6\u8bb0\u5f55\u6bcf\u7ae0\u7684\u5173\u952e\u60c5\u8282\u70b9\u548c\u8f6c\u6298\\n2. \u8ddf\u8e2a\u89d2\u8272\u72b6\u6001\u548c\u60c5\u611f\u53d8\u5316\u7684\u7ec6\u8282\\n3. \u6807\u6ce8\u91cd\u8981\u5bf9\u8bdd\u548c\u884c\u52a8\u7684\u5177\u4f53\u5185\u5bb9\\n4. \u8bb0\u5f55\u6240\u6709\u4f0f\u7b14\u548c\u7ebf\u7d22\u7684\u57cb\u8bbe\u4e0e\u56de\u6536\\n5. \u8ffd\u8e2a\u89d2\u8272\u5173\u7cfb\u7684\u5fae\u5999\u53d8\u5316\\n6. \u5b57\u6570\u63a7\u5236\u5728800\u5b57\u4ee5\u5185\\n\\n\u8bf7\u6309\u7ae0\u8282\u5206\u522b\u603b\u7ed3\uff0c\u683c\u5f0f\uff1a\\n\u7b2cX\u7ae0\uff1a[\u8be6\u7ec6\u6458\u8981\uff0c\u5305\u542b\u5177\u4f53\u60c5\u8282\u3001\u5bf9\u8bdd\u8981\u70b9\u3001\u89d2\u8272\u72b6\u6001\u53d8\u5316]\\n\\n\u6ce8\u610f\uff1a\u8fd9\u662f\u5373\u65f6\u6458\u8981\uff0c\u9700\u8981\u4fdd\u7559\u8db3\u591f\u7684\u7ec6\u8282\u4f9b\u540e\u7eed\u7ae0\u8282\u751f\u6210\u53c2\u8003\u3002', '\u9ed8\u8ba4immediate_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "9eef1f49", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.809353Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template medium_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(medium_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('6f168d10-5ef3-4f6c-881e-0b4713121ebc'), 'medium_summary_prompt', '\u57fa\u4e8e\u4ee5\u4e0b\u8be6\u7ec6\u7684\u5373\u65f6\u6458\u8981\uff0c\u751f\u6210\u4e2d\u7b49\u8be6\u7ec6\u5ea6\u7684\u4e2d\u671f\u6458\u8981\uff1a\\n\\n\u5373\u65f6\u6458\u8981\u5185\u5bb9\uff1a\\n{immediate_summaries}\\n\\n\u8981\u6c42\uff1a\\n1. \u6982\u62ec\u4e3b\u8981\u60c5\u8282\u53d1\u5c55\u8109\u7edc\\n2. \u603b\u7ed3\u89d2\u8272\u5173\u7cfb\u53d8\u5316\\n3. \u68b3\u7406\u91cd\u8981\u51b2\u7a81\u548c\u8f6c\u6298\u70b9\\n4. \u4fdd\u7559\u5173\u952e\u4f0f\u7b14\u548c\u7ebf\u7d22\\n5. \u5b57\u6570\u63a7\u5236\u57281200\u5b57\u4ee5\u5185\\n\\n\u8bf7\u751f\u6210\u8fde\u8d2f\u7684\u4e2d\u671f\u53d1\u5c55\u6458\u8981\uff1a', '\u9ed8\u8ba4medium_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "feb6b017", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.809353Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template medium_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(medium_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('e4c69b20-100b-40b6-a6c9-4f3931997a0c'), 'medium_summary_prompt', '\u57fa\u4e8e\u4ee5\u4e0b\u8be6\u7ec6\u7684\u5373\u65f6\u6458\u8981\uff0c\u751f\u6210\u4e2d\u7b49\u8be6\u7ec6\u5ea6\u7684\u4e2d\u671f\u6458\u8981\uff1a\\n\\n\u5373\u65f6\u6458\u8981\u5185\u5bb9\uff1a\\n{immediate_summaries}\\n\\n\u8981\u6c42\uff1a\\n1. \u6982\u62ec\u4e3b\u8981\u60c5\u8282\u53d1\u5c55\u8109\u7edc\\n2. \u603b\u7ed3\u89d2\u8272\u5173\u7cfb\u53d8\u5316\\n3. \u68b3\u7406\u91cd\u8981\u51b2\u7a81\u548c\u8f6c\u6298\u70b9\\n4. \u4fdd\u7559\u5173\u952e\u4f0f\u7b14\u548c\u7ebf\u7d22\\n5. \u5b57\u6570\u63a7\u5236\u57281200\u5b57\u4ee5\u5185\\n\\n\u8bf7\u751f\u6210\u8fde\u8d2f\u7684\u4e2d\u671f\u53d1\u5c55\u6458\u8981\uff1a', '\u9ed8\u8ba4medium_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "838c7a1c", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.820864Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template global_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(global_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('fd1db3d2-e8db-4afe-a8e5-d4b0486e6af7'), 'global_summary_prompt', '\u57fa\u4e8e\u5f53\u524d\u5168\u5c40\u6458\u8981\u548c\u6700\u65b0\u7ae0\u8282\u4fe1\u606f\uff0c\u66f4\u65b0\u5168\u5c40\u6458\u8981\uff1a\\n\\n\u5f53\u524d\u5168\u5c40\u6458\u8981\uff1a\\n{current_global_summary}\\n\\n\u6700\u65b0\u53d1\u5c55\u4fe1\u606f\uff1a\\n{latest_developments}\\n\\n\u8981\u6c42\uff1a\\n1. \u4fdd\u6301\u5168\u4e66\u53d1\u5c55\u7684\u5b8f\u89c2\u89c6\u89d2\\n2. \u7a81\u51fa\u4e3b\u8981\u6545\u4e8b\u7ebf\u548c\u89d2\u8272\u5f27\u7ebf\\n3. \u8bb0\u5f55\u91cd\u5927\u8f6c\u6298\u548c\u9ad8\u6f6e\u70b9\\n4. \u7ef4\u62a4\u6574\u4f53\u53d9\u4e8b\u903b\u8f91\\n5. \u5b57\u6570\u63a7\u5236\u57281500\u5b57\u4ee5\u5185\\n\\n\u8bf7\u751f\u6210\u66f4\u65b0\u540e\u7684\u5168\u5c40\u6458\u8981\uff1a', '\u9ed8\u8ba4global_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "37731193", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.821864Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template global_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(global_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('2fa81152-d0c7-4b8a-9336-897d2fd7bc9d'), 'global_summary_prompt', '\u57fa\u4e8e\u5f53\u524d\u5168\u5c40\u6458\u8981\u548c\u6700\u65b0\u7ae0\u8282\u4fe1\u606f\uff0c\u66f4\u65b0\u5168\u5c40\u6458\u8981\uff1a\\n\\n\u5f53\u524d\u5168\u5c40\u6458\u8981\uff1a\\n{current_global_summary}\\n\\n\u6700\u65b0\u53d1\u5c55\u4fe1\u606f\uff1a\\n{latest_developments}\\n\\n\u8981\u6c42\uff1a\\n1. \u4fdd\u6301\u5168\u4e66\u53d1\u5c55\u7684\u5b8f\u89c2\u89c6\u89d2\\n2. \u7a81\u51fa\u4e3b\u8981\u6545\u4e8b\u7ebf\u548c\u89d2\u8272\u5f27\u7ebf\\n3. \u8bb0\u5f55\u91cd\u5927\u8f6c\u6298\u548c\u9ad8\u6f6e\u70b9\\n4. \u7ef4\u62a4\u6574\u4f53\u53d9\u4e8b\u903b\u8f91\\n5. \u5b57\u6570\u63a7\u5236\u57281500\u5b57\u4ee5\u5185\\n\\n\u8bf7\u751f\u6210\u66f4\u65b0\u540e\u7684\u5168\u5c40\u6458\u8981\uff1a', '\u9ed8\u8ba4global_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "5814738d", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.833884Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template character_development_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(character_development_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('7c36e1f4-7fc2-405f-9493-d18f128d3fec'), 'character_development_summary_prompt', '\u57fa\u4e8e\u5f53\u524d\u89d2\u8272\u53d1\u5c55\u6458\u8981\u548c\u65b0\u7ae0\u8282\u5185\u5bb9\uff0c\u66f4\u65b0\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a\\n\\n\u5f53\u524d\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a\\n{current_character_summary}\\n\\n\u65b0\u7ae0\u8282\u5185\u5bb9\uff1a\\n{new_chapter_content}\\n\\n\u8981\u6c42\uff1a\\n1. \u8ddf\u8e2a\u4e3b\u8981\u89d2\u8272\u7684\u6210\u957f\u8f68\u8ff9\\n2. \u8bb0\u5f55\u89d2\u8272\u5173\u7cfb\u7684\u91cd\u8981\u53d8\u5316\\n3. \u603b\u7ed3\u89d2\u8272\u7684\u5173\u952e\u51b3\u7b56\u548c\u8f6c\u53d8\\n4. \u8ffd\u8e2a\u89d2\u8272\u7684\u60c5\u611f\u53d1\u5c55\\n5. \u5b57\u6570\u63a7\u5236\u57281000\u5b57\u4ee5\u5185\\n\\n\u8bf7\u66f4\u65b0\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a', '\u9ed8\u8ba4character_development_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "ed4f20e4", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:07:18.834884Z", "level": "ERROR", "name": "app.services.prompt_service", "message": "Failed to update template character_development_summary_prompt: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint \"prompt_templates_name_key\"\nDETAIL:  Key (name)=(character_development_summary_prompt) already exists.\n[SQL: INSERT INTO prompt_templates (id, name, template, description, version, is_active) VALUES ($1::UUID, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::INTEGER, $6::BOOLEAN) RETURNING prompt_templates.created_at, prompt_templates.updated_at]\n[parameters: (UUID('678ce6e0-b961-41ae-9a77-82b46d4f0830'), 'character_development_summary_prompt', '\u57fa\u4e8e\u5f53\u524d\u89d2\u8272\u53d1\u5c55\u6458\u8981\u548c\u65b0\u7ae0\u8282\u5185\u5bb9\uff0c\u66f4\u65b0\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a\\n\\n\u5f53\u524d\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a\\n{current_character_summary}\\n\\n\u65b0\u7ae0\u8282\u5185\u5bb9\uff1a\\n{new_chapter_content}\\n\\n\u8981\u6c42\uff1a\\n1. \u8ddf\u8e2a\u4e3b\u8981\u89d2\u8272\u7684\u6210\u957f\u8f68\u8ff9\\n2. \u8bb0\u5f55\u89d2\u8272\u5173\u7cfb\u7684\u91cd\u8981\u53d8\u5316\\n3. \u603b\u7ed3\u89d2\u8272\u7684\u5173\u952e\u51b3\u7b56\u548c\u8f6c\u53d8\\n4. \u8ffd\u8e2a\u89d2\u8272\u7684\u60c5\u611f\u53d1\u5c55\\n5. \u5b57\u6570\u63a7\u5236\u57281000\u5b57\u4ee5\u5185\\n\\n\u8bf7\u66f4\u65b0\u89d2\u8272\u53d1\u5c55\u6458\u8981\uff1a', '\u9ed8\u8ba4character_development_summary_prompt\u6a21\u677f', 1, True)]\n(Background on this error at: https://sqlalche.me/e/20/gkpj)", "request_id": "0d5d932b", "user_id": "anonymous", "operation": "unknown", "module": "prompt_service", "function": "update_template", "line": 104, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:57.071110Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.novels", "message": "\u521b\u5efa\u5c0f\u8bf4\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidTextRepresentationError'>: invalid input value for enum novelstatus: \"DRAFT\"\n[SQL: INSERT INTO novels (id, user_id, title, description, genre, target_length, style_settings, status, chapter_count, total_words) VALUES ($1::UUID, $2::UUID, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::INTEGER, $7::JSON, $8::novelstatus, $9::INTEGER, $10::INTEGER) RETURNING novels.created_at, novels.updated_at]\n[parameters: (UUID('80a2d3c4-1321-451d-bf96-452f8731bfa8'), UUID('17051f79-239d-47c0-bee8-93fb6b441c9a'), 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4', '2099\u5e74\uff0c\u4eba\u5de5\u667a\u80fd\u4e0e\u4eba\u7c7b\u5171\u5b58\u7684\u672a\u6765\u4e16\u754c\uff0c\u4e00\u4e2a\u7a0b\u5e8f\u5458\u53d1\u73b0\u4e86\u6539\u53d8\u4e16\u754c\u7684\u79d8\u5bc6', 'science_fiction', 10000, '{\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"plot_and_character\"}', 'DRAFT', 0, 0)]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)", "request_id": "82b310bc", "user_id": "anonymous", "operation": "unknown", "module": "novels", "function": "create_novel", "line": 91, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:57.072111Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25", "request_id": "56a3bf1a", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:57.073110Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25, \u8be6\u60c5: {'title': 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4'}", "request_id": "856edcd5", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:01.674678Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.novels", "message": "\u521b\u5efa\u5c0f\u8bf4\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidTextRepresentationError'>: invalid input value for enum novelstatus: \"DRAFT\"\n[SQL: INSERT INTO novels (id, user_id, title, description, genre, target_length, style_settings, status, chapter_count, total_words) VALUES ($1::UUID, $2::UUID, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::INTEGER, $7::JSON, $8::novelstatus, $9::INTEGER, $10::INTEGER) RETURNING novels.created_at, novels.updated_at]\n[parameters: (UUID('3a8e9d6c-7177-4a13-b682-879ae6d83615'), UUID('17051f79-239d-47c0-bee8-93fb6b441c9a'), 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4', '2099\u5e74\uff0c\u4eba\u5de5\u667a\u80fd\u4e0e\u4eba\u7c7b\u5171\u5b58\u7684\u672a\u6765\u4e16\u754c\uff0c\u4e00\u4e2a\u7a0b\u5e8f\u5458\u53d1\u73b0\u4e86\u6539\u53d8\u4e16\u754c\u7684\u79d8\u5bc6', 'science_fiction', 10000, '{\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"plot_and_character\"}', 'DRAFT', 0, 0)]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)", "request_id": "c81cdb2d", "user_id": "anonymous", "operation": "unknown", "module": "novels", "function": "create_novel", "line": 91, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:01.674678Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25", "request_id": "6c536c03", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:01.675682Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25, \u8be6\u60c5: {'title': 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4'}", "request_id": "c53bcaa4", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:34.489677Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.novels", "message": "\u521b\u5efa\u5c0f\u8bf4\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidTextRepresentationError'>: invalid input value for enum novelstatus: \"DRAFT\"\n[SQL: INSERT INTO novels (id, user_id, title, description, genre, target_length, style_settings, status, chapter_count, total_words) VALUES ($1::UUID, $2::UUID, $3::VARCHAR, $4::VARCHAR, $5::VARCHAR, $6::INTEGER, $7::JSON, $8::novelstatus, $9::INTEGER, $10::INTEGER) RETURNING novels.created_at, novels.updated_at]\n[parameters: (UUID('fddf24d9-44b9-498e-a222-afa9208f1e38'), UUID('17051f79-239d-47c0-bee8-93fb6b441c9a'), 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4', '2099\u5e74\uff0c\u4eba\u5de5\u667a\u80fd\u4e0e\u4eba\u7c7b\u5171\u5b58\u7684\u672a\u6765\u4e16\u754c\uff0c\u4e00\u4e2a\u7a0b\u5e8f\u5458\u53d1\u73b0\u4e86\u6539\u53d8\u4e16\u754c\u7684\u79d8\u5bc6', 'science_fiction', 10000, '{\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"plot_and_character\"}', 'DRAFT', 0, 0)]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)", "request_id": "5fed8ced", "user_id": "anonymous", "operation": "unknown", "module": "novels", "function": "create_novel", "line": 91, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:34.490680Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25", "request_id": "14327b3b", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:34.490680Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u521b\u5efa\u5c0f\u8bf4\u5931\u8d25, \u8be6\u60c5: {'title': 'AI\u79d1\u5e7b\u6d4b\u8bd5\u5c0f\u8bf4'}", "request_id": "c8b90ae9", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.394522Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.generation", "message": "\u751f\u6210\u67b6\u6784\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidTextRepresentationError'>: invalid input value for enum tasktype: \"GENERATE_ARCHITECTURE\"\n[SQL: INSERT INTO generation_tasks (id, task_id, user_id, novel_id, task_type, status, progress, parameters, result_doc_id, result_chapter_id, error_message, started_at, completed_at) VALUES ($1::UUID, $2::VARCHAR, $3::UUID, $4::UUID, $5::tasktype, $6::taskstatus, $7::INTEGER, $8::VARCHAR, $9::UUID, $10::UUID, $11::VARCHAR, $12::TIMESTAMP WITH TIME ZONE, $13::TIMESTAMP WITH TIME ZONE) RETURNING generation_tasks.created_at, generation_tasks.updated_at]\n[parameters: (UUID('dc42b5af-d146-4625-b195-09d2693f0a44'), '14d006d8-cd51-4cc9-828c-cb37e00d37f6', UUID('17051f79-239d-47c0-bee8-93fb6b441c9a'), UUID('4b034326-6904-4617-bdbc-43023f0f69ca'), 'GENERATE_ARCHITECTURE', 'PENDING', 0, '{\"theme\": \"2099\\\\u5e74\\\\uff0c\\\\u4eba\\\\u5de5\\\\u667a\\\\u80fd\\\\u4e0e\\\\u4eba\\\\u7c7b\\\\u5171\\\\u5b58\\\\u7684\\\\u672a\\\\u6765\\\\u4e16\\\\u754c\\\\uff0c\\\\u4e00\\\\u4e2a\\ ... (111 characters truncated) ... fiction\", \"target_length\": 10000, \"style_preferences\": {\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"character_development\"}}', None, None, None, None, None)]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)", "request_id": "9379cadd", "user_id": "anonymous", "operation": "unknown", "module": "generation", "function": "generate_architecture", "line": 149, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.394522Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u751f\u6210\u67b6\u6784\u5931\u8d25", "request_id": "8f06bfdb", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.395521Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: TASK_001, \u6d88\u606f: \u751f\u6210\u67b6\u6784\u5931\u8d25, \u8be6\u60c5: {}", "request_id": "0beb56f6", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:52.108719Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.generation", "message": "\u751f\u6210\u67b6\u6784\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.Error) <class 'asyncpg.exceptions.InvalidTextRepresentationError'>: invalid input value for enum tasktype: \"GENERATE_ARCHITECTURE\"\n[SQL: INSERT INTO generation_tasks (id, task_id, user_id, novel_id, task_type, status, progress, parameters, result_doc_id, result_chapter_id, error_message, started_at, completed_at) VALUES ($1::UUID, $2::VARCHAR, $3::UUID, $4::UUID, $5::tasktype, $6::taskstatus, $7::INTEGER, $8::VARCHAR, $9::UUID, $10::UUID, $11::VARCHAR, $12::TIMESTAMP WITH TIME ZONE, $13::TIMESTAMP WITH TIME ZONE) RETURNING generation_tasks.created_at, generation_tasks.updated_at]\n[parameters: (UUID('4c77e17a-cc61-4abb-908a-a5e46642746a'), '164c117e-02ae-4e46-b5ac-125ef11b9836', UUID('17051f79-239d-47c0-bee8-93fb6b441c9a'), UUID('a5a46d39-43f3-4ac6-afb1-1fc40fe62077'), 'GENERATE_ARCHITECTURE', 'PENDING', 0, '{\"theme\": \"2099\\\\u5e74\\\\uff0c\\\\u4eba\\\\u5de5\\\\u667a\\\\u80fd\\\\u4e0e\\\\u4eba\\\\u7c7b\\\\u5171\\\\u5b58\\\\u7684\\\\u672a\\\\u6765\\\\u4e16\\\\u754c\\\\uff0c\\\\u4e00\\\\u4e2a\\ ... (111 characters truncated) ... fiction\", \"target_length\": 10000, \"style_preferences\": {\"style\": \"\\\\u7ec6\\\\u817b\\\\u79d1\\\\u5e7b\", \"pace\": \"medium\", \"focus\": \"character_development\"}}', None, None, None, None, None)]\n(Background on this error at: https://sqlalche.me/e/20/dbapi)", "request_id": "5bb07e9d", "user_id": "anonymous", "operation": "unknown", "module": "generation", "function": "generate_architecture", "line": 149, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:52.109752Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u751f\u6210\u67b6\u6784\u5931\u8d25", "request_id": "9dc146fc", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:52.110275Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: TASK_001, \u6d88\u606f: \u751f\u6210\u67b6\u6784\u5931\u8d25, \u8be6\u60c5: {}", "request_id": "c0f1ea8c", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:11:22.412233Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "6cb6cc21", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1035, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:20:27.676015Z", "level": "ERROR", "name": "app.services.auth_service", "message": "WeChat API error: {'errcode': 40013, 'errmsg': 'invalid appid, rid: 6854710d-54a0dc37-0591676d'}", "request_id": "895a265c", "user_id": "anonymous", "operation": "unknown", "module": "auth_service", "function": "_get_openid_from_wechat", "line": 68, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:20:27.677070Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.auth", "message": "\u5fae\u4fe1\u767b\u5f55\u5931\u8d25 - \u9519\u8bef: ", "request_id": "acd3521b", "user_id": "anonymous", "operation": "unknown", "module": "auth", "function": "wechat_login", "line": 63, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:20:27.677604Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5", "request_id": "d946a076", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:20:27.678644Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/auth/wechat/login, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: AUTH_003, \u6d88\u606f: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5, \u8be6\u60c5: {}", "request_id": "bdad3010", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:26.047043Z", "level": "ERROR", "name": "app.services.auth_service", "message": "WeChat API error: {'errcode': 40013, 'errmsg': 'invalid appid, rid: 68547183-71113113-2842ad3b'}", "request_id": "4df7d9dd", "user_id": "anonymous", "operation": "unknown", "module": "auth_service", "function": "_get_openid_from_wechat", "line": 68, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:26.048085Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.auth", "message": "\u5fae\u4fe1\u767b\u5f55\u5931\u8d25 - \u9519\u8bef: ", "request_id": "c278f6dd", "user_id": "anonymous", "operation": "unknown", "module": "auth", "function": "wechat_login", "line": 63, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:26.048607Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5", "request_id": "17cade5c", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:26.049127Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/auth/wechat/login, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: AUTH_003, \u6d88\u606f: \u767b\u5f55\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5, \u8be6\u60c5: {}", "request_id": "c13f79e6", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:39.095318Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236", "request_id": "bd87bf0d", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:39.097321Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: USER_002, \u6d88\u606f: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236, \u8be6\u60c5: {}", "request_id": "13acde28", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:03.515726Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "73a8f620", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1135, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:04.510107Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Architecture generation failed: Novel not found", "request_id": "0b72d966", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_generate_novel_architecture_async", "line": 388, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:04.527107Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Task a06a89fc-a9d9-4608-b3a3-5bd10be66d6f failed with exception: Novel not found", "request_id": "a90da28a", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "on_failure", "line": 45, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:43.516280Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "2f04d9d9", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1135, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:43:15.886922Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "52fc5ac9", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1135, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:22.033832Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.generation", "message": "\u751f\u6210\u67b6\u6784\u5931\u8d25 - \u7528\u6237ID: c72308be-62a3-4a77-bbff-2df453f9a3d6, \u9519\u8bef: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column \"current_stage\" of relation \"generation_tasks\" does not exist\n[SQL: INSERT INTO generation_tasks (id, task_id, user_id, novel_id, task_type, status, progress, parameters, result_doc_id, result_chapter_id, error_message, current_stage, detailed_progress, result_data, started_at, completed_at) VALUES ($1::UUID, $2::VARCHAR, $3::UUID, $4::UUID, $5::tasktype, $6::taskstatus, $7::INTEGER, $8::VARCHAR, $9::UUID, $10::UUID, $11::VARCHAR, $12::VARCHAR, $13::VARCHAR, $14::VARCHAR, $15::TIMESTAMP WITH TIME ZONE, $16::TIMESTAMP WITH TIME ZONE) RETURNING generation_tasks.created_at, generation_tasks.updated_at]\n[parameters: (UUID('9f8be037-65fa-4e0e-8610-aaac85a9fea9'), '4f74134d-cbec-48ae-bb29-5d70ed6b41e8', UUID('c72308be-62a3-4a77-bbff-2df453f9a3d6'), UUID('a753bd95-e13a-44e6-876d-ea95992fe6df'), 'generate_architecture', 'PENDING', 0, '{\"theme\": \"2099\\\\u5e74\\\\uff0cAI\\\\u4e0e\\\\u4eba\\\\u7c7b\\\\u534f\\\\u4f5c\\\\u63a2\\\\u7d22\\\\u5b87\\\\u5b99\\\\u7684\\\\u5192\\\\u9669\\\\u6545\\\\u4e8b\", \"genre\": \"science_fiction\", \"target_length\": 8000, \"style_preferences\": {\"style\": \"\\\\u5b9e\\\\u65f6\\\\u79d1\\\\u5e7b\", \"pace\": \"fast\", \"focus\": \"streaming_development\"}}', None, None, None, None, None, None, None, None)]\n(Background on this error at: https://sqlalche.me/e/20/f405)", "request_id": "8d818801", "user_id": "anonymous", "operation": "unknown", "module": "generation", "function": "generate_architecture", "line": 149, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:22.034352Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u751f\u6210\u67b6\u6784\u5931\u8d25", "request_id": "71f2f928", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:22.035396Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: TASK_001, \u6d88\u606f: \u751f\u6210\u67b6\u6784\u5931\u8d25, \u8be6\u60c5: {}", "request_id": "b757ad97", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:31.342650Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"1/4\", \"step_na...: \"\u6838\u5fc3\u79cd\u5b50\u751f\u6210\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "da477239", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:31.343174Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "2b3ac926", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:31.344736Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "6ddfd55a", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:36.362526Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"1/4\", \"step_na...: \"\u6838\u5fc3\u79cd\u5b50\u751f\u6210\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "5ee87be4", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:36.363529Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "46eabe06", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:36.365526Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "e79c8573", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:41.618106Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"1/4\", \"step_na...: \"\u6838\u5fc3\u79cd\u5b50\u751f\u6210\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "e40d71b8", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:41.618619Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "799365d6", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:41.620164Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "2b6036a4", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:46.607477Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "b31b7fa6", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:46.607477Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "02ce98ab", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:46.609478Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "8b743197", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:51.902399Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "83deb400", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:51.903398Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "c22e8c3f", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:51.905399Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "0881e144", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:56.915690Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "253a8fcf", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:56.916690Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "aa861741", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:56.917690Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "47e6a227", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:02.175228Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "68c06a5e", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:02.175228Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "31d57dcf", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:02.177224Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "ce05ee70", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:07.199939Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "e29004fc", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:07.200939Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "5380e1fc", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:07.201939Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "27d52b46", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:12.479685Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "20973fda", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:12.480207Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "abb8aa68", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:12.481751Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "475dafeb", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:17.496925Z", "level": "ERROR", "name": "app.api.api_v1.endpoints.tasks", "message": "\u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25 - \u7528\u6237ID: 17051f79-239d-47c0-bee8-93fb6b441c9a, \u4efb\u52a1ID: 15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u9519\u8bef: 1 validation error for TaskResponse\ndetailed_progress\n  Input should be a valid dictionary [type=dict_type, input_value='{\"step\": \"2/4\", \"step_na...\u89d2\u8272\u52a8\u529b\u5b66\u6784\u5efa\"}', input_type=str]\n    For further information visit https://errors.pydantic.dev/2.5/v/dict_type", "request_id": "c4f0142e", "user_id": "anonymous", "operation": "unknown", "module": "tasks", "function": "get_task_detail", "line": 143, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:17.497925Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25", "request_id": "292ba288", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:17.498924Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2, \u65b9\u6cd5: GET, \u9519\u8bef\u4ee3\u7801: GENERAL_003, \u6d88\u606f: \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\u5931\u8d25, \u8be6\u60c5: {'task_id': '15ad44e9-5f47-4efa-9708-9b4cd313cef2'}", "request_id": "d2622c38", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:05:02.860336Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "c67d3a94", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1165, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:19.269769Z", "level": "ERROR", "name": "app.services.llm_service", "message": "OpenAI stream API call failed: ", "request_id": "883cfcbf", "user_id": "anonymous", "operation": "unknown", "module": "llm_service", "function": "stream_invoke", "line": 96, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:02:08.712214Z", "level": "ERROR", "name": "app.core.exceptions", "message": "\u672a\u6355\u83b7\u5f02\u5e38 - \u8def\u5f84: /, \u65b9\u6cd5: GET, \u5f02\u5e38\u7c7b\u578b: RuntimeError, \u9519\u8bef: Response content longer than Content-Length, \u5806\u6808: Traceback (most recent call last):\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 162, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\trustedhost.py\", line 51, in __call__\n    await self.app(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\cors.py\", line 83, in __call__\n    await self.app(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 109, in __call__\n    await response(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 270, in __call__\n    async with anyio.create_task_group() as task_group:\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 597, in __aexit__\n    raise exceptions[0]\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 273, in wrap\n    await func()\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 134, in stream_response\n    return await super().stream_response(send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 265, in stream_response\n    await send({\"type\": \"http.response.body\", \"body\": chunk, \"more_body\": True})\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 159, in _send\n    await send(message)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py\", line 560, in send\n    raise RuntimeError(\"Response content longer than Content-Length\")\nRuntimeError: Response content longer than Content-Length\n", "request_id": "4c6dd3f8", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "general_exception_handler", "line": 248, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:02:33.774274Z", "level": "ERROR", "name": "app.core.exceptions", "message": "\u672a\u6355\u83b7\u5f02\u5e38 - \u8def\u5f84: /, \u65b9\u6cd5: GET, \u5f02\u5e38\u7c7b\u578b: RuntimeError, \u9519\u8bef: Response content longer than Content-Length, \u5806\u6808: Traceback (most recent call last):\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 162, in __call__\n    await self.app(scope, receive, _send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\trustedhost.py\", line 51, in __call__\n    await self.app(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\cors.py\", line 83, in __call__\n    await self.app(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 109, in __call__\n    await response(scope, receive, send)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 270, in __call__\n    async with anyio.create_task_group() as task_group:\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py\", line 597, in __aexit__\n    raise exceptions[0]\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 273, in wrap\n    await func()\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\base.py\", line 134, in stream_response\n    return await super().stream_response(send)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\responses.py\", line 265, in stream_response\n    await send({\"type\": \"http.response.body\", \"body\": chunk, \"more_body\": True})\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\starlette\\middleware\\errors.py\", line 159, in _send\n    await send(message)\n  File \"E:\\Ecode\\AInovel_v2\\ai_novel_mini_program\\AI_NovelGenerator\\novel_ai_env\\Lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py\", line 560, in send\n    raise RuntimeError(\"Response content longer than Content-Length\")\nRuntimeError: Response content longer than Content-Length\n", "request_id": "2dc9ab6e", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "general_exception_handler", "line": 248, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:47:36.968195Z", "level": "ERROR", "name": "app.core.database", "message": "Failed to initialize database: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "505adf7e", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "init_db", "line": 85, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:47:36.978199Z", "level": "ERROR", "name": "app.main", "message": "\u274c Database initialization failed: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "8abc8ea7", "user_id": "anonymous", "operation": "unknown", "module": "main", "function": "lifespan", "line": 49, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:52:41.000076Z", "level": "ERROR", "name": "app.core.database", "message": "Failed to initialize database: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "d23b2ece", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "init_db", "line": 85, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:52:41.001076Z", "level": "ERROR", "name": "app.main", "message": "\u274c Database initialization failed: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "ba0d552b", "user_id": "anonymous", "operation": "unknown", "module": "main", "function": "lifespan", "line": 49, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:31:40.593358Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "c8c47873", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1201, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:31.606341Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Architecture generation failed: 'core_seed'", "request_id": "31495d40", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_generate_novel_architecture_async", "line": 419, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:31.639217Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Task 0be64cd7-5d86-4d3d-959a-4ae22e97080c failed with exception: 'core_seed'", "request_id": "5b485d69", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "on_failure", "line": 46, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:46:43.759641Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236", "request_id": "0de0cef8", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:46:43.760643Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/generation/architecture, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: USER_002, \u6d88\u606f: \u5df2\u8fbe\u5230\u751f\u6210\u914d\u989d\u9650\u5236, \u8be6\u60c5: {}", "request_id": "1c21c43d", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:50:24.476443Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Failed to update task status: Unconsumed column names: result", "request_id": "ec7e6113", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_update_task_status", "line": 1201, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:55:43.874196Z", "level": "ERROR", "name": "app.core.database", "message": "Failed to initialize database: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "149b6d19", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "init_db", "line": 85, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:55:43.875197Z", "level": "ERROR", "name": "app.main", "message": "\u274c Database initialization failed: Multiple exceptions: [Errno 10061] Connect call failed ('::1', 5432, 0, 0), [Errno 10061] Connect call failed ('127.0.0.1', 5432)", "request_id": "43af37e9", "user_id": "anonymous", "operation": "unknown", "module": "main", "function": "lifespan", "line": 49, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T19:06:46.754814Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Architecture generation failed: 'core_seed'", "request_id": "103eb09a", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "_generate_novel_architecture_async", "line": 419, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T19:06:46.785171Z", "level": "ERROR", "name": "app.services.generation_tasks", "message": "Task 6bae5dc7-347c-4e31-9a2e-e3ea0620c2f2 failed with exception: 'core_seed'", "request_id": "b658d0ce", "user_id": "anonymous", "operation": "unknown", "module": "generation_tasks", "function": "on_failure", "line": 46, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T19:09:12.517619Z", "level": "ERROR", "name": "app.core.database", "message": "Database session error: \u5df2\u8fbe\u5230\u5c0f\u8bf4\u521b\u5efa\u914d\u989d\u9650\u5236", "request_id": "59c21257", "user_id": "anonymous", "operation": "unknown", "module": "database", "function": "get_async_db", "line": 64, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T19:09:12.518619Z", "level": "ERROR", "name": "app.core.exceptions", "message": "API\u5f02\u5e38 - \u8def\u5f84: /api/v1/novels/, \u65b9\u6cd5: POST, \u9519\u8bef\u4ee3\u7801: USER_002, \u6d88\u606f: \u5df2\u8fbe\u5230\u5c0f\u8bf4\u521b\u5efa\u914d\u989d\u9650\u5236, \u8be6\u60c5: {}", "request_id": "a4b8d113", "user_id": "anonymous", "operation": "unknown", "module": "exceptions", "function": "base_api_exception_handler", "line": 169, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
