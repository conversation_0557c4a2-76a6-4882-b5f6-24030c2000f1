"""add llm config tables

Revision ID: add_llm_config_tables
Revises: fcab069334cc
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_llm_config_tables'
down_revision = 'fcab069334cc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create llm_configs table
    op.create_table('llm_configs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=True),
        sa.Column('api_key', sa.String(length=500), nullable=True),
        sa.Column('base_url', sa.String(length=500), nullable=True),
        sa.Column('api_version', sa.String(length=50), nullable=True),
        sa.Column('model', sa.String(length=100), nullable=False),
        sa.Column('temperature', sa.Float(), nullable=True),
        sa.Column('max_tokens', sa.Integer(), nullable=True),
        sa.Column('top_p', sa.Float(), nullable=True),
        sa.Column('frequency_penalty', sa.Float(), nullable=True),
        sa.Column('presence_penalty', sa.Float(), nullable=True),
        sa.Column('timeout', sa.Integer(), nullable=True),
        sa.Column('max_retries', sa.Integer(), nullable=True),
        sa.Column('retry_delay', sa.Float(), nullable=True),
        sa.Column('custom_headers', sa.JSON(), nullable=True),
        sa.Column('custom_params', sa.JSON(), nullable=True),
        sa.Column('total_requests', sa.Integer(), nullable=True),
        sa.Column('total_tokens', sa.Integer(), nullable=True),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_llm_configs_id'), 'llm_configs', ['id'], unique=False)
    op.create_index(op.f('ix_llm_configs_user_id'), 'llm_configs', ['user_id'], unique=False)
    op.create_index(op.f('ix_llm_configs_is_default'), 'llm_configs', ['is_default'], unique=False)
    op.create_index(op.f('ix_llm_configs_provider'), 'llm_configs', ['provider'], unique=False)

    # Create llm_usage_logs table
    op.create_table('llm_usage_logs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('config_id', sa.Integer(), nullable=False),
        sa.Column('request_id', sa.String(length=100), nullable=True),
        sa.Column('endpoint', sa.String(length=200), nullable=True),
        sa.Column('method', sa.String(length=10), nullable=True),
        sa.Column('prompt_tokens', sa.Integer(), nullable=True),
        sa.Column('completion_tokens', sa.Integer(), nullable=True),
        sa.Column('total_tokens', sa.Integer(), nullable=True),
        sa.Column('response_time', sa.Float(), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('estimated_cost', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['config_id'], ['llm_configs.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_llm_usage_logs_id'), 'llm_usage_logs', ['id'], unique=False)
    op.create_index(op.f('ix_llm_usage_logs_user_id'), 'llm_usage_logs', ['user_id'], unique=False)
    op.create_index(op.f('ix_llm_usage_logs_config_id'), 'llm_usage_logs', ['config_id'], unique=False)
    op.create_index(op.f('ix_llm_usage_logs_created_at'), 'llm_usage_logs', ['created_at'], unique=False)


def downgrade() -> None:
    # Drop llm_usage_logs table
    op.drop_index(op.f('ix_llm_usage_logs_created_at'), table_name='llm_usage_logs')
    op.drop_index(op.f('ix_llm_usage_logs_config_id'), table_name='llm_usage_logs')
    op.drop_index(op.f('ix_llm_usage_logs_user_id'), table_name='llm_usage_logs')
    op.drop_index(op.f('ix_llm_usage_logs_id'), table_name='llm_usage_logs')
    op.drop_table('llm_usage_logs')
    
    # Drop llm_configs table
    op.drop_index(op.f('ix_llm_configs_provider'), table_name='llm_configs')
    op.drop_index(op.f('ix_llm_configs_is_default'), table_name='llm_configs')
    op.drop_index(op.f('ix_llm_configs_user_id'), table_name='llm_configs')
    op.drop_index(op.f('ix_llm_configs_id'), table_name='llm_configs')
    op.drop_table('llm_configs') 