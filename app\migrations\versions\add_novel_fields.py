"""add novel description, target_length, style_settings fields

Revision ID: add_novel_fields
Revises: fcab069334cc
Create Date: 2025-01-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON


# revision identifiers, used by Alembic.
revision = 'add_novel_fields'
down_revision = 'fcab069334cc'
branch_labels = None
depends_on = None


def upgrade():
    """添加Novel模型缺失的字段"""
    # 添加description字段
    op.add_column('novels', sa.Column('description', sa.Text(), nullable=True, comment='小说简介'))
    
    # 添加target_length字段
    op.add_column('novels', sa.Column('target_length', sa.Integer(), nullable=True, comment='目标字数'))
    
    # 添加style_settings字段
    op.add_column('novels', sa.Column('style_settings', JSON(), nullable=True, comment='风格设置JSON'))


def downgrade():
    """回滚：删除添加的字段"""
    op.drop_column('novels', 'style_settings')
    op.drop_column('novels', 'target_length')
    op.drop_column('novels', 'description') 