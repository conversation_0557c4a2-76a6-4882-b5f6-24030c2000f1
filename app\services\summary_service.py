"""
多级摘要管理服务
基于认知科学的分层记忆模型，实现immediate/medium/global三层摘要管理
"""

import logging
from typing import Dict, List, Optional, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc
from app.models.document import Document, DocumentType, SummaryLevel
from app.models.chapter import Chapter

logger = logging.getLogger(__name__)


class HierarchicalSummaryService:
    """分层摘要管理服务"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def update_all_summaries(self, novel_id: UUID, new_chapter_number: int, new_chapter_content: str):
        """更新所有层级的摘要"""
        try:
            # 1. 更新即时摘要（最近章）
            await self._update_immediate_summary(novel_id, new_chapter_number, new_chapter_content)
            
            # 2. 更新中期摘要（前10章）
            await self._update_medium_summary(novel_id, new_chapter_number)
            
            # 3. 更新全局摘要
            await self._update_global_summary(novel_id, new_chapter_number)
            
            # 4. 更新角色发展摘要
            await self._update_character_summary(novel_id, new_chapter_content)
            
            logger.info(f"Successfully updated all summaries for novel {novel_id}, chapter {new_chapter_number}")
            
        except Exception as e:
            logger.error(f"Failed to update summaries: {e}")
            try:
                await self.session.rollback()
            except Exception as rollback_error:
                logger.error(f"Failed to rollback session: {rollback_error}")
            raise
    
    async def _update_immediate_summary(self, novel_id: UUID, chapter_number: int, new_content: str):
        """更新即时摘要（最近章详细摘要）"""
        try:
            from app.services.llm_service import llm_service
            from app.services.prompt_service import PromptService
            
            # 获取最近章内容
            recent_chapters = await self._get_recent_chapters(novel_id, chapter_number, 3)
            
            if not recent_chapters:
                return
            
            # 构建即时摘要内容
            combined_content = "\n\n".join([
                f"第{ch.chapter_number}章：{ch.content}" 
                for ch in recent_chapters
            ])
            
            prompt_service = PromptService(self.session)
            
            # 使用专门的即时摘要提示词
            immediate_summary_prompt = await prompt_service.format_prompt(
                "immediate_summary_prompt",
                recent_chapters_content=combined_content,
                chapter_range=f"第{recent_chapters[0].chapter_number}-{recent_chapters[-1].chapter_number}章"
            )
            
            if not immediate_summary_prompt:
                # 使用简单提示词
                immediate_summary_prompt = f"""请为以下最近章节生成详细摘要：

{combined_content[:2000]}

要求：
1. 详细记录每章的关键情节点
2. 跟踪角色状态和情感变化
3. 标注重要对话和行动
4. 记录伏笔和线索
5. 字数控制在800字以内

请按章节分别总结，格式：
第X章：[详细摘要]
"""
            
            new_summary = await llm_service.invoke_with_retry(immediate_summary_prompt)
            
            # 保存即时摘要
            await self._save_summary(
                novel_id=novel_id,
                summary_level=SummaryLevel.IMMEDIATE,
                content=new_summary,
                chapter_start=recent_chapters[0].chapter_number,
                chapter_end=recent_chapters[-1].chapter_number,
                title=f"即时摘要（第{recent_chapters[0].chapter_number}-{recent_chapters[-1].chapter_number}章）"
            )
            
        except Exception as e:
            logger.error(f"Failed to update immediate summary: {e}")
    
    async def _update_medium_summary(self, novel_id: UUID, current_chapter: int):
        """更新中期摘要（前10章中等详细度摘要）"""
        try:
            # 每5章更新一次中期摘要
            if current_chapter % 5 != 0:
                return
            
            from app.services.llm_service import llm_service
            
            # 获取所有即时摘要
            immediate_summaries = await self._get_summaries_by_level(novel_id, SummaryLevel.IMMEDIATE)
            
            if len(immediate_summaries) < 2:
                return
            
            # 合并即时摘要
            combined_immediate = "\n\n".join([summary.content for summary in immediate_summaries[-3:]])  # 最近3个即时摘要
            
            medium_summary_prompt = f"""基于以下详细的即时摘要，生成中等详细度的中期摘要：

{combined_immediate}

要求：
1. 概括主要情节发展脉络
2. 总结角色关系变化
3. 梳理重要冲突和转折点
4. 保留关键伏笔和线索
5. 字数控制在1200字以内

请生成连贯的中期发展摘要："""
            
            new_medium_summary = await llm_service.invoke_with_retry(medium_summary_prompt)
            
            # 保存中期摘要
            chapter_start = immediate_summaries[-3].chapter_range_start if len(immediate_summaries) >= 3 else immediate_summaries[0].chapter_range_start
            await self._save_summary(
                novel_id=novel_id,
                summary_level=SummaryLevel.MEDIUM,
                content=new_medium_summary,
                chapter_start=chapter_start,
                chapter_end=current_chapter,
                title=f"中期摘要（第{chapter_start}-{current_chapter}章）"
            )
            
        except Exception as e:
            logger.error(f"Failed to update medium summary: {e}")
    
    async def _update_global_summary(self, novel_id: UUID, current_chapter: int):
        """更新全局摘要（高度概括的全书摘要）"""
        try:
            from app.services.llm_service import llm_service
            
            # 获取当前全局摘要
            current_global = await self._get_latest_summary(novel_id, SummaryLevel.GLOBAL)
            
            # 获取最新的中期摘要
            latest_medium = await self._get_latest_summary(novel_id, SummaryLevel.MEDIUM)
            
            # 获取最新的即时摘要
            latest_immediate = await self._get_latest_summary(novel_id, SummaryLevel.IMMEDIATE)
            
            # 构建更新提示
            update_content = ""
            if latest_medium:
                update_content += f"最新中期摘要：\n{latest_medium.content}\n\n"
            if latest_immediate:
                update_content += f"最新即时摘要：\n{latest_immediate.content}\n\n"
            
            if not update_content:
                return
            
            current_global_content = current_global.content if current_global else ""
            
            global_summary_prompt = f"""基于当前全局摘要和最新章节信息，更新全局摘要：

当前全局摘要：
{current_global_content}

最新发展信息：
{update_content}

要求：
1. 保持全书发展的宏观视角
2. 突出主要故事线和角色弧线
3. 记录重大转折和高潮点
4. 维护整体叙事逻辑
5. 字数控制在1500字以内

请生成更新后的全局摘要："""
            
            new_global_summary = await llm_service.invoke_with_retry(global_summary_prompt)
            
            # 保存全局摘要
            await self._save_summary(
                novel_id=novel_id,
                summary_level=SummaryLevel.GLOBAL,
                content=new_global_summary,
                chapter_start=1,
                chapter_end=current_chapter,
                title=f"全局摘要（第1-{current_chapter}章）"
            )
            
        except Exception as e:
            logger.error(f"Failed to update global summary: {e}")
    
    async def _update_character_summary(self, novel_id: UUID, new_chapter_content: str):
        """更新角色发展摘要"""
        try:
            from app.services.llm_service import llm_service
            
            # 获取当前角色摘要
            current_char_summary = await self._get_latest_summary(novel_id, SummaryLevel.CHARACTER)
            
            current_char_content = current_char_summary.content if current_char_summary else ""
            
            character_summary_prompt = f"""基于当前角色发展摘要和新章节内容，更新角色发展摘要：

当前角色发展摘要：
{current_char_content}

新章节内容：
{new_chapter_content[:1000]}

要求：
1. 跟踪主要角色的成长轨迹
2. 记录角色关系的重要变化
3. 总结角色的关键决策和转变
4. 追踪角色的情感发展
5. 字数控制在1000字以内

请更新角色发展摘要："""
            
            new_character_summary = await llm_service.invoke_with_retry(character_summary_prompt)
            
            # 保存角色摘要
            await self._save_summary(
                novel_id=novel_id,
                summary_level=SummaryLevel.CHARACTER,
                content=new_character_summary,
                chapter_start=1,
                chapter_end=None,  # 角色摘要不限制章节范围
                title="角色发展摘要"
            )
            
        except Exception as e:
            logger.error(f"Failed to update character summary: {e}")
    
    async def _get_recent_chapters(self, novel_id: UUID, current_chapter: int, count: int) -> List[Chapter]:
        """获取最近的N章内容"""
        start_chapter = max(1, current_chapter - count + 1)
        
        query = select(Chapter).where(
            and_(
                Chapter.novel_id == novel_id,
                Chapter.chapter_number >= start_chapter,
                Chapter.chapter_number <= current_chapter
            )
        ).order_by(Chapter.chapter_number)
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def _get_summaries_by_level(self, novel_id: UUID, level: SummaryLevel) -> List[Document]:
        """获取指定级别的所有摘要"""
        query = select(Document).where(
            and_(
                Document.novel_id == novel_id,
                Document.summary_level == level
            )
        ).order_by(desc(Document.updated_at))
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def _get_latest_summary(self, novel_id: UUID, level: SummaryLevel) -> Optional[Document]:
        """获取指定级别的最新摘要"""
        query = select(Document).where(
            and_(
                Document.novel_id == novel_id,
                Document.summary_level == level
            )
        ).order_by(desc(Document.updated_at)).limit(1)
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def _save_summary(self, novel_id: UUID, summary_level: SummaryLevel, content: str,
                      chapter_start: int, chapter_end: Optional[int], title: str):
        """保存摘要到数据库"""
        try:
            # 查找是否存在相同级别的现有摘要
            existing_query = select(Document).where(
                and_(
                    Document.novel_id == novel_id,
                    Document.doc_type == DocumentType.SUMMARY,
                    Document.summary_level == summary_level
                )
            ).order_by(desc(Document.created_at))
            
            result = await self.session.execute(existing_query)
            existing_summaries = result.scalars().all()
            
            # 如果存在相同级别的摘要，将旧的标记为非活跃
            for existing in existing_summaries:
                existing.is_active = False
            
            # 创建新摘要
            new_summary = Document(
                novel_id=novel_id,
                doc_type=DocumentType.SUMMARY,
                summary_level=summary_level,
                title=title,
                content=content,
                chapter_range_start=chapter_start,
                chapter_range_end=chapter_end,
                is_active=True
            )
            
            self.session.add(new_summary)
            await self.session.commit()
            
            logger.info(f"Saved {summary_level.value} summary for novel {novel_id}")
            
        except Exception as e:
            logger.error(f"Failed to save summary: {e}")
            try:
                await self.session.rollback()
            except Exception as rollback_error:
                logger.error(f"Failed to rollback session: {rollback_error}")
            raise
    
    async def get_context_summaries(self, novel_id: UUID, current_chapter: int) -> Dict[str, str]:
        """获取用于生成上下文的多级摘要"""
        summaries = {}
        
        try:
            # 获取即时摘要
            immediate = await self._get_latest_summary(novel_id, SummaryLevel.IMMEDIATE)
            if immediate:
                summaries["immediate_summary"] = immediate.content
            
            # 获取中期摘要
            medium = await self._get_latest_summary(novel_id, SummaryLevel.MEDIUM)
            if medium:
                summaries["medium_summary"] = medium.content
            
            # 获取全局摘要
            global_summary = await self._get_latest_summary(novel_id, SummaryLevel.GLOBAL)
            if global_summary:
                summaries["global_summary"] = global_summary.content
            
            # 获取角色摘要
            character = await self._get_latest_summary(novel_id, SummaryLevel.CHARACTER)
            if character:
                summaries["character_summary"] = character.content
            
            return summaries
            
        except Exception as e:
            logger.error(f"Failed to get context summaries: {e}")
            return {}


# 即时摘要提示词模板
IMMEDIATE_SUMMARY_PROMPT = """请为以下最近章节生成详细摘要：

章节内容：
{recent_chapters_content}

章节范围：{chapter_range}

要求：
1. 详细记录每章的关键情节点和转折
2. 跟踪角色状态和情感变化的细节
3. 标注重要对话和行动的具体内容
4. 记录所有伏笔和线索的埋设与回收
5. 追踪角色关系的微妙变化
6. 字数控制在800字以内

请按章节分别总结，格式：
第X章：[详细摘要，包含具体情节、对话要点、角色状态变化]

注意：这是即时摘要，需要保留足够的细节供后续章节生成参考。""" 
