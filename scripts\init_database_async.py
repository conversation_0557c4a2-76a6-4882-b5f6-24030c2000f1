#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异步数据库初始化脚本
完全使用异步引擎，避免驱动冲突
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine, Base, AsyncSessionLocal
from app.core.config import settings

# 导入所有模型以确保它们被注册
from app.models import (
    user, novel, chapter, document, 
    generation_task, generation_state, prompt_template
)

async def create_database_tables():
    """使用异步引擎创建数据库表"""
    try:
        print(f"🔗 连接到数据库: {settings.DATABASE_URL}")
        
        # 测试数据库连接
        async with engine.begin() as conn:
            result = await conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✅ 数据库连接成功: {version[:50]}...")
            
            # 创建所有表
            print("📋 创建数据库表结构...")
            await conn.run_sync(Base.metadata.create_all)
            print("✅ 数据库表创建成功!")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        return False

async def verify_tables():
    """验证表是否创建成功"""
    try:
        async with AsyncSessionLocal() as session:
            # 检查主要表是否存在
            tables_to_check = [
                'users', 'novels', 'chapters', 'documents',
                'generation_tasks', 'generation_states', 'prompt_templates'
            ]
            
            print("🔍 验证表结构...")
            for table_name in tables_to_check:
                result = await session.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table_name}'
                    )
                """))
                exists = result.scalar()
                status = "✅" if exists else "❌"
                print(f"  {status} {table_name}")
                
        return True
        
    except Exception as e:
        print(f"❌ 表验证失败: {e}")
        return False

async def initialize_default_data():
    """初始化默认数据"""
    try:
        from app.services.prompt_service import initialize_default_prompts
        
        async with AsyncSessionLocal() as session:
            print("📝 初始化默认提示词模板...")
            success = await initialize_default_prompts(session)
            
            if success:
                print("✅ 默认提示词模板初始化成功!")
            else:
                print("⚠️ 提示词模板初始化过程中出现问题")
                
            return success
            
    except Exception as e:
        print(f"❌ 默认数据初始化失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始异步数据库初始化...")
    print("=" * 50)
    
    try:
        # 步骤1: 创建数据库表
        tables_created = await create_database_tables()
        if not tables_created:
            print("❌ 数据库表创建失败，终止初始化")
            return False
        
        # 步骤2: 验证表结构
        tables_verified = await verify_tables()
        if not tables_verified:
            print("⚠️ 表验证失败，但继续初始化")
        
        # 步骤3: 初始化默认数据
        data_initialized = await initialize_default_data()
        
        print("=" * 50)
        if tables_created and data_initialized:
            print("🎉 数据库初始化完全成功!")
            return True
        elif tables_created:
            print("⚠️ 数据库初始化完成，但有警告")
            return True
        else:
            print("❌ 数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"💥 初始化过程中发生致命错误: {e}")
        return False
    finally:
        # 关闭数据库连接
        await engine.dispose()

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 