#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Celery应用配置
"""
import os
from celery import Celery
from app.core.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "novel_generator",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=['app.tasks.generation_tasks']
)

# 配置Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone=settings.TIMEZONE,
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 结果1小时后过期
)

# 任务路由配置
celery_app.conf.task_routes = {
    'app.tasks.generation_tasks.*': {'queue': 'generation'},
}

# 配置队列
from kombu import Queue, Exchange

celery_app.conf.task_queues = (
    Queue('generation', Exchange('generation'), routing_key='generation'),
    Queue('default', Exchange('default'), routing_key='default'),
)

celery_app.conf.task_default_queue = 'default'
celery_app.conf.task_default_exchange_type = 'direct'
celery_app.conf.task_default_routing_key = 'default' 