# Schemas包 - 数据传输对象
from .auth import WeChatLoginRequest, TokenResponse, UserResponse, UserUpdateRequest
from .novel import NovelCreate, NovelUpdate, NovelResponse, NovelListResponse
from .chapter import ChapterCreate, ChapterUpdate, ChapterResponse, ChapterListResponse
from .generation import (
    ArchitectureGenerationRequest, ArchitectureGenerationResponse,
    ChapterGenerationRequest, ChapterGenerationResponse,
    CharacterGenerationRequest, CharacterGenerationResponse
)
from .task import TaskResponse, TaskListResponse

__all__ = [
    "WeChatLoginRequest", "TokenResponse", "UserResponse", "UserUpdateRequest",
    "NovelCreate", "NovelUpdate", "NovelResponse", "NovelListResponse",
    "ChapterCreate", "ChapterUpdate", "ChapterResponse", "ChapterListResponse",
    "ArchitectureGenerationRequest", "ArchitectureGenerationResponse",
    "ChapterGenerationRequest", "ChapterGenerationResponse",
    "CharacterGenerationRequest", "CharacterGenerationResponse",
    "TaskResponse", "TaskListResponse"
] 