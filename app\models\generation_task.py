#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成任务模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid

from app.core.database import Base


class TaskType(str, enum.Enum):
    """任务类型枚举"""
    GENERATE_ARCHITECTURE = "generate_architecture"  # 生成架构
    GENERATE_BLUEPRINT = "generate_blueprint"  # 生成蓝图
    GENERATE_CHAPTER = "generate_chapter"  # 生成章节
    REGENERATE_PARAGRAPH = "regenerate_paragraph"  # 重新生成段落
    CONSISTENCY_CHECK = "consistency_check"  # 一致性检查
    FINALIZE_CHAPTER = "finalize_chapter"  # 完善章节


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    PENDING = "PENDING"  # 等待中
    IN_PROGRESS = "IN_PROGRESS"  # 进行中
    SUCCESS = "SUCCESS"  # 成功
    FAILED = "FAILED"  # 失败
    CANCELLED = "CANCELLED"  # 已取消


class GenerationTask(Base):
    """异步生成任务表"""
    __tablename__ = "generation_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    task_id = Column(String(255), unique=True, index=True, nullable=False, comment="Celery任务ID")
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    novel_id = Column(UUID(as_uuid=True), ForeignKey("novels.id"), nullable=True, index=True, comment="小说ID")
    
    # 任务信息
    task_type = Column(Enum(TaskType, values_callable=lambda obj: [e.value for e in obj]), nullable=False, comment="任务类型")
    status = Column(Enum(TaskStatus, values_callable=lambda obj: [e.value for e in obj]), default=TaskStatus.PENDING.value, comment="任务状态")
    progress = Column(Integer, default=0, comment="进度百分比(0-100)")
    
    # 任务参数和结果
    parameters = Column(Text, nullable=True, comment="任务参数JSON")
    result_doc_id = Column(UUID(as_uuid=True), nullable=True, comment="结果文档ID")
    result_chapter_id = Column(UUID(as_uuid=True), nullable=True, comment="结果章节ID")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 详细状态信息
    current_stage = Column(String(100), nullable=True, comment="当前执行阶段")
    detailed_progress = Column(Text, nullable=True, comment="详细进度信息JSON")
    result_data = Column(Text, nullable=True, comment="结果数据JSON")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 关系
    user = relationship("User", back_populates="generation_tasks")
    novel = relationship("Novel", back_populates="generation_tasks")

    def __repr__(self):
        return f"<GenerationTask(id={self.id}, type={self.task_type}, status={self.status})>" 