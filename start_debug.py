#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 快速调试启动脚本
同时启动API服务和Celery Worker
"""
import os
import sys
import subprocess
import threading
import time
import signal


class DebugManager:
    """调试管理器 - 同时管理API和Worker进程"""

    def __init__(self):
        self.api_process = None
        self.worker_process = None
        self.running = True

    def log(self, service, message):
        """统一日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        prefix = f"[{service.upper()}]"
        print(f"{timestamp} {prefix} {message}")

    def check_environment(self):
        """检查运行环境"""
        print("检查运行环境...")

        # 检查是否在项目根目录
        if not os.path.exists("app"):
            print("❌ 错误：请在项目根目录运行此脚本")
            return False

        # 检查app/main.py是否存在
        if not os.path.exists("app/main.py"):
            print("❌ 错误：app/main.py 不存在")
            return False

        # 检查app/core/celery_app.py是否存在
        if not os.path.exists("app/core/celery_app.py"):
            print("❌ 错误：app/core/celery_app.py 不存在")
            return False

        print(f"✅ Python版本: {sys.version}")
        print(f"✅ 工作目录: {os.getcwd()}")
        print(f"✅ Python解释器: {sys.executable}")

        return True

    def get_python_executable(self):
        """获取正确的Python解释器路径"""
        # 检测是否在VSCode调试环境中
        is_debug_mode = 'debugpy' in sys.modules or any('debugpy' in str(module) for module in sys.modules.values() if hasattr(module, '__file__') and module.__file__)

        if is_debug_mode:
            self.log('system', "🔍 检测到VSCode调试环境")

        # 优先检查虚拟环境
        venv_path = os.environ.get('VIRTUAL_ENV')
        if venv_path:
            if os.name == 'nt':  # Windows
                venv_python = os.path.join(venv_path, 'Scripts', 'python.exe')
            else:  # Unix/Linux/Mac
                venv_python = os.path.join(venv_path, 'bin', 'python')

            if os.path.exists(venv_python):
                # 规范化路径，确保大小写一致
                venv_python = os.path.abspath(venv_python)
                self.log('system', f"✓ 使用虚拟环境Python: {venv_python}")
                return venv_python

        # 检查项目本地虚拟环境 - 支持多种命名
        current_dir = os.getcwd()
        venv_names = ['novel_ai_venv', 'novel_ai_venv', 'venv', 'env']

        for venv_name in venv_names:
            local_venv = os.path.join(current_dir, venv_name)
            if os.path.exists(local_venv):
                if os.name == 'nt':  # Windows
                    local_python = os.path.join(local_venv, 'Scripts', 'python.exe')
                else:
                    local_python = os.path.join(local_venv, 'bin', 'python')

                if os.path.exists(local_python):
                    # 规范化路径，确保大小写一致
                    local_python = os.path.abspath(local_python)
                    self.log('system', f"✓ 使用本地虚拟环境Python: {local_python}")
                    return local_python

        # 在调试模式下，尝试从当前解释器路径推断虚拟环境
        if is_debug_mode:
            current_python = sys.executable
            # 检查当前解释器是否在虚拟环境中
            if 'novel_ai_venv' in current_python or 'novel_ai_venv' in current_python:
                current_python = os.path.abspath(current_python)
                self.log('system', f"✓ 调试模式使用虚拟环境Python: {current_python}")
                return current_python

        # 最后使用当前Python解释器
        current_python = os.path.abspath(sys.executable)
        self.log('system', f"⚠️ 使用当前Python解释器: {current_python}")
        return current_python

    def start_api_server(self):
        """启动API服务器"""
        self.log('api', "正在启动API服务器...")

        # 设置环境变量
        os.environ.setdefault('PYTHONPATH', os.getcwd())
        # 设置编码环境变量，解决中文编码问题
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            "--host=0.0.0.0",
            "--port=8000",
            "--reload",
            "--log-level=info"
        ]

        self.log('api', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动API服务
            self.api_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='replace'  # 遇到编码错误时替换为?
            )
            self.log('api', f"API服务已启动，PID: {self.api_process.pid}")
            self.log('api', "服务地址: http://localhost:8000")
            self.log('api', "API文档: http://localhost:8000/docs")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.api_process, 'api'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('api', f"启动失败: {e}")
            return False

    def start_celery_worker(self):
        """启动Celery Worker"""
        self.log('worker', "正在启动Celery Worker...")

        # 设置环境变量
        os.environ.setdefault('PYTHONPATH', os.getcwd())
        # 设置编码环境变量，解决中文编码问题
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令 - 简化参数，提高兼容性
        cmd = [
            python_exe, "-m", "celery",
            "-A", "app.core.celery_app:celery_app",
            "worker",
            "--loglevel=info",
            "--pool=solo"
        ]

        self.log('worker', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动Celery Worker
            self.worker_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                encoding='utf-8',
                errors='replace'  # 遇到编码错误时替换为?
            )
            self.log('worker', f"Celery Worker已启动，PID: {self.worker_process.pid}")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.worker_process, 'worker'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('worker', f"启动失败: {e}")
            return False

    def log_output_thread(self, process, service):
        """日志输出线程"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip() and self.running:
                    # 处理编码问题
                    try:
                        clean_line = line.strip()
                        # 过滤掉可能导致编码问题的字符
                        clean_line = ''.join(char for char in clean_line if ord(char) < 65536)
                        if clean_line:
                            self.log(service, clean_line)
                    except UnicodeError:
                        # 如果仍有编码问题，使用安全的方式输出
                        safe_line = line.strip().encode('utf-8', errors='ignore').decode('utf-8')
                        if safe_line:
                            self.log(service, safe_line)
        except Exception as e:
            if self.running:
                self.log(service, f"日志输出异常: {e}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, _frame):
            self.log('system', f"接收到信号 {signum}，正在关闭服务...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        self.log('system', "正在关闭所有服务...")

        # 关闭API服务
        if self.api_process and self.api_process.poll() is None:
            self.log('api', "正在关闭API服务...")
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                self.log('api', "API服务已关闭")
            except subprocess.TimeoutExpired:
                self.log('api', "强制终止API服务...")
                self.api_process.kill()
                self.api_process.wait()

        # 关闭Celery Worker
        if self.worker_process and self.worker_process.poll() is None:
            self.log('worker', "正在关闭Celery Worker...")
            try:
                self.worker_process.terminate()
                self.worker_process.wait(timeout=10)
                self.log('worker', "Celery Worker已关闭")
            except subprocess.TimeoutExpired:
                self.log('worker', "强制终止Celery Worker...")
                self.worker_process.kill()
                self.worker_process.wait()

        self.log('system', "所有服务已关闭")

    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)

            # 检查API进程
            if self.api_process and self.api_process.poll() is not None:
                self.log('api', f"API服务进程已退出，返回码: {self.api_process.returncode}")
                if self.running:
                    self.log('system', "API服务异常退出，关闭系统...")
                    self.shutdown()
                    break

            # 检查Worker进程
            if self.worker_process and self.worker_process.poll() is not None:
                self.log('worker', f"Celery Worker进程已退出，返回码: {self.worker_process.returncode}")
                if self.running:
                    self.log('system', "Celery Worker异常退出，关闭系统...")
                    self.shutdown()
                    break


    def start_system(self):
        """启动整个系统"""
        print("🚀 AI小说生成器 - 快速调试启动")
        print("包含：FastAPI服务 + Celery Worker")
        print("=" * 40)

        # 检查环境
        if not self.check_environment():
            return False

        # 设置信号处理器
        self.setup_signal_handlers()

        # 启动API服务
        self.log('system', "正在启动服务...")
        if not self.start_api_server():
            self.log('system', "API服务启动失败")
            return False

        # 等待API服务启动
        time.sleep(2)

        # 启动Celery Worker
        if not self.start_celery_worker():
            self.log('system', "Celery Worker启动失败")
            self.shutdown()
            return False

        # 等待Worker启动
        time.sleep(2)

        self.log('system', "✅ 所有服务启动完成")
        self.log('system', "")
        self.log('system', "服务状态:")
        self.log('system', "  - API服务: http://localhost:8000")
        self.log('system', "  - Swagger文档: http://localhost:8000/docs")
        self.log('system', "  - Celery Worker: 运行中")
        self.log('system', "")
        self.log('system', "按 Ctrl+C 停止所有服务")
        self.log('system', "=" * 50)

        # 启动进程监控
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()

        # 等待关闭信号
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.log('system', "接收到关闭信号...")
            self.shutdown()

        return True


def main():
    """主函数"""
    manager = DebugManager()
    success = manager.start_system()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()